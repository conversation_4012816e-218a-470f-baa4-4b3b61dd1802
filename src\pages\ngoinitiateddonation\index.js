import React, { useState } from "react";
import BlankLayout from 'src/@core/layouts/BlankLayout';
import { useF<PERSON>, Controller } from "react-hook-form";
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  FormControl,
  InputAdornment,
  MenuItem,
  TextField,
  Typography,
  Select,
  Snackbar,
  Alert,
  Stack,
  InputLabel,
  IconButton,
} from "@mui/material";
import { ArrowBack, VolunteerActivism } from "@mui/icons-material";


const mockDonor = { name: "<PERSON>", email: "<EMAIL>" };
const mockNgos = [
  { value: "1", label: "Helping Hands Foundation" },
  { value: "2", label: "Green Earth Trust" },
];

const buttonStyle = {
  borderRadius: "8px",
  textTransform: "none",
  fontWeight: 600,
  boxShadow: "none",
  height: 38,
  minWidth: 80,
};

const handHeartColor = "#e91e63"; // Creative Hand-Heart Color
const pageBackground = "linear-gradient(135deg, #e0e7ff 0%, #fff 100%)";

const RazorpayKey = "rzp_test_zKgAOeC4Kqzl31";

const loadRazorpayScript = () =>
  new Promise((resolve) => {
    if (window.Razorpay) {
      resolve(true);
      return;
    }
    const script = document.createElement("script");
    script.src = "https://checkout.razorpay.com/v1/checkout.js";
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });

const NGOInitiatedDonation = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [receipt, setReceipt] = useState(null);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [isCustomNgo, setIsCustomNgo] = useState(false);

  const { control, handleSubmit, reset, formState: { errors } } = useForm({
    defaultValues: {
      donorName: mockDonor.name,
      donorEmail: mockDonor.email,
      ngo: "",
      amount: "",
      remarks: "",
    },
  });

  const handleSnackbarClose = () => setOpenSnackbar(false);

  // Step navigation with dialog on back from payment form
  const handleBack = () => {
  if (activeStep > 0) {
    setActiveStep((prev) => prev - 1); // Navigate to the previous step
  }
};

const handleLogout = () => {
  setActiveStep(0); // Reset to the first step
  setReceipt(null); // Clear the receipt
  reset(); // Reset the form
};

  // Razorpay payment
  const openRazorpay = async (data) => {
    const res = await loadRazorpayScript();
    if (!res) {
      alert("Razorpay SDK failed to load. Are you online?");
      setLoading(false);
      return;
    }
    const amountInPaise = Number(data.amount) * 100;
    const options = {
      key: RazorpayKey,
      amount: amountInPaise,
      currency: "INR",
      name: "Pure Heart Donation",
      description: `Donation to ${mockNgos.find(n => n.value === data.ngo)?.label || "your NGO"}`,
      prefill: {
        name: data.donorName,
        email: data.donorEmail,
      },
      handler: function (response) {
        const generatedReceipt = {
          ...data,
          ngo: mockNgos.find(n => n.value === data.ngo)?.label,
          receiptId: Math.floor(Math.random() * 1000000),
          date: new Date().toLocaleString(),
          paymentId: response.razorpay_payment_id,
        };
        setReceipt(generatedReceipt);
        setActiveStep(3);
        setLoading(false);
        setOpenSnackbar(true);
      },
      modal: {
        ondismiss: function () {
          setLoading(false);
        },
      },
    };
    const paymentObject = new window.Razorpay(options);
    paymentObject.open();
  };

  const onSubmit = async (data) => {
    setLoading(true);
    try {
      setTimeout(() => {
        openRazorpay(data);
      }, 400);
    } catch (err) {
      setLoading(false);
    }
  };

  const restartDonation = () => {
    reset();
    setActiveStep(0);
    setReceipt(null);
  };

  return (
    <Box
          sx={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: 'linear-gradient(135deg, #e0e7ff 0%, #fff 100%)'
          }}>
          <Card sx={{
            minWidth: 340,
            maxWidth: 420,
            width: '100%',
            p: { xs: 1, sm: 3 },
            boxShadow: 6,
            borderRadius: 4,
            bgcolor: '#fff',
            position: 'relative'
          }}>
            <CardContent>
              <Stack spacing={2} alignItems="center" sx={{ mb: 2 }}>
                <VolunteerActivism sx={{ fontSize: 40, color: 'primary.main' }} />
                <Typography variant="h4" fontWeight="bold" color="primary" align="center" sx={{ letterSpacing: 1 }}>
                  Donate
                </Typography>
                <Typography variant="subtitle2" color="text.secondary" align="center">
                  Your generosity can change lives!
                </Typography>
              </Stack>
          {loading && (
            <Box sx={{ width: "100%", display: "flex", justifyContent: "center", alignItems: "center", minHeight: 120 }}>
              <CircularProgress />
            </Box>
          )}

          {/* Step 0: Payment Link Sent */}
          {!loading && activeStep === 0 && (
            <Box textAlign="center">
              <Typography variant="h6" sx={{ mb: 2 }}>A payment link has been sent to your email.</Typography>
              <Typography>Email: {mockDonor.email}</Typography>
              <Button variant="contained" sx={{ mt: 3, ...buttonStyle }} onClick={() => setActiveStep(1)}>
                Click Payment Link
              </Button>
            </Box>
          )}
          {/* Step 1: Link Clicked */}
{!loading && activeStep === 1 && (
  <Box textAlign="center">
    <Typography variant="h6" sx={{ mb: 2 }}>You have clicked the payment link.</Typography>
    <Box sx={{ display: "flex", justifyContent: "center", gap: 2, mt: 3 }}>
      <Button onClick={() => setActiveStep(0)}>
        Back
      </Button>
      <Button variant="contained" sx={buttonStyle} onClick={() => setActiveStep(2)}>
        Proceed to Payment
      </Button>
    </Box>
  </Box>
)}
          {/* Step 2: Payment Form */}
          {!loading && activeStep === 2 && (
            <form onSubmit={handleSubmit(onSubmit)}>
              <Stack spacing={2}>
                <Controller
                  name="donorName"
                  control={control}
                  render={({ field }) => (
                    <TextField {...field} size="small" label="Donor Name" fullWidth disabled />
                  )}
                />
                <Controller
                  name="donorEmail"
                  control={control}
                  render={({ field }) => (
                    <TextField {...field} size="small" label="Donor Email" fullWidth disabled />
                  )}
                />
                

<Controller
  name="ngo"
  control={control}
  rules={{ required: "Please select an NGO" }}
  render={({ field }) => (
    <FormControl fullWidth error={!!errors.ngo} size="small">
      <InputLabel>Select NGO</InputLabel>
      <Select
        {...field}
        label="Select NGO"
        displayEmpty
        size="small"
        onChange={(e) => {
          field.onChange(e); // Update the form value
          setIsCustomNgo(e.target.value === "custom"); // Check if "Other" is selected
        }}
      >
        <MenuItem value="" disabled>Select NGO</MenuItem>
        {mockNgos.map((ngo) => (
          <MenuItem key={ngo.value} value={ngo.value}>{ngo.label}</MenuItem>
        ))}
        <MenuItem value="custom">Other</MenuItem>
      </Select>
      {errors.ngo && <Typography color="error" variant="caption">{errors.ngo.message}</Typography>}
    </FormControl>
  )}
/>

{isCustomNgo && (
  <Controller
    name="customNgo"
    control={control}
    rules={{ required: "Please specify the NGO name" }}
    render={({ field }) => (
      <TextField
        {...field}
        size="small"
        label="Custom NGO Name"
        fullWidth
        error={!!errors.customNgo}
        helperText={errors.customNgo?.message}
      />
    )}
  />
)}
                <Controller
                  name="amount"
                  control={control}
                  rules={{
                    required: "Amount is required",
                    min: { value: 1, message: "Amount must be at least ₹1" },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Amount"
                      type="number"
                      fullWidth
                      InputProps={{ startAdornment: <InputAdornment position="start">₹</InputAdornment> }}
                      error={!!errors.amount}
                      helperText={errors.amount?.message}
                    />
                  )}
                />
                <Controller
                  name="remarks"
                  control={control}
                  render={({ field }) => (
                    <TextField {...field} size="small" label="Remarks (optional)" fullWidth />
                  )}
                />
                <Box sx={{ display: "flex", gap: 32, mt: 2 }}>
  <Button
    variant="outlined"
    onClick={handleBack}
    sx={{
      flex: 1, // Keep the "Back" button smaller
      height: 36, // Adjust height to make it smaller
      fontSize: "0.875rem", // Adjust font size
    }}
  >
    Back
  </Button>
  <Button
    variant="contained"
    type="submit"
    sx={{
      ...buttonStyle,
      flex: 5, // Increase the length of the "Complete Payment" button
      height: 36, // Adjust height to make it smaller
      fontSize: "0.875rem", // Adjust font size
    }}
  >
    Complete Payment
  </Button>
</Box>
              </Stack>
            </form>
          )}

          
          {/* Step 3: Receipt */}
          {!loading && activeStep === 3 && receipt && (
            <Box textAlign="center" sx={{ mt: 2 }}>
              <Typography variant="h5" color="success.main" sx={{ mb: 1, fontWeight: 700 }}>Thank you for your donation!</Typography>
              <Typography variant="subtitle1" sx={{ mb: 2, color: "text.secondary" }}>
                Receipt ID: {receipt.receiptId}
              </Typography>
              <Stack spacing={0.5} sx={{ mb: 2 }}>
                <Typography>
                  Date: <b>{receipt.date}</b>
                </Typography>
                <Typography>
                  Donor: <b>{receipt.donorName}</b>
                </Typography>
                <Typography>
                  Email: <b>{receipt.donorEmail}</b>
                </Typography>
                <Typography>
                  NGO: <b>{receipt.ngo}</b>
                </Typography>
                <Typography>
                  Amount: <b>₹{receipt.amount}</b>
                </Typography>
                <Typography>
                  Remarks: <b>{receipt.remarks || "-"}</b>
                </Typography>
              </Stack>
              <Button sx={{ mt: 2 }} variant="outlined" onClick={restartDonation}>
                Make Another Donation
              </Button>
            </Box>
          )}
        </CardContent>
       <Snackbar open={openSnackbar} autoHideDuration={4000} onClose={handleSnackbarClose}>
         <Alert 
           onClose={handleSnackbarClose} 
           severity="success" 
           sx={{ width: '100%', color: '#fff', backgroundColor: 'green' }} // White text and green background
         >
           Receipt generated and sent successfully!
         </Alert>
       </Snackbar>
      </Card>
    </Box>
  );
};

NGOInitiatedDonation.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
NGOInitiatedDonation.guestGuard = true;

export default NGOInitiatedDonation;
