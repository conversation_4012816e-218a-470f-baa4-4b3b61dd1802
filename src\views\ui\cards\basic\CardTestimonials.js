// ** MUI Imports
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'
import Rating from '@mui/material/Rating'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

const data = [
  {
    stats: 'Name1',
    title: 'Designation',
    color: 'primary',
    icon: 'tabler:chart-pie-2',
    description:
      'New way to promote your business that won’t cost you more money, maybe printing is one of the options you won’t resist. Printing is a widely use process in making printed materials'
  },
  {
    color: 'info',
    stats: 'Name2',
    title: 'Designation2',
    icon: 'tabler:users',
    description:
      'New way to promote your business that won’t cost you more money, maybe printing is one of the options you won’t resist. Printing is a widely use process in making printed materials'
  },
  {
    stats: 'Name3',
    color: 'success',
    title: 'Designation3',
    icon: 'tabler:currency-dollar',
    description:
      'New way to promote your business that won’t cost you more money, maybe printing is one of the options you won’t resist. Printing is a widely use process in making printed materials'
  }
]

const renderTestimonials = () => {
  return data.map((testimonial, index) => (
    <>
      <Grid item xs={12} sm={6} md={4} key={index}>
        <Card>
          <CardContent
            key={index}
            sx={{
              p: 5,
              gap: 3,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'start',
              justifyContent: 'center'
            }}
          >
            <Box sx={{ mb: 5, display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>
              <Rating readOnly value={5} name='read-only' sx={{ mr: 2 }} />
            </Box>
            <Typography sx={{ mb: 3, textAlign: 'left' }} variant='body2'>
              {testimonial.description}
            </Typography>
            <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>
              <CustomAvatar skin='light' color={testimonial.color} sx={{ mr: 5, width: 40, height: 40 }}>
                <Icon icon={testimonial.icon} />
              </CustomAvatar>
              <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
                <Typography variant='h6'>{testimonial.stats}</Typography>
                <Typography variant='body2'>{testimonial.title}</Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </>
  ))
}

const CardTestimonials = () => {
  return (
    <Grid container spacing={6}>
      {renderTestimonials()}
    </Grid>
  )
}

export default CardTestimonials
