import {
  Box,
} from "@mui/material";
import Grid from "@mui/material/Grid";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import FallbackSpinner from "src/@core/components/spinner";

const DonorReport = () => {
  const [userList, setUserList] = useState([]);
  const auth = useAuth();
  const formData =[];

  const {
    getAllListValuesByListNameId,
  } = useContext(AuthContext);

  const [searchKeyword, setSearchKeyword] = useState("");
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [loading, setLoading] = useState(true);

  const [zonesData, setZonesData] = useState(null);

  const {
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });

  const handleZoneSuccess = (data) => {
    setZonesData(data?.listValues);
  };

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.allZonesListNameId,
        handleZoneSuccess,
        handleError
      );
    }
  }, [authConfig]);

  const fetchUsers = async (currentPage, currentPageSize, searchKeyword) => {
    // const url = getUrl(authConfig.locationZonesGetAll);

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        const locations = response.data.locationZonesResponse || [];

        setUserList(locations);

        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const columns = [
    {
      field: "name",
      headerName: "Donor Name",
      flex: 0.11,
      minWidth: 120,
    },
    {
      field: "email",
      headerName: "Email",
      flex: 0.11,
      minWidth: 120,
    },
    {
      field: "phone",
      headerName: "Mobile Number",
      flex: 0.11,
      minWidth: 120,
    },
    {
      field: "totalDonation",
      headerName: "Total Donation",
      flex: 0.11,
      minWidth: 120,
    },
    {
      field: "totalAmount",
      headerName: "Total Amount",
      flex: 0.11,
      minWidth: 100,
    },
  ];
  

  return (
    <>
      <Grid>

          
            <div style={{ height: 430, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={userList}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
              )}
            </div>
       
      </Grid>
    </>
  );
};

export default DonorReport;