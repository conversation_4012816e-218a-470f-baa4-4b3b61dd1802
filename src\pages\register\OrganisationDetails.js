import {
  Alert,
  Box,
  Button,
  CircularProgress,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputAdornment,
  Snackbar,
  TextField,
  Typography,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
// ** Layout Import
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import { getUrl } from "src/helpers/utils";

import { useRouter } from "next/router";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import GoogleMapsIconButton from "src/@core/components/custom-components/toastDisplay";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import FallbackSpinner from "src/@core/components/spinner";

const OrganisationDetails = ({ email, setShowPwd }) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const { fetchProfile, getAllListValuesByListNameId,setPageLoad } =
    useContext(AuthContext);

  const router = useRouter();
  const { role } = router.query;

  const [companyType, setCompanyType] = useState("");

  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");
  const [companyTypeOptions, setCompanyTypeOptions] = useState([]);

  const getPasswordStrength = () => {
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isValidLength = password.length >= 8;

    const conditionsMet = [
      hasUpperCase,
      hasLowerCase,
      hasNumber,
      hasSpecialChar,
    ].filter(Boolean).length;

    if (!isValidLength || conditionsMet < 2) return "Poor";
    if (conditionsMet === 3) return "Moderate";
    if (conditionsMet === 4 && password.length >= 8) return "Strong";
    return "Moderate";
  };

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };
  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.companyType,
        (data) =>
          setCompanyTypeOptions(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  async function submit(data) {
    setLoading(true);
    const ipAddress = await fetchIpAddress();
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isValidLength = password.length >= 8;

    if (
      !hasUpperCase ||
      !hasLowerCase ||
      !hasNumber ||
      !hasSpecialChar ||
      !isValidLength
    ) {
      setToastMessage(
        "Password must be greater than 7 characters with uppercase, lowercase, number, and special character."
      );
      setShowToast(true);
      setLoading(false);
      return; // Exit early if password is weak
    }

    let updatedData = {};
    if (role === "society") {
      updatedData = {
        ...data,
        email: email,
        oragnisationType: "SOCIETY",
        ipAddress: ipAddress,
        password: password,
      };
    } else {
      updatedData = {
        ...data,
        email: email,
        oragnisationType: "SERVICE_PROVIDER",
        ipAddress: ipAddress,
        password: password,
        companyType: companyType,
      };
    }

    let response;
    try {
      response = await axios({
        method: "POST",
        url: getUrl(authConfig.signUpEndpoint),
        data: updatedData,
      });
      setPageLoad(true);
      // Extract tokens
      const accessToken = response.data.token.accessToken;
      const refreshToken = response.data.token.refreshToken;

      // Store tokens in localStorage
      window.localStorage.setItem(authConfig.storageTokenKeyName, accessToken);
      window.localStorage.setItem(authConfig.refreshTokenKeyName, refreshToken);

      // Fetch profile with the access token
      await fetchProfile(accessToken);

      // Navigate to dashboard
      router.replace("/dashboard").then(() => router.reload());
    } catch (error) {
      console.error("Error verifying OTP:", error);
      if (error?.response?.status === 400) {
        setToastMessage(error?.response?.data?.message);
      } else {
        setToastMessage("Failed to register. Please try again later.");
      }
      setShowToast(true);
    } finally {
      setLoading(false);
    }
  }

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  const validateLocationUrl = (value) => {
    const urlPattern =
      /^https:\/\/(www\.)?(maps\.app\.goo\.gl|google\.com\/maps)\/.+$/;
    if (!urlPattern.test(value)) {
      return "Please enter a valid Google Maps URL";
    }
    try {
      const url = new URL(value);
      if (url.hostname !== "maps.app.goo.gl" && url.hostname !== "google.com") {
        return "Please enter a valid Google Maps URL";
      }
    } catch (error) {
      return "Invalid URL format";
    }
    return true;
  };

  const handlePrev = () => {
    setShowPwd(false);
  };

  return (
    <>
      <Container
        maxWidth="xs"
        sx={{
          height: "90vh",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "start",
          boxShadow: 3,
          p: 8,
          mt: 8,
          mb: 8,
          borderRadius: 6,
          bgcolor: "white",
          position: "relative",
          overflowY: "auto",
          flexGrow: 1,
        }}
      >
        <Box
          sx={{
            width: "80%",
            position: "absolute",
            mr: 8,
            "&:hover": {
              cursor: "pointer",
            },
          }}
        >
          <Icon
            icon="ooui:arrow-previous-ltr"
            fontSize={20}
            onClick={handlePrev}
          />
        </Box>
        <Box>
          <Typography variant="body2" sx={{ my: 2 }}>
            {""}
          </Typography>

          <Grid
            container
            spacing={3}
            sx={{
              marginTop: 8,
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="First Name*"
                    placeholder="Enter First Name"
                    fullWidth
                    size="small"
                    onChange={(e) => {
                      const newValue = e.target.value.replace(/\s/g, " ");
                      field.onChange(newValue);
                    }}
                    id="first-name"
                    error={Boolean(errors.firstName)}
                    helperText={errors.firstName?.message}
                    InputLabelProps={{
                      shrink: true,
                      sx: { fontSize: "1rem" },
                    }}
                    sx={{
                      borderRadius: "5px",
                      background: "white",
                      width: "100%",
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Last Name"
                    placeholder="Enter Last Name"
                    fullWidth
                    size="small"
                    onChange={(e) => {
                      const newValue = e.target.value.replace(/\s/g, " ");
                      field.onChange(newValue);
                    }}
                    id="last-name"
                    error={Boolean(errors.lastName)}
                    helperText={errors.lastName?.message}
                    InputLabelProps={{
                      shrink: true,
                      sx: { fontSize: "1rem" },
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
              <Controller
                name="organisationName"
                control={control}
                rules={{ required: "Organisation Name is required" }}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label={
                      role === "service-provider"
                        ? "Company Name*"
                        : "Society Name*"
                    }
                    placeholder={
                      role === "service-provider"
                        ? "Enter Company Name"
                        : "Enter Society Name"
                    }
                    fullWidth
                    size="small"
                    onChange={(e) => {
                      const newValue = e.target.value.replace(/\s/g, " ");
                      field.onChange(newValue);
                    }}
                    id="organisation-name"
                    error={Boolean(errors.organisationName)}
                    helperText={errors.organisationName?.message}
                    InputLabelProps={{
                      shrink: true,
                      sx: { fontSize: "1rem" },
                    }}
                  />
                )}
              />
            </Grid>

            {role === "service-provider" && (
              <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
                <FormControl fullWidth error={Boolean(errors.companyType)}>
                  <SelectAutoComplete
                    register={() =>
                      register("companyType", {
                        required: "This field is required",
                      })
                    }
                    id={"companyType"}
                    label={"Registration Type*"}
                    name="companyType"
                    nameArray={companyTypeOptions}
                    value={companyType}
                    onChange={(e) => setCompanyType(e.target.value)}
                    error={Boolean(errors.companyType)}
                    aria-describedby="validation-companyType"
                  />
                  {errors.companyType && (
                    <FormHelperText
                      sx={{ color: "error.main" }}
                      id="validation-companyType"
                    >
                      {errors.companyType.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            )}
            {role === "society" && (
              <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
                <FormControl fullWidth>
                  <Controller
                    name="googleLocation"
                    control={control}
                    rules={{
                      validate: validateLocationUrl,
                    }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Google location"
                        InputLabelProps={{
                          shrink: true,
                          sx: { fontSize: "1rem" },
                        }}
                        size="small"
                        placeholder="Click on icon to navigate & paste URL here"
                        error={Boolean(errors.googleLocation)}
                        helperText={errors.googleLocation?.message}
                        aria-describedby="validation-basic-googleLocation"
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <span
                                style={{
                                  position: "absolute",
                                  right: 8,
                                  top: 0,
                                }}
                              >
                                <GoogleMapsIconButton />
                              </span>
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            )}
            <Grid
              item
              xs={8.5}
              sx={{
                marginBottom: 2,
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography sx={{ fontSize: "0.9rem" }}>Email :</Typography>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  ml: 1,
                }}
              >
                <Typography
                  sx={{
                    fontWeight: "bold",
                    fontSize: "0.9rem",
                    wordBreak: "break-word",
                  }}
                >
                  {email}
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Password*"
                    placeholder="Enter Password"
                    type={showPassword ? "text" : "password"}
                    fullWidth
                    size="small"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    id="last-name"
                    error={Boolean(errors.password)}
                    helperText={errors.password?.message}
                    InputLabelProps={{
                      shrink: true,
                      sx: { fontSize: "1rem" },
                    }}
                    onCut={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onCopy={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            onMouseDown={(e) => e.preventDefault()}
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            <Icon
                              icon={
                                showPassword ? "tabler:eye" : "tabler:eye-off"
                              }
                              fontSize={{ xs: 5, lg: 20 }}
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
              {password?.length > 0 && (
                <Typography
                  color={
                    getPasswordStrength() === "Poor"
                      ? "error"
                      : getPasswordStrength() === "Moderate"
                      ? "orange"
                      : "primary"
                  }
                >
                  {getPasswordStrength()}
                </Typography>
              )}
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            width: "80%",
            py: 2,
            textAlign: "center",
            mt: "auto",
          }}
        >
          <>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 4, width: "90%" }}
              onClick={handleSubmit(submit)}
            >
              {loading ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Sign Up"
              )}
            </Button>
          </>
        </Box>
      </Container>
      <Snackbar
        open={showToast}
        autoHideDuration={5000} // Toast will be visible for 5 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity="error"
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "12px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default OrganisationDetails;
