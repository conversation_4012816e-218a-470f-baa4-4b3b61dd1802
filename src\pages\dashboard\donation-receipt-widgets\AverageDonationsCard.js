import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  CircularProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import TrendingUpOutlinedIcon from '@mui/icons-material/TrendingUpOutlined';

const AverageDonationsCard = ({ value, loading = false, currencySymbol = '₹' }) => {
  const theme = useTheme();
  const cardColor = '#1F1F7A';

  const displayValue = value !== null && value !== undefined
    ? `${currencySymbol} ${Number(value).toFixed(2)}`
    : '-';

  return (
    <Card
      sx={{
        position: 'relative',
        overflow: 'visible',
        borderLeft: `5px solid ${cardColor}`,
        borderRadius: '8px'
      }}
      elevation={2}
    >
      <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
        <Box>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 0.5 }}>
            Average Donation
          </Typography>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            {loading ? <CircularProgress size={20} /> : displayValue}
          </Typography>
        </Box>
        <Avatar
          sx={{
            bgcolor: `${cardColor}20`,
            color: cardColor,
            width: 40,
            height: 40
          }}
        >
          <TrendingUpOutlinedIcon />
        </Avatar>
      </CardContent>
    </Card>
  );
};

export default AverageDonationsCard;
