import { Menu, MenuItem, Tooltip } from "@mui/material";
import CustomChip from "src/@core/components/mui/chip";
import CustomAvatar from "src/@core/components/mui/avatar";
import Icon from "src/@core/components/icon";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";
import { useRBAC } from "../permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const mapIsActiveToLabel = (isActive) => {
  return userStatusObj[isActive] || "Unknown";
};

const useColumns = ({
  menu,
  setMenu,
  currentRow,
  setCurrentRow,
  setOpenDialog,
  setOpenDeleteDialog,
  handleCloseMenuItems,
  tenantsList,
}) => {
  const { canMenuPageSectionField } = useRBAC();
  const canAccessHeads = (requiredPermission, field) =>
    canMenuPageSectionField(
      MENUS.LEFT,
      PAGES.DONATION_HEAD,
      "Donation_Head_DataGrid_Table",
      field,
      requiredPermission
    );
  const { donationHeadId, setDonationHeadId,user } = useContext(AuthContext);

  return [
    {
      field: "name",
      headerName: "Name",
      flex: 0.13,
      minWidth: 120,
    },
    user?.organisationCategory === "SUPER_ADMIN"
    ?{
      field: "orgId",
      headerName: "NGO Name",
      flex: 0.13,
      minWidth: 120,
      renderCell: (params) => {
        const org = tenantsList?.find(
          (item) => item?.value === params?.row?.orgId
        );
        return (
          <Tooltip title={org ? org?.key : ""}>
            <span>{org ? org?.key : ""}</span>
          </Tooltip>
        );
      },
    } : null,
    {
      field: "description",
      headerName: "Description",
      flex: 0.13,
      minWidth: 120,
    },
    {
      field: "createdOn",
      headerName: "Created On",
      flex: 0.07,
      minWidth: 120,
      valueFormatter: (params) => params.value?.split("T")[0],
    },
    {
      field: "updatedOn",
      headerName: "Updated On",
      flex: 0.07,
      minWidth: 120,
      valueFormatter: (params) => params.value?.split("T")[0],
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.07,
      minWidth: 100,
      renderCell: ({ row }) => (
        <CustomChip
          rounded
          skin="light"
          size="small"
          label={mapIsActiveToLabel(row.isActive)}
          color={row.isActive ? "success" : "error"}
          sx={{ textTransform: "capitalize" }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.05,
      minWidth: 50,
      sortable: false,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          const row = params.row;
          setCurrentRow(row);
          setDonationHeadId({
            ...donationHeadId,
            id: params.row.id,
          });
        };

        const onClickViewProfile = () => {
          setOpenDialog(true);
          handleCloseMenuItems();
        };

        const onClickToggleStatus = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems(); // Close menu after action
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              id="actions-menu"
              anchorEl={menu}
              open={Boolean(menu)}
              onClose={handleCloseMenuItems}
            >
              {canAccessHeads(PERMISSIONS.FULL_ACCESS, "Edit") && (
                <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              )}
              {canAccessHeads(
                PERMISSIONS.FULL_ACCESS,
                "Activate_Or_Deactivate"
              ) && (
                <MenuItem onClick={onClickToggleStatus}>
                  {currentRow?.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ].filter(Boolean);
};

export default useColumns;
