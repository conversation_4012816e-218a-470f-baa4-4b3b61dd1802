// ** Next Import

// ** MUI Components
import Box from "@mui/material/Box";
import { styled } from "@mui/material/styles";
import Typography from "@mui/material/Typography";
import { useRBAC} from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import { useEffect } from "react";
// ** Layout Import


// ** Demo Imports
import FooterIllustrations from "src/views/pages/misc/FooterIllustrations";

// ** Styled Components
const BoxWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.down("md")]: {
    width: "90vw",
  },
}));

const SiteMap = () => {



  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessSiteMap = (requiredPermission) =>
    canMenuPageSection(MENUS.TOP, PAGES.USER_MANAGEMENT, PAGES.SITE_MAP , requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessSiteMap(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if(canAccessSiteMap(PERMISSIONS.READ)) {
  return (
    <>
      <Box className="content-center">
        <Box
          sx={{
            p: 5,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            textAlign: "center",
          }}
        >
          <BoxWrapper>
            <Typography
              sx={{ mb: 6, color: "text.secondary", fontSize: "2.5rem" }}
            >
              Work in progress
            </Typography>
          </BoxWrapper>
        </Box>
      </Box>
    </>
  );
}
else {
  return null;
}
};

export default SiteMap;
