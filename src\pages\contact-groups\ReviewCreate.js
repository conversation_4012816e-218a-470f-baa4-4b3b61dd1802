import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  IconButton,
  Link,
  Typography,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import DeleteIcon from "@mui/icons-material/Delete";
import toast from "react-hot-toast";

const ReviewCreate = ({ formValues, selectedUsers, setSelectedUsers }) => {
  // Remove a donor from the final list
  const handleRemoveDonor = (donorId) => {
    setSelectedUsers(selectedUsers.filter((donor) => donor.id !== donorId));
    toast.success("Donor removed from the final list");
  };

  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  // Define columns for the donor table
  const columns = [
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.3,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "contactNumber",
      headerName: "Mobile Number",
      flex: 0.15,
      minWidth: 100,
      renderCell: (params) => {
        const contactNumber = params && params.value ? params.value : "";

        if (!contactNumber) return null;

        return (
          <Link
            href={`tel:${contactNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {contactNumber}
          </Link>
        );
      },
    },
    {
      field: "panNo",
      minWidth: 100,
      headerName: "Pan No",
      flex: 0.15,
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.1,
      minWidth: 80,
      sortable: false,
      renderCell: (params) => (
        <IconButton
          color="error"
          onClick={() => handleRemoveDonor(params.row.id)}
          size="small"
        >
          <DeleteIcon fontSize="small" />
        </IconButton>
      ),
    },
  ];

  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <Typography variant="h6" sx={{ mb: 4 }}>
        Review Group Details
      </Typography>

      {/* Group Details Summary */}
      <Card sx={{ p: 3, mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
          Group Information
        </Typography>

        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          <Box sx={{ display: "flex" }}>
            <Typography
              variant="body2"
              sx={{ width: "150px", fontWeight: 500 }}
            >
              Group Name:
            </Typography>
            <Typography variant="body2">
              {formValues?.contactGroupName || "Not specified"}
            </Typography>
          </Box>

          <Box sx={{ display: "flex" }}>
            <Typography
              variant="body2"
              sx={{ width: "150px", fontWeight: 500 }}
            >
              Description:
            </Typography>
            <Typography variant="body2">
              {formValues?.description || "Not specified"}
            </Typography>
          </Box>
        </Box>
      </Card>

      <Typography variant="subtitle1" sx={{ mb: 2 }}>
        Final Donor List ({selectedUsers.length})
      </Typography>

      {selectedUsers.length > 0 ? (
        <Card sx={{ flex: 1 }}>
          <DataGrid
            autoHeight
            rows={selectedUsers}
            columns={columns}
            pagination
            rowsPerPageOptions={rowsPerPageOptions}
            pageSize={pageSize}
            page={page - 1}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
            disableSelectionOnClick
            sx={{
              "& .MuiDataGrid-columnHeaders": { borderRadius: 0 },
              "& .MuiDataGrid-cell:focus-within": { outline: "none" },
            }}
            rowHeight={38}
            headerHeight={38}
          />
        </Card>
      ) : (
        <Box
          sx={{
            p: 4,
            textAlign: "center",
            border: "1px dashed",
            borderColor: "divider",
            borderRadius: 1,
          }}
        >
          <Typography color="text.secondary">
            No donors have been added to the final list.
            <br />
            Please go back to the previous step to add donors
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ReviewCreate;
