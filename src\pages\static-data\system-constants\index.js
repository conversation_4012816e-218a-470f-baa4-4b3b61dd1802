import NavTabEmployees from "src/@core/components/custom-components/NavTabEmployees";
import States from "./States";
import PaymentMode from "./PaymentMode";
import DonationType from "./DonationType";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import { useRBAC } from "src/pages/permission/RBACContext";
const SystemConstants = () => {
  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessSystemConstants = (requiredPermission) =>
    canMenuPageSection(
      MENUS.TOP,
      PAGES.STATIC_DATA,
      PAGES.SYSTEM_CONSTANTS,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessSystemConstants(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessSystemConstants(PERMISSIONS.READ)) {
    return (
      <div>
        <NavTabEmployees
          tabContent1={
            <>
              <States />
            </>
          }
          tabContent2={
            <>
              <DonationType />
            </>
          }
          tabContent3={
            <>
              <PaymentMode />
            </>
          }
        />
      </div>
    );
  } else {
    return null;
  }
};

export default SystemConstants;
