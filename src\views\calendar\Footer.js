import React from 'react';
import { Button, Divider } from '@mui/material';
import {useForm } from "react-hook-form";


const Footer = ({ onSubmit, onReset, isUpdate }) => {

  const {
    control,
    setValue,
    clearErrors,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();


  return (
    <>
      <Button type="submit" variant="contained" onClick={handleSubmit(onSubmit)} sx={{ mr: 4 }}>
        {isUpdate ? 'Update' : 'Add'}
      </Button>
      <Button variant="outlined" color="secondary" onClick={onReset}>
        Reset
      </Button>
    </>
  );
};

export default Footer;
