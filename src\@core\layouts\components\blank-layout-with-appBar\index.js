// ** Next Import
import Link from 'next/link'

// ** Next Import
import { useRouter } from 'next/router'

// ** MUI Imports
import AppBar from '@mui/material/AppBar'
import Box from '@mui/material/Box'
import Toolbar from '@mui/material/Toolbar'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import { styled, useTheme } from '@mui/material/styles'

// ** Configs
import themeConfig from 'src/configs/themeConfig'

import { useAuth } from 'src/hooks/useAuth'

// ** Hook
import { useSettings } from 'src/@core/hooks/useSettings'
import ProfessionalMenu from './ProfessionalMenu'

const LinkStyled = styled(Link)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  textDecoration: 'none',
  color: '#444',
  marginRight: theme.spacing(8)
}))

const BlankLayoutAppBar = () => {
  // ** Hooks & Vars
  const theme = useTheme()
  const { settings } = useSettings()
  const { skin } = settings

  const router = useRouter()
  const { logout } = useAuth()

  const handleLogout = () => {
    logout()
  }

  return (
    <AppBar
      color='default'
      position='sticky'
      elevation={skin === 'bordered' ? 0 : 3}
      sx={{
        backgroundColor: 'background.paper',
        ...(skin === 'bordered' && { borderBottom: `1px solid ${theme.palette.divider}` })
      }}
    >
      <Toolbar
        sx={{
          alignItems: 'center',
          justifyContent: 'space-between',
          p: theme => `${theme.spacing(0, 6)} !important`,
          minHeight: `${theme.mixins.toolbar.minHeight - (skin === 'bordered' ? 1 : 0)}px !important`
        }}
      >
        <LinkStyled href='/'>
          <img width={32} height={30} alt='' src='/images/logo.webp' className=''></img>
          <Typography
            variant='h6'
            sx={{
              ml: 2.5,
              fontWeight: 600,
              lineHeight: '24px',
              fontSize: '1.375rem !important'
            }}
          >
            {themeConfig.templateName}
          </Typography>
        </LinkStyled>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <ProfessionalMenu />
          <LinkStyled href='/readiness'>Readiness</LinkStyled>
          <LinkStyled href='/'>Resources</LinkStyled>
          <LinkStyled href='/'>Calculator</LinkStyled>
        </Box>
        <Button onClick={handleLogout}  variant='contained'>
          Login
        </Button>
      </Toolbar>
    </AppBar>
  )
}

export default BlankLayoutAppBar
