import React from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import Icon from 'src/@core/components/icon';

const Header = ({ title, onClose,showCloseButton = true }) => {
  return (
    <Box
      className="dialog-header"
      sx={{
        display: 'flex',
        justifyContent: 'space-between'
      }}
    >
      <Typography variant="h6">{title}</Typography>
      {showCloseButton && (

      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <IconButton size="small" onClick={onClose} sx={{ color: 'text.primary' }}>
          <Icon icon="tabler:x" fontSize="1.25rem" />
        </IconButton>
      </Box>
            )}

    </Box>
  );
};

export default Header;
