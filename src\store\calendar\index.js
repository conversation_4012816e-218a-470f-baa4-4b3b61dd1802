// ** Redux Imports
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

// ** Axios Imports
import axios from 'axios'
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
// ** Fetch Events
export const fetchEvents = createAsyncThunk('appCalendar/fetchEvents', async () => {
  const url = getUrl(`${authConfig.eventEndpoint}`) + '/all';

  try {
    const response = await axios.post(url, {
      headers: getAuthorizationHeaders(),
    });

    const events = response?.data.map(data => ({
      id: data?.id,
      title: data?.title || "",
      start: `${data?.startDate}T${data?.startTime}`,
      end: `${data?.endDate}T${data?.endTime}`,
      allDay: data?.allDay,
      display: data?.display || "block",
      url: data?.url,
      extendedProps: {
        attendees: data?.attendees ? data.attendees.map((attendee) => attendee.value) : [],
        description:data?.description,
        googleMeetOrLocationUrl: data?.googleMeetOrLocationUrl,
        isActive: data?.isActive,
        inPersonEvent: data?.inPersonEvent,
        searchForRoom: data?.searchForRoom,
        googleMeet: data?.googleMeet
      }
    }));


    return events;
  } catch (error) {
    console.error("Error fetching events:", error);
    throw error; 
  }
});

export const fetchEventsAdmin = createAsyncThunk('appCalendar/fetchEvents', async () => {
  const url = getUrl(`${authConfig.eventEndpoint}`) + '/admin-all';

  try {
    const response = await axios.post(url, {
      headers: getAuthorizationHeaders(),
    });

    const events = response?.data.map(data => ({
      id: data?.id,
      title: data?.title || "",
      start: `${data?.startDate}T${data?.startTime}`,
      end: `${data?.endDate}T${data?.endTime}`,
      allDay: data?.allDay,
      display: data?.display || "block",
      url: data?.url,
      extendedProps: {
        attendees: data?.attendees ? data.attendees.map((attendee) => attendee.value) : [],
        description:data?.description,
        googleMeetOrLocationUrl: data?.googleMeetOrLocationUrl,
        isActive: data?.isActive,
        inPersonEvent: data?.inPersonEvent,
        searchForRoom: data?.searchForRoom,
        googleMeet: data?.googleMeet
      }
    }));


    return events;
  } catch (error) {
    console.error("Error fetching events:", error);
    throw error; 
  }
});

export const fetchEventsAll = createAsyncThunk('appCalendar/fetchEvents', async ({ fromDate,toDate,employee }) => {
  const url = getUrl(`${authConfig.eventEndpoint}`) + '/all';
  const data = {
    fromDate:fromDate,
    toDate:toDate,
    assignedToEmployeeId:employee || null
  }
  try {
    const response = await axios.post(url, data, {
      headers: getAuthorizationHeaders(),
    });

    const events = response?.data.map(data => ({
      id: data?.id,
      title: data?.title || "",
      start: `${data?.startDate}T${data?.startTime}`,
      end: `${data?.endDate}T${data?.endTime}`,
      allDay: data?.allDay,
      display: data?.display || "block",
      url: data?.url,
      extendedProps: {
        attendees: data?.attendees ? data.attendees.map((attendee) => attendee.value) : [],
        description:data?.description,
        googleMeetOrLocationUrl: data?.googleMeetOrLocationUrl,
        isActive: data?.isActive,
        inPersonEvent: data?.inPersonEvent,
        searchForRoom: data?.searchForRoom,
        googleMeet: data?.googleMeet
      }
    }));


    return events;
  } catch (error) {
    console.error("Error fetching events:", error);
    throw error; 
  }
});

export const fetchEventsAdminAll = createAsyncThunk('appCalendar/fetchEvents', async ({ fromDate,toDate,employee }) => {
  const url = getUrl(`${authConfig.eventEndpoint}`) + '/admin-all';
  const data = {
    fromDate:fromDate,
    toDate:toDate,
    assignedToEmployeeId:employee || null
  }
  try {
    const response = await axios.post(url, data, {
      headers: getAuthorizationHeaders(),
    });

    const events = response?.data.map(data => ({
      id: data?.id,
      title: data?.title || "",
      start: `${data?.startDate}T${data?.startTime}`,
      end: `${data?.endDate}T${data?.endTime}`,
      allDay: data?.allDay,
      display: data?.display || "block",
      url: data?.url,
      extendedProps: {
        attendees: data?.attendees ? data.attendees.map((attendee) => attendee.value) : [],
        description:data?.description,
        googleMeetOrLocationUrl: data?.googleMeetOrLocationUrl,
        isActive: data?.isActive,
        inPersonEvent: data?.inPersonEvent,
        searchForRoom: data?.searchForRoom,
        googleMeet: data?.googleMeet
      }
    }));


    return events;
  } catch (error) {
    console.error("Error fetching events:", error);
    throw error; 
  }
});


// ** Add Event
export const addEvent = createAsyncThunk('appCalendar/addEvent', async (event, { dispatch }) => {
  const url = getUrl(authConfig.eventEndpoint);

  const response = await axios.post(
    url,
    event,
     {headers: getAuthorizationHeaders(),}
  );

  // Dispatch fetchEvents
  await dispatch(fetchEvents());
  return response.data.event
})

// ** Update Event
export const updateEvent = createAsyncThunk('appCalendar/updateEvent', async (event, { dispatch }) => {
  const url = getUrl(authConfig.eventEndpoint) + "/" + event.id;
  const response = await axios.patch(
    url,
    
     event
    ,
    {
      headers: getAuthorizationHeaders(),
    }
  );
  await dispatch(fetchEvents())

  return response.data.event
})

// ** Delete Event
export const deleteEvent = createAsyncThunk('appCalendar/deleteEvent', async (id, { dispatch }) => {
  const url = `${getUrl(authConfig.eventEndpoint)}/delete/${id}`;
  try {
    const response = await axios.patch(url, {
      headers: getAuthorizationHeaders(),
    });
  } catch (error) {
    console.error('Error deleting event:', error.response ? error.response.data : error.message);
  }
  await dispatch(fetchEvents())

  return response.data
})

// ** Fetch Event By Id
dayjs.extend(customParseFormat); // Enable custom time parsing

export const fetchEventById = createAsyncThunk('appCalendar/fetchEventById', async (id) => {
  const url = getUrl(`${authConfig.eventEndpoint}/${id}`);

  try {
    const response = await axios.get(url, {
      headers: getAuthorizationHeaders(),
    });

    const adjustTime = (time) => {
      if (!time) return null; // Handle cases where time is null or undefined
      
      // Try parsing the time in multiple formats
      let parsedTime = dayjs(time, ["HH:mm", "hh:mm A", "YYYY-MM-DDTHH:mm:ssZ"], true);
      
      if (!parsedTime.isValid()) {
        console.warn(`Invalid time format received: ${time}`);
        return time; // Return the original value if parsing fails
      }

      return parsedTime.subtract(5, "hour").subtract(30, "minute").format("HH:mm");
    };

    const eventData = {
      id: response?.data?.id,
      title: response?.data?.title || "",
      startDate: response?.data?.startDate,
      endDate: response?.data?.endDate,
      startTime: adjustTime(response?.data?.startTime),
      endTime: adjustTime(response?.data?.endTime),
      allDay: response?.data?.allDay,
      display: response?.data?.display || "block",
      url: response?.data?.url,
      extendedProps: {
        attendees: response?.data?.attendees || [],
        description: response?.data?.description,
        googleMeetOrLocationUrl: response?.data?.googleMeetOrLocationUrl,
        isActive: response?.data?.isActive,
        inPersonEvent: response?.data?.inPersonEvent,
        searchForRoom: response?.data?.searchForRoom,
        googleMeet: response?.data?.googleMeet
      },
      createdBy: response?.data?.createdBy,
      updatedBy: response?.data?.updatedBy,
      createdOn: response?.data?.createdOn,
      updatedOn: response?.data?.updatedOn
    };


    return eventData;
  } catch (error) {
    console.error("Error fetching event by ID:", error);
    throw error;
  }
});


export const appCalendarSlice = createSlice({
  name: 'appCalendar',
  initialState: {
    events: [],
    selectedEvent: null
  },
  reducers: {
    handleSelectEvent: (state, action) => {
      state.selectedEvent = action.payload
    }
  },
  extraReducers: builder => {
    builder.addCase(fetchEvents.fulfilled, (state, action) => {
      state.events = action.payload
    })
  }
})

export const { handleSelectEvent } = appCalendarSlice.actions

export default appCalendarSlice.reducer