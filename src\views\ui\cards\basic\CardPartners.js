// ** MUI Imports
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'
import Rating from '@mui/material/Rating'

import { styled } from '@mui/material/styles'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

const data = [
  {
    title: 'Title',
    src: '/images/svg/aon.svg'
  },
  {
    title: 'Title2',
    src: '/images/svg/aspnetzero.svg'
  },
  {
    title: 'Title3',
    src: '/images/svg/atica.svg'
  },
  {
    title: 'Title4',
    src: '/images/svg/nasa.svg'
  }
]

const Img = styled('img')(({ theme }) => ({
  [theme.breakpoints.up('md')]: {
    height: 75
  }
}))

const renderPartners = () => {
  return data.map((partner, index) => (
    <>
      <Grid item xs={12} sm={6} md={3} key={index}>
        <Card>
          <CardContent
            key={index}
            sx={{
              p: 5,
              gap: 3,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Img height='75' width='75' alt='redevelopment-project' src={partner.src} />

            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
              <Typography variant='h6'>{partner.title}</Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </>
  ))
}

const CardPartners = () => {
  return (
    <Grid container spacing={6}>
      {renderPartners()}
    </Grid>
  )
}

export default CardPartners
