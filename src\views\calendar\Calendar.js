import { useEffect, useRef } from 'react'
import FullCalendar from '@fullcalendar/react'
import listPlugin from '@fullcalendar/list'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import bootstrap5Plugin from '@fullcalendar/bootstrap5'
import interactionPlugin from '@fullcalendar/interaction'
import CalendarWrapper from 'src/@core/styles/libs/fullcalendar'
import 'bootstrap-icons/font/bootstrap-icons.css'

const blankEvent = {
  title: '',
  start: '',
  end: '',
  allDay: false,
  url: '',
  extendedProps: {
    calendar: '',
    guests: [],
    location: '',
    description: ''
  }
}

// Helper function to get all dates between two dates
const getAllDatesBetween = (start, end) => {
  const dates = [];
  let currentDate = new Date(start);
  const endDate = new Date(end);

  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  return dates;
};

// Preprocess events to include all dates between start and end
const preprocessEvents = events => {
  const expandedEvents = [];

  events.forEach(event => {
    const allDates = getAllDatesBetween(event.start, event.end);

    allDates.forEach((date, index) => {
      expandedEvents.push({
        ...event,
        id: `${event.id}-${index}`, // Generate a unique ID for rendering
        start: date.toISOString().split('T')[0], // Set start to current date
        end: date.toISOString().split('T')[0],   // Set end to current date
        allDay: true,                            // Ensure it spans all day
        title: event.title,                      // Retain the original title
        originalId: event.id                     // Keep the original event ID
      });
    });
  });

  return expandedEvents;
};

const Calendar = props => {
  // ** Props
  const {
    store,
    dispatch,
    direction,
    updateEvent,
    calendarApi,
    calendarsColor,
    setCalendarApi,
    handleSelectEvent,
    handleLeftSidebarToggle,
    handleAddEventSidebarToggle
  } = props;

  // ** Refs
  const calendarRef = useRef();
  useEffect(() => {
    if (calendarApi === null) {
      // @ts-ignore
      setCalendarApi(calendarRef.current?.getApi());
    }
  }, [calendarApi, setCalendarApi]);

  if (store) {
    // Preprocess events
    const processedEvents = preprocessEvents(store.events);

    // ** calendarOptions(Props)
    const calendarOptions = {
      events: processedEvents, // Pass preprocessed events
      plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin, bootstrap5Plugin],
      initialView: 'dayGridMonth',
      headerToolbar: {
        start: 'sidebarToggle, prev, next, title',
        end: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth'
      },
      views: {
        week: {
          titleFormat: { year: 'numeric', month: 'long', day: 'numeric' }
        }
      },
      editable: true,
      eventResizableFromStart: true,
      dragScroll: true,
      dayMaxEvents: 2,
      navLinks: true,
      eventClassNames({ event: calendarEvent }) {
        const colorName = calendarsColor[calendarEvent._def.extendedProps.calendar];
        return [`bg-${colorName}`];
      },
      eventClick({ event: clickedEvent, jsEvent }) {
        jsEvent.preventDefault();
        const originalId = clickedEvent.extendedProps.originalId; 
        if (clickedEvent._def) {
          clickedEvent._def.publicId = originalId;
        }
        dispatch(handleSelectEvent(clickedEvent)); 
        handleAddEventSidebarToggle();
      },
      customButtons: {
        sidebarToggle: {
          icon: 'bi bi-list',
          click() {
            handleLeftSidebarToggle();
          }
        }
      },
      dateClick(info) {
        const ev = { ...blankEvent };
        ev.start = info.date;
        ev.end = info.date;
        ev.allDay = true;
        dispatch(handleSelectEvent(ev));
        handleAddEventSidebarToggle();
      },
      eventDrop({ event: droppedEvent }) {
        dispatch(updateEvent(droppedEvent));
      },
      eventResize({ event: resizedEvent }) {
        dispatch(updateEvent(resizedEvent));
      },
      ref: calendarRef,
      direction
    };

    // @ts-ignore
    return <FullCalendar {...calendarOptions} />;
  } else {
    return null;
  }
};

export default Calendar;
