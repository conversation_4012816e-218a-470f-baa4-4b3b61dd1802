/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');

/** @type {import('next').NextConfig} */

module.exports = {
  trailingSlash: true,
  reactStrictMode: false,
  transpilePackages: [
    '@fullcalendar/common',
    '@fullcalendar/core',
    '@fullcalendar/react',
    '@fullcalendar/daygrid',
    '@fullcalendar/list',
    '@fullcalendar/timegrid',
  ],
  server: {
    host: 'localhost',
    port: 3000,
  },
  experimental: {
    esmExternals: false,
  },
  webpack: config => {
    config.resolve.alias = {
      ...config.resolve.alias,
      // apexcharts: path.resolve(__dirname, './node_modules/apexcharts-clevision'), // Ensure this is correct
    };
    return config;
  },
};
