import { styled } from "@mui/material/styles";
import TableCell from "@mui/material/TableCell";

const MUITableCell = styled(TableCell)(({ theme }) => ({
  // You may want to keep default padding for better alignment
  padding: theme.spacing(1), // Set a default padding, adjust as necessary
  "&:not(:last-child)": {
    paddingRight: theme.spacing(2), // Space between cells
  },
  "& .MuiTypography-root": {
    "&.data-field": {
      color: theme.palette.text.viewData,
      fontSize: "16px",
      fontWeight: 600,
    },
  },
}));

export default MUITableCell
