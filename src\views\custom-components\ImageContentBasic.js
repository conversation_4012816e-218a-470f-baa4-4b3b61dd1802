// ** MUI Components
import { styled } from '@mui/material/styles'
import Grid from '@mui/material/Grid'
import MuiLink from '@mui/material/Link'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import IconTextBox from './IconTextBox'
import Button from '@mui/material/Button'

const Img = styled('img')(({ theme }) => ({
  [theme.breakpoints.down('lg')]: {
    height: 330,
    marginTop: theme.spacing(5)
  },
  [theme.breakpoints.up('lg')]: {
    height: 340,
    marginTop: theme.spacing(5)
  },
  [theme.breakpoints.down('md')]: {
    height: 300
  }
}))

const ImageContentBasic = props => {
  // ** Props
  const { title, subtitle, src } = props

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: 'center',
          width: { xs: '98vw', md: '85vw' },
          background: 'rgb(255 159 67 / 16%)'
        }}
      >
        <Box sx={{ minWidth: '35%', display: 'flex', justifyContent: 'center' }}>
          <Img height='400' alt='image' src={src} />
        </Box>
        <Box
          sx={{
            p: { xs: 6, md: 14 },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'center',
            minWidth: '65%'
          }}
        >
          <Typography variant='h3' sx={{ mb: 6, fontWeight: '700' }}>
            {title}
          </Typography>
          <Typography variant='h6' sx={{ mb: 3.5, color: '#777' }}>
            {subtitle || null}
          </Typography>
          <IconTextBox icon={'tabler:user-check'} title={'No cost to join'} />
          <IconTextBox
            icon={'tabler:user-check'}
            title={'Enter your data'}
            subtitle={'Get your Readiness check up done to print a readiness report.'}
          />
          <IconTextBox
            icon={'tabler:user-check'}
            title={'Get one free consultation form our RMs'}
            subtitle={'Get regular industry updates and articles from our Redevelopment Library'}
          />
          <Button variant='contained' sx={{ mt: 4 }}>
            Sign up for free
          </Button>
        </Box>

        {/* <Img height='500' alt='error-illustration' src='/images/pages/404.png' /> */}
      </Box>
    </>
  )
}

export default ImageContentBasic
