import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Box, Typography, Paper, CircularProgress, useTheme } from '@mui/material';
import axios from 'axios';
import { useContext } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import { getAuthorizationHeaders, getUrl } from 'src/helpers/utils';
import authConfig from 'src/configs/auth';



const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

const TotalDonationsPerMonthBarGraphTenant = () => {
    const dummyMonthlyData = [
        { month: "Jan", amount: 0 },
        { month: "Feb", amount: 0 },
        { month: "Mar", amount: 0 },
        { month: "Apr", amount: 0 },
        { month: "May", amount: 0 },
        { month: "Jun", amount: 0 },
        { month: "Jul", amount: 0 },
        { month: "Aug", amount: 0 },
        { month: "Sep", amount: 0 },
        { month: "Oct", amount: 0 },
        { month: "Nov", amount: 0 },
        { month: "Dec", amount: 0 },
      ];
      
  const theme = useTheme();
  const { user } = useContext(AuthContext);
  const [title] = useState('Total Donations per Month');
  const [series, setSeries] = useState([{ name: 'Donations', data: [] }]);
  const [categories, setCategories] = useState([]);
  const [currencySymbol] = useState('₹');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const orgId = user?.orgId; // Adjust based on user context if needed
        const url = `${getUrl(authConfig.dashboardStatisticsEndpoint + '/monthly-donations')}`;
        const params = {};
        if (orgId) params.orgId = orgId;

        const response = await axios({
          method: 'get',
          url: url,
          params: params,
          headers: getAuthorizationHeaders(),
        });
       // console.log('Monthly Donations:', response.data);

        let monthlyData = response.data.data;
        if (!monthlyData || monthlyData.length === 0) {
          monthlyData = dummyMonthlyData;
        }
        
        
        if (monthlyData.length === 0) {
          setCategories([]);
          setSeries([{ name: 'Donations', data: [] }]);
        } else {
          setCategories(monthlyData.map((item) => item.month));
          setSeries([{ name: 'Donations', data: monthlyData.map((item) => item.amount || 0) }]);
        }
      } catch (error) {
        console.error('Error fetching donation data:', error);
        setCategories([]);
        setSeries([{ name: 'Donations', data: [] }]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user?.orgId]);

  const chartOptions = {
    chart: {
      type: 'bar',
      toolbar: { show: false },
      zoom: { enabled: false },
      foreColor: theme.palette.text.secondary,
    },
    colors: [theme.palette.primary.main],
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: '45%',
        distributed: false,
      },
    },
    dataLabels: { enabled: false },
    xaxis: {
      categories: categories,
      axisBorder: { show: false },
      axisTicks: { show: false },
      labels: { style: { fontSize: '12px' } },
    },
    yaxis: {
      title: {
        text: `Total Donations (${currencySymbol})`,
        style: { fontSize: '12px', fontWeight: 500 },
        offsetX: 10
        
      },
      labels: {
        style: { fontSize: '12px' },
        formatter: (value) => `${Math.floor(value)}`,
      },
    },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 3,
      xaxis: { lines: { show: false } },
      yaxis: { lines: { show: true } },
    },
    tooltip: {
      theme: theme.palette.mode,
      y: {
        formatter: (value) => `${currencySymbol}${value.toLocaleString()}`,
        title: { formatter: (seriesName) => `${seriesName}:` },
      },
    },
  };

  return (
    <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        {title}
      </Typography>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <CircularProgress />
        </Box>
      ) : categories.length > 0 && series[0].data.length > 0 ? (
        <Chart options={chartOptions} series={series} type="bar" height={300} />
      ) : (
        <Typography ></Typography>
      )}
    </Paper>
  );
};

export default TotalDonationsPerMonthBarGraphTenant;
