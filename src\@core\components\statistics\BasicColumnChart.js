import React, { useEffect, useState, useContext } from "react";
import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import authConfig from "src/configs/auth";
import axios from "axios";
import { getUrl, getAuthorizationHeaders } from "src/helpers/utils";
import { AuthContext } from "src/context/AuthContext";
import { Box, Card, Typography } from "@mui/material";

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const BasicColumnChart = () => {
  const { user } = useContext(AuthContext);
  const [chartData, setChartData] = useState({ spByMonth: [], chsByMonth: [] });
  const router = useRouter(); // Initialize the router

  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs +
          authConfig.serviceProvidersByMonthAdminEndpoint
      );
    } else {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs +
          authConfig.serviceProvidersByMonthHTEndpoint
      );
    }

    let headers;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        accept: authConfig?.STATISTICS_GET_REQ_SP_COUNT_ADMIN_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        accept: authConfig?.STATISTICS_GET_REQ_SP_COUNT_HT_V1,
      });
    }

    axios({
      method: "get",
      url,
      headers,
    })
      .then((res) => {
        setChartData(res?.data);
      })
      .catch((error) => {
        console.error("Error fetching chart data", error);
      });
  }, []);

  // Map the data to the format needed for the chart
  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  // Format the data for SP and CHS
  const formattedSPData = Array.from({ length: 12 }, (_, index) => {
    const found = chartData.spByMonth?.find(
      (d) => parseInt(d.month) === index + 1
    );
    return found ? found.count : 0;
  });

  const formattedCHSData = Array.from({ length: 12 }, (_, index) => {
    const found = chartData.chsByMonth?.find(
      (d) => parseInt(d.month) === index + 1
    );
    return found ? found.count : 0;
  });

  // Get the current year
  const currentYear = new Date().getFullYear();

  const state = {
    series: [
      {
        name: "SP",
        data: formattedSPData,
      },
      {
        name: "CHS",
        data: formattedCHSData,
      },
    ],
    options: {
      chart: {
        type: "bar",
        height: 350,
        events: {
          dataPointSelection: (
            event,
            chartContext,
            { seriesIndex, dataPointIndex }
          ) => {
            const month = dataPointIndex + 1; // Extract month (1-based index)
            const seriesName = state.series[seriesIndex].name; // Get the series name
            if (seriesName === "SP") {
              router.push(`/SP?month=${month}`); // Navigate to SP page with the month filter
            } else if (seriesName === "CHS") {
              router.push(`/CHS?month=${month}`); // Navigate to CHS page with the month filter
            }
          },
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: "55%",
          endingShape: "rounded",
          dataLabels: {
            position: "top", // Display data labels above the bars
          },
        },
      },
      colors: ["rgba(141, 223, 141, 1)", "rgba(16, 138, 0, 1)"],
      dataLabels: {
        enabled: true,
        formatter: (val) => `${val}`,
        offsetY: -20,
        style: {
          fontSize: "12px",
          colors: ["#304758"],
        },
      },
      stroke: {
        show: true,
        width: 2,
        colors: ["transparent"],
      },
      xaxis: {
        categories: months,
      },
      yaxis: {
        title: {
          text: `No. of SP's and CHS's by Month (${currentYear})`,
        },
      },
      fill: {
        opacity: 1,
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return `${val}`;
          },
        },
      },
      legend: {
        position: "top",
        horizontalAlign: "center",
      },
    },
  };

  return (
    <div id="chart">
      <Card sx={{ p: 3 }}>
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
            SP's and CHS's by month
          </Typography>
        </Box>
        <ApexChart
          options={state.options}
          series={state.series}
          type="bar"
          height={350}
        />
      </Card>
    </div>
  );
};

export default BasicColumnChart;
