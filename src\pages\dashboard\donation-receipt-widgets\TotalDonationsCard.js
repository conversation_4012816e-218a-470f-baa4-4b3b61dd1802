import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  CircularProgress,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';

const TotalDonationsCard = ({ title, value, icon, formatValue, color = '#1F1F7A', loading }) => {
  const theme = useTheme();

  const displayValue = loading
    ? <CircularProgress size={20} />
    : value !== null && value !== undefined
      ? (formatValue ? formatValue(value) : value)
      : '-';

  return (
    <Card
      sx={{
        position: 'relative',
        overflow: 'visible',
        borderLeft: `5px solid ${color}`,
        borderRadius: '8px',
      }}
      elevation={2}
    >
      <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
        <Box>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 0.5 }}>
            {title}
          </Typography>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            {displayValue}
          </Typography>
        </Box>
        <Avatar
          sx={{
            bgcolor: `${color}20`,
            color: color,
            width: 40,
            height: 40,
          }}
        >
          {icon}
        </Avatar>
      </CardContent>
    </Card>
  );
};

export default TotalDonationsCard;
