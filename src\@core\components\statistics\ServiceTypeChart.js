import React, { useContext, useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { AuthContext } from "src/context/AuthContext";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";
import { Card } from "@mui/material";

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const ServiceTypeChart = () => {
  const { listValues,user } = useContext(AuthContext)

  const router = useRouter();

  const [sampleData,setSampleData] = useState([])

  useEffect(() => {
    let url;
        if (user?.roleId === authConfig?.superAdminRoleId) {
          url =
            getUrl(authConfig.statisticsEndpointGraphs) +
            "/admin/service-requisitions-count-by-service-type";
        } else {
          url =
            getUrl(authConfig.statisticsEndpointGraphs) +
            "/service-requisitions-count-by-service-type";
        }
    
        let headers;
    
        if (user?.roleId === authConfig?.superAdminRoleId) {
          headers = getAuthorizationHeaders({
            accept: authConfig?.STATISTICS_GET_REQ_BY_SERVICE_TYPE_COUNT_ADMIN_V1,
          });
        } else {
          headers = getAuthorizationHeaders({
            accept: authConfig?.STATISTICS_GET_REQ_BY_SERVICE_TYPE_COUNT_EMPLOYEE_V1,
          });
        }
    
    axios({
      method: "get",
      url: url,
      headers: headers,
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);
  const generateRandomColor = () => {
    return `#${Math.floor(Math.random() * 16777215)
      .toString(16)
      .padStart(6, "0")}`;
  };

  // Extract categories and data from sampleData
  const serviceTypeIds = sampleData?.map(
    (item) => listValues?.find((listItem) => listItem.id === item.serviceTypeId)?.name || item.serviceTypeId
  );
  const counts = sampleData?.map((item) => item.count);
  const randomColors = serviceTypeIds?.map(() => generateRandomColor());

  const handleBarClick = (event, chartContext, config) => {
    const clickedIndex = config.dataPointIndex;
    const clickedServiceId = sampleData[clickedIndex]?.serviceTypeId;
    if (clickedServiceId) {
      router.push({
        pathname: "/service-requisitions",
        query: { service: clickedServiceId, assigned: user?.roleId !== authConfig?.superAdminRoleId },
      });
    }
  };

  const options = {
    chart: {
      id: "service-type-bar-chart",
      toolbar: { show: true },
      events: {
        dataPointSelection: handleBarClick, // Add click event handler
      },
    },
    xaxis: {
      categories: serviceTypeIds,
      labels: {
        style: { fontSize: "12px" },
        rotate: -45, // Rotate labels for better readability
      },
    },
    yaxis: {
      title: { text: "No. of SR's by Service type" },
      labels: {
        style: { fontSize: "12px" },
      },
    },
    title: {
      text: "SR Distribution Across Service Types",
    },
    plotOptions: {
      bar: {
        horizontal: true,
        distributed: true,
      },
    },
    dataLabels: {
      enabled: true, // Show data labels for better insight
      style: {
        colors: ["#000"],
      },
    },
    colors: randomColors,
  };

  const chartHeight = Math.max(sampleData.length * 30, 400);
  
  return (
    <Card style={{ overflowY: "auto", height: "450px" }} sx={{ p: 3 }}>
      <ApexChart options={options} series={[{ name: "Total Requisitions", data: counts }]} type="bar" height={chartHeight} />
    </Card>
  );
};

export default ServiceTypeChart;
