const DataGrid = theme => {
  return {
    MuiDataGrid: {
      styleOverrides: {
        root: {
          border: 0,
          color: theme.palette.text.primary,
          '& .MuiDataGrid-columnHeader:focus, & .MuiDataGrid-columnHeader:focus-within': {
            outline: 'none'
          },
          '& .MuiDataGrid-overlay': {
            backgroundColor: "#fff" // Ensure the overlay also has the same background color
          }
        },
        
        toolbarContainer: {
          paddingRight: `${theme.spacing(6)} !important`,
          paddingLeft: `${theme.spacing(3.25)} !important`
        },
        columnHeaders: {
          backgroundColor: "#d6d6f5"
        },
        columnHeader: {
          '&:not(.MuiDataGrid-columnHeaderCheckbox)': {
            paddingLeft: theme.spacing(4),
            paddingRight: theme.spacing(4),
            '&:first-of-type': {
              paddingLeft: theme.spacing(6)
            }
          },
          '&:last-of-type': {
            paddingRight: theme.spacing(6)
          }
        },
        columnHeaderCheckbox: {
          maxWidth: '30px !important', // Reduced width
          minWidth: '30px !important',  // Reduced width
          '& .MuiCheckbox-root': {
            padding: '4px', // Adjust padding inside the checkbox
            '& .MuiSvgIcon-root': {
              fontSize: '18px', // Adjust the size of the checkbox icon
              width: '18px', // Set width
              height: '18px' // Set height
            }
          }
        },
        columnHeaderTitleContainer: {
          padding: 0
        },
        columnHeaderTitle: {
          fontWeight: 700,
          fontSize: '0.75rem'
        },
        columnSeparator: {
          color: theme.palette.divider
        },
        row: {
          '&:last-child': {
            '& .MuiDataGrid-cell': {
              borderBottom: 0
            }
          }
        },
        cell: {
          borderColor: theme.palette.divider,
          '&:not(.MuiDataGrid-cellCheckbox)': {
            paddingLeft: theme.spacing(4),
            paddingRight: theme.spacing(4),
            '&:first-of-type': {
              paddingLeft: theme.spacing(6)
            }
          },
          '&:last-of-type': {
            paddingRight: theme.spacing(6)
          },
          '&:focus, &:focus-within': {
            outline: 'none'
          }
        },
        cellCheckbox: {
          maxWidth: '30px !important', // Reduced width
          minWidth: '30px !important',  // Reduced width
          '& .MuiCheckbox-root': {
            padding: '4px', // Adjust padding inside the checkbox
            '& .MuiSvgIcon-root': {
              fontSize: '18px', // Adjust the size of the checkbox icon
              width: '18px', // Set width
              height: '18px' // Set height
            }
          }
        },
        editInputCell: {
          padding: 0,
          color: theme.palette.text.primary,
          '& .MuiInputBase-input': {
            padding: 0
          }
        },
        footerContainer: {
          borderTop: `1px solid ${theme.palette.divider}`,
          '& .MuiTablePagination-toolbar': {
            paddingLeft: `${theme.spacing(4)} !important`,
            paddingRight: `${theme.spacing(4)} !important`
          },
          '& .MuiTablePagination-displayedRows, & .MuiTablePagination-selectLabel': {
            color: theme.palette.text.primary
          }
        },
        selectedRowCount: {
          margin: 0,
          paddingLeft: theme.spacing(4),
          paddingRight: theme.spacing(4)
        }
      }
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          padding: '4px',
          '& .MuiSvgIcon-root': {
            fontSize: '18px',
            width: '18px',
            height: '18px'
          }
        }
      }
    }
  };
};

export default DataGrid;
