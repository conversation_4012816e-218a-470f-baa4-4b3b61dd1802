// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Badge from '@mui/material/Badge'

// ** Third Party Components
import clsx from 'clsx'
import { useKeenSlider } from 'keen-slider/react'
import CardService from '../ui/cards/basic/CardService'
import CardService2 from '../ui/cards/basic/CardService2'
import CardService3 from '../ui/cards/basic/CardService3'
import CardService4 from '../ui/cards/basic/CardService4'

const SwiperSpacing = ({ direction }) => {
  const [loaded, setLoaded] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)

  // ** Hook
  const [sliderRef, instanceRef] = useKeenSlider({
    rtl: direction === 'rtl',
    loop: true,
    slides: {
      perView: 2,
      spacing: 20
    },
    slideChanged(slider) {
      setCurrentSlide(slider.track.details.rel)
    },
    created() {
      setLoaded(true)
    }
  })

  return (
    <>
      <Box ref={sliderRef} className='keen-slider'>
        <Box className='keen-slider__slide'>
          <CardService />
        </Box>
        <Box className='keen-slider__slide'>
          <CardService2 />
        </Box>
        <Box className='keen-slider__slide'>
          <CardService3 />
        </Box>
        <Box className='keen-slider__slide'>
          <CardService4 />
        </Box>
      </Box>
      {loaded && instanceRef.current && (
        <Box className='swiper-dots'>
          {[...Array(instanceRef.current.track.details.slides.length).keys()].map(idx => {
            return (
              <Badge
                key={idx}
                variant='dot'
                component='div'
                className={clsx({
                  active: currentSlide === idx
                })}
                onClick={() => {
                  instanceRef.current?.moveToIdx(idx)
                }}
              ></Badge>
            )
          })}
        </Box>
      )}
    </>
  )
}

export default SwiperSpacing
