import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    clearAllFilters,
    onApplyFilters,
  } = props;

  const { setValue, control, reset, handleSubmit,formState:{errors} } = useForm();

  useEffect(() => {
    const filterMap = new Map(
      selectedFilters?.map((filter) => [filter.key, filter.value])
    );

    setValue("trustName", filterMap.get("trustNameFilter") || "");
    setValue("mobileNumber", filterMap.get("contactPersonMobileFilter") || "");
    setValue("email", filterMap.get("contactPersonEmailFilter") || "");
    setValue("contactPersonName", filterMap.get("contactPersonNameFilter") || "");
  }, [selectedFilters, setValue]);

  const handleCancel = () => {
    reset();
    setSearchingState(false);
    clearAllFilters();
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = (data) => {
    const filters = [];

    if (data?.trustName) {
      filters.push({
        key: "trustNameFilter",
        label: "Trust Name",
        value: data.trustName,
      });
    }
    if (data?.mobileNumber) {
      filters.push({
        key: "contactPersonMobileFilter",
        label: "Mobile Number",
        value: data.mobileNumber,
      });
    }
    if (data?.email) {
      filters.push({
        key: "contactPersonEmailFilter",
        label: "Email Address",
        value: data.email,
      });
    }
    if (data?.contactPersonName) {
      filters.push({
        key: "contactPersonNameFilter",
        label: "Contact Person Name",
        value: data.contactPersonName,
      });
    }
    onApplyFilters(filters);
    setSearchingState(true);
    toggle();
  };

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
        <Header
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            position: "relative",
          }}
        >
          <Typography variant="h5" sx={{ ml: 3 }}>
            Advanced Search
          </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>

        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems="center">
              {/* Donor Name */}
              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <Controller
                    name="trustName"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Trust Name"
                        InputLabelProps={{ shrink: true }}
                        placeholder="Enter Trust name"
                        error={Boolean(errors.trustName)}
                        helperText={errors.trustName?.message}
                        aria-describedby="trustName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={12}>
                <FormControl fullWidth>
                  <Controller
                    name="contactPersonName"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Contact Person Name"
                        InputLabelProps={{ shrink: true }}
                        placeholder="Enter Contact Person Name"
                        error={Boolean(errors.contactPersonName)}
                        helperText={errors.contactPersonName?.message}
                        aria-describedby="contactPersonName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} xl={12}>
                <FormControl fullWidth>
                  <Controller
                    name="email"
                    control={control}
                    rules={{
                      required: false
                    }}
                    render={({ field }) => (
                      <TextField
                        type="email"
                        {...field}
                        label="Contact Person Email"
                        size="small"
                        InputLabelProps={{
                          shrink: true,
                          sx: { fontSize: "1rem" },
                        }}
                        helperText={errors.email?.message}
                        error={Boolean(errors.email)}
                        placeholder="Enter Email"
                        fullWidth
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} xl={12}>
                <FormControl fullWidth>
                  <Controller
                    name="mobileNumber"
                    control={control}
                    rules={{ required: false}}
                    render={({ field }) => (
                      <MobileNumberValidation
                        {...field}
                        type="tel"
                        label="Contact Person Number"
                        size="small"
                        error={Boolean(errors.mobileNumber)}
                        helperText={errors.mobileNumber?.message}
                        InputLabelProps={{ shrink: true }}
                        placeholder="+91 1234567890"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit(handleApply)}
            sx={{ mr: 4 }}
          >
            Apply
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default AdvancedSearch;
