import EditIcon from "@mui/icons-material/Edit";
import {
  <PERSON>,
  Card,
  CardContent,
  Container,
  Divider,
  Icon<PERSON>utton,
  <PERSON>lt<PERSON>,
  Typography,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import EditProfileForm from "./EditProfileForm";

const DonorProfile = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm();
  const { user, listValues } = useContext(AuthContext);

  const fetchOrganisationData = () => {
    const url =
      getUrl(authConfig.organisationsEndpoint) + "/donor/" + user?.orgId;

    axios({
      method: "get",
      url: url,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setProfileData(res.data);
      })
      .catch((err) => console.log("CHS Data error", err));
  };

  useEffect(() => {
    fetchOrganisationData();
  }, []);

  const profileFields = [
    { label: "Name", key: "name" },
    { label: "Email", key: "email" },
    { label: "Mobile Number", key: "contactNumber" },
    { label: "Donor Type", key: "donorType" },
    ...(listValues?.find((item) => item.id === profileData?.donorType)?.name ===
    "Entity"
      ? [{ label: "Organisation Name", key: "donorOrgName" }]
      : []),
    { label: "PAN Number", key: "panNo" },
    { label: "Referral Source", key: "donorReferralSource" },
    ...(listValues?.find((item) => item.id === profileData?.donorReferralSource)
      ?.name === "Any Other"
      ? [{ label: "Other Referral Source", key: "donorReferralSourceAnyOther" }]
      : []),
    { label: "Address", key: "address" },
    { label: "State", key: "state" },
    { label: "PIN Code", key: "pinCode" },
  ];
  //profileData?.donorType
  const ProfileView = () => {
    return (
      <Box>
        {profileFields.map((field, idx) => (
          <Box key={field.key}>
            <Box sx={{ display: "flex", px: 2, py: 1 }}>
              <Box sx={{ flex: 1, color: "text.secondary", fontWeight: 500 }}>
                {field.label}
              </Box>
              <Box sx={{ flex: 2, color: "text.primary" }}>
                {field.label === "Donor Type"
                  ? listValues?.find(
                      (item) => item.id === profileData?.donorType
                    )?.name || "-"
                  : field.label === "Referral Source"
                  ? listValues?.find(
                      (item) => item.id === profileData?.donorReferralSource
                    )?.name || "-"
                  : profileData?.[field.key] || "-"}
              </Box>
            </Box>
            {idx < profileFields.length - 1 && <Divider />}
          </Box>
        ))}
      </Box>
    );
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Card>
        <CardContent>
          <Box
            sx={{
              position: "relative",
              "&:hover .edit-icon": {
                opacity: 1,
              },
            }}
          >
            {/* Donor Information Heading */}
            <Box
              sx={{ bgcolor: "#ededf7", px: 2, py: 1, borderRadius: 1, mb: 4 }}
            >
              <Typography variant="subtitle1" fontWeight="bold">
                Donor Information
              </Typography>
            </Box>
            {!isEditing && (
              <Tooltip title="Edit Profile">
                <IconButton
                  className="edit-icon"
                  onClick={() => setIsEditing(true)}
                  sx={{
                    position: "absolute",
                    right: 0,
                    top: 0,
                    opacity: 0,
                    transition: "opacity 0.2s",
                    color: "primary.main",
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.04)",
                    },
                  }}
                >
                  <EditIcon />
                </IconButton>
              </Tooltip>
            )}

            {isEditing ? (
              <EditProfileForm
                profileData={profileData}
                setIsEditing={setIsEditing}
                fetchOrganisationData={fetchOrganisationData}
              />
            ) : (
              <ProfileView />
            )}
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
};

export default DonorProfile;
