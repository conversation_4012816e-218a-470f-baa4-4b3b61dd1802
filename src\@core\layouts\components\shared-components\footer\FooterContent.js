// ** MUI Imports
import Box from "@mui/material/Box";
import MuiLink from "@mui/material/Link";
import Typography from "@mui/material/Typography";
import useMediaQuery from "@mui/material/useMediaQuery";
import authConfig from "src/configs/auth";

const FooterContent = () => {
  // ** Var
  const hidden = useMediaQuery((theme) => theme.breakpoints.down("md"));

  const guestURL = authConfig.guestURL;

  return (
    <Box
      sx={{
        display: "flex",
        flexWrap: "wrap",
        alignItems: "center",
        justifyContent: "space-between",
      }}
    >
      <Typography sx={{ mr: 3 }}>
        {`© ${new Date().getFullYear()} `}
        <MuiLink href="/dashboard">Pure Heart</MuiLink>
      </Typography>
      {
        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            "& :not(:last-child)": { mr: 4 },
          }}
        >
          <MuiLink href={guestURL + "contact-us"}>Contact Us</MuiLink>
          <MuiLink href={guestURL + "privacy-policy"}>Privacy Policy</MuiLink>
          <MuiLink href={guestURL + "terms-conditions"}>
            Terms & Conditions
          </MuiLink>
        </Box>
      }
    </Box>
  );
};

export default FooterContent;
