import {
  <PERSON><PERSON><PERSON><PERSON>,
  Divider,
  Grid,
  Toolt<PERSON>,
  Typography,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useEffect, useState } from "react";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import axios from "axios";
import FallbackSpinner from "src/@core/components/spinner";
import { useForm } from "react-hook-form";
import { Box } from "@mui/system";
import CustomChip from "src/@core/components/mui/chip";
import { format } from "date-fns";
import Link from "next/link";
const STATUS = {
  VERIFIED: "Verified",
  PENDING: "Pending",
  "NOT VERIFIED": "NOT_VERIFIED_INDIVIDUAL_CONTACT",
};
const userStatusObj = {
  VERIFIED: "Verified",
  PENDING: "Pending",
  NOT_VERIFIED_INDIVIDUAL_CONTACT: "Not Verified",
};
const userStatusObjs = {
  true: "Active",
  false: "InActive",
};
export const formatCategory = (category) => {
  if (!category) return "";
  return category
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

const Email = ({role}) => {
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const mapIsActiveToLabel = (verificationStatus) => {
    return userStatusObj[verificationStatus] || verificationStatus || "Unknown";
  };
  const mapIsActiveToLabels = (isActive) => {
    return userStatusObjs[isActive] || "Unknown";
  };
  const getStatusColor = (verificationStatus) => {
    if (verificationStatus === STATUS.VERIFIED) return "success";
    if (verificationStatus === STATUS.PENDING) return "warning";
    if (verificationStatus === STATUS["NOT VERIFIED"]) return "error";
  };
 
  const columns = [
    {
      field: "contactValue",
      minWidth: 175,
      headerName: "Email",
      flex: 0.28,
      renderCell: (params) => {
        const email = params?.value;

        return email?.length > 21 ? (
          <Tooltip title={email}>
            <Link
              href={`mailto:${email}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{email}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "contactType",
      minWidth: 135,
      headerName: "Contact Type",
      flex: 0.16,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "verificationStatus",
      headerName: "Status",
      flex: 0.11,
      minWidth: 150,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row?.verificationStatus)} // Display correct status
            color={getStatusColor(row?.verificationStatus)} // Set color based on status
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "isActive",
      headerName: "Is Active",
      flex: 0.14,
      minWidth: 110,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabels(row?.isActive)}
            color={row?.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "createdOn",
      headerName: "Captured On",
      flex: 0.18,
      minWidth: 160,
      renderCell: ({ row }) => (
        <Tooltip
          title={format(new Date(row.createdOn), "MMMM dd, yyyy HH:mm:ss")}
        >
          <span>
            {format(new Date(row.createdOn), "MMMM dd, yyyy HH:mm:ss")}
          </span>
        </Tooltip>
      ),
    },
  ].filter(Boolean); // Filter out null values if the condition is false

  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [searchKeyword, setSearchKeyword] = useState("");


  const fetchNotifications = async (
    currentPage,
    currentPageSize,
    role,
    searchKeyword
  ) => {
    setLoading(true);

    const url = getUrl(authConfig.getAllEmail);
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
      roleId:role,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        console.log("DATA", response?.data);
        setUserList(response.data?.individualVerificationResponseDTOList || []);
        setRowCount(response.data?.rowCount);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching emails:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNotifications(page, pageSize,role, searchKeyword);
  }, [page, pageSize,role, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <>
      <div>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h6">
              Unregistered {role === authConfig.societyRoleId ? "Societies":"Service Providers"} List </Typography>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              ></Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <CardContent>
          <div style={{ height: 380, width: "100%" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <DataGrid
                rows={userList || []}
                columns={columns}
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
                components={{
                  NoRowsOverlay: () => (
                    <Typography
                      variant="body1"
                      align="center"
                      sx={{ marginTop: "120px" }}
                    >
                      {userList?.length === 0 ? "No Data" : "No Rows"}
                    </Typography>
                  ),
                }}
              />
            )}
          </div>
        </CardContent>
      </div>
    </>
  );
};

export default Email;
