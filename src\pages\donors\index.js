import { useRouter } from "next/router";
import { useEffect } from "react";
import NavTabsDonors from "src/@core/components/custom-components/NavTabsDonors";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRBAC } from "../permission/RBACContext";
import DonorsPage from "./DonorsPage";
import ContactGroupActions from "../contact-groups";

const Donors = () => {
  const {
    canMenuPage,
    canMenuPageSection,
    canMenuPageSectionField,
    rbacRoles,
  } = useRBAC();
  const router = useRouter();

  const canAccessDonors = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.DONORS, requiredPermission);


  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessDonors(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);
  if (canAccessDonors(PERMISSIONS.READ)) {
    return (
     <NavTabsDonors
        tabContent1={
          <>
           <DonorsPage/> 
          </>
        }
        tabContent2={
          <>
           <ContactGroupActions/>
          </>
        }
      />
    );
  } else {
    return null;
  }
};

export default Donors;
