// ** MUI Imports
import Grid from "@mui/material/Grid";

// ** Custom Components Imports
import { useContext, useEffect, useState } from "react";

// ** Styled Component
import axios from "axios";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import LongFormContent from "./sections-longform/LongFormContent";

import { useRBAC } from "../permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/AuthContext";
import DonorProfile from "../donor";


const initialFormData = () => ({
  profileDetails: {
    name: "",
    email: "",
    mobileNumber: "",
  },
  organisationDetails: {
    trustName: "",
    orgEmail: "",
    registrationNo: "",
    panNo: "",
    g80RegistrationNo: "",
    state: "",
    address: "",
    pinCode: "",
  },
  documents: {
    logoFileLocation: "",
    g80CertificationFileLocationPageOne: "",
    g80CertificationFileLocationPageTwo: "",
  },
  members: {
    orgIndividualsList: [],
  },
});

const RegistrationForm = () => {
  const [employeesData, setEmployeesData] = useState([]);

  const { user } = useContext(AuthContext);

  const { canMenuPage, rbacRoles } = useRBAC();
  const router = useRouter();

  const canAccessProfile = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.PROFILE, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessProfile(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);
  if (canAccessProfile(PERMISSIONS.READ)) {
    return (
      <div>
        <style>
          {`
          .tableBody:hover {
            background-color: #f6f6f7;
          }
      `}
        </style>
        <DatePickerWrapper>
          <Grid container spacing={2} className="match-height">
            {user?.organisationCategory === "DONOR" ? (
              <DonorProfile />
            ) : (
              <LongFormContent
                employeesData={employeesData}
                initialFormData={initialFormData}
              />
            )}
          </Grid>
        </DatePickerWrapper>
      </div>
    );
  } else {
    return null;
  }
};

export default RegistrationForm;
