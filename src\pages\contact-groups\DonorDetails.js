import { Box, FormControl, Grid, TextField } from "@mui/material";
import { useEffect, useRef } from "react";
import { Controller, useForm } from "react-hook-form";

const DonorDetails = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.profileDetails || {}, // Initialize form fields with existing data
  });

  // Ref to track if the initial reset has been called
  const isInitialized = useRef(false);

  // Reinitialize form when formData.societyDetails changes
  useEffect(() => {
    if (formData?.profileDetails && !isInitialized.current) {
      reset(formData.profileDetails); // Reset the form with initial values
      isInitialized.current = true; // Mark as initialized
    }
  }, [formData?.profileDetails, reset]);

  // Watch all fields for changes
  const watchedFields = watch();
  // Update formData on any change
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    const hasWatchedFieldsChanged =
      JSON.stringify(previousWatchedFields.current) !==
      JSON.stringify(watchedFields);

    if (hasWatchedFieldsChanged) {
      onUpdate({
        ...watchedFields,
      });
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, onUpdate]);

  return (
    <Box sx={{ pt: 3 }}>
      <Grid container spacing={5} style={{ padding: "16px" }}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="contactGroupName"
              control={control}
              rules={{ required: "Donor Group Name is required" }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Donor Group Name"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Enter your Donor Group Name "
                  error={Boolean(errors.contactGroupName)}
                  helperText={errors.contactGroupName?.message}
                  aria-describedby="validation-contactGroupName"
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <Controller
              name="description"
              control={control}
              rules={{ required: false }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Description"
                  InputLabelProps={{ shrink: true }}
                  size="small"
                  placeholder="Add description "
                  error={Boolean(errors.description)}
                  helperText={errors.description?.message}
                  aria-describedby="validation-description"
                />
              )}
            />
          </FormControl>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DonorDetails;
