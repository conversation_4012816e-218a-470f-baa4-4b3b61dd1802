import SearchIcon from "@mui/icons-material/Search";
import { Box, CardContent, Chip, Divider, InputAdornment } from "@mui/material";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import FallbackSpinner from "src/@core/components/spinner";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import useColumns from "./Columns";
import DeleteDialog from "./DeleteDialog";
import DonationHeadDialog from "./DonationHeadDialog";
import authConfig from "src/configs/auth";
import { useRBAC } from "../permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import AdvancedSearch from "./AdvancedSearch";

const LandingPage = () => {
  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);

  const { donationHeadDetails } = useContext(AuthContext);

  const [searchKeyword, setSearchKeyword] = useState("");
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);

  const [keyword, setKeyword] = useState("");

  const [loading, setLoading] = useState(true);
  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };
  // Function to remove a single filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };
  const [selectedFilters, setSelectedFilters] = useState([]);

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });

  const [tenantsList, setTenantsList] = useState([]);
  useEffect(() => {
    // Fetch all tenants
    axios({
      method: "get",
      url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setTenantsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.name,
          }))
        );
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const fetchUsers = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    selectedFilters
  ) => {
    const url = getUrl(authConfig.donationHeadEndpoint) + "/all";

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyWord: searchKeyword,
    };
    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        const donationHeads = response.data.donationHeads || [];

        setUserList(donationHeads);

        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseCreateDialog = () => {
    reset();
    setOpenDialog(false);
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize, searchKeyword, selectedFilters);
  };

  useEffect(() => {
    fetchUsers(page, pageSize, searchKeyword, selectedFilters);
  }, [page, pageSize, searchKeyword, selectedFilters]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const columns = useColumns({
    menu,
    setMenu,
    currentRow,
    setCurrentRow,
    setOpenDialog,
    setOpenDeleteDialog,
    handleCloseMenuItems,
    tenantsList,
  });

  const { canMenuPage, canMenuPageSection, rbacRoles } = useRBAC();
  const router = useRouter();

  const canAccessDonationHeads = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.DONATION_HEAD, requiredPermission);
  const canAccessActions = (requiredPermission, section) =>
    canMenuPageSection(
      MENUS.LEFT,
      PAGES.DONATION_HEAD,
      section,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessDonationHeads(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessDonationHeads(PERMISSIONS.READ)) {
    return (
      <>
        <Grid>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">{"Donation Heads"}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                >
                  {canAccessActions(
                    PERMISSIONS.FULL_ACCESS,
                    "Main_Search_Field"
                  ) && (
                    <Grid item xs={12} sm={7} md={4} lg={4}>
                      <FormControl fullWidth>
                        <Controller
                          name="mainSearch"
                          control={control}
                          // defaultValue={name}
                          render={({ field: { onChange } }) => (
                            <TextField
                              id="mainSearch"
                              placeholder="Search by Donation Head"
                              value={keyword}
                              onChange={(e) => {
                                onChange(e.target.value);
                                setKeyword(e.target.value);
                                setSearchKeyword(e.target.value);
                              }}
                              onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                  setSearchKeyword(keyword);
                                  fetchUsers(
                                    page,
                                    pageSize,
                                    searchKeyword,
                                    selectedFilters
                                  );
                                }
                              }}
                              sx={{
                                "& .MuiInputBase-root": {
                                  height: "40px",
                                },
                              }}
                              InputProps={{
                                endAdornment: (
                                  <InputAdornment position="start">
                                    <SearchIcon
                                      sx={{
                                        cursor: "pointer",
                                        marginRight: "-15px",
                                      }}
                                      onClick={() => {
                                        setSearchKeyword(keyword);
                                        fetchUsers(
                                          page,
                                          pageSize,
                                          searchKeyword,
                                          selectedFilters
                                        );
                                      }}
                                    />{" "}
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    </Grid>
                  )}
                  {canAccessActions(
                    PERMISSIONS.FULL_ACCESS,
                    "Advanced_Search"
                  ) && (
                    <Grid item xs="auto" sm="auto" md="auto" lg="auto">
                      <AdvancedSearch
                        open={openAdvancedSearch}
                        toggle={handleAdvancedSearch}
                        searchingState={searchingState}
                        setSearchingState={setSearchingState}
                        selectedFilters={selectedFilters}
                        clearAllFilters={clearAllFilters}
                        onApplyFilters={handleApplyFilters}
                        tenantsList={tenantsList}
                      />
                    </Grid>
                  )}
                  {canAccessActions(PERMISSIONS.FULL_ACCESS, "Add") && (
                    <Grid item xs="auto" sm="auto" md="auto" lg="auto">
                      <Button variant="contained" onClick={handleOpenDialog}>
                        Add
                      </Button>
                    </Grid>
                  )}
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />

          <CardContent>
            <>
              <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
                {selectedFilters.map((filter) => {
                  if (filter.label === "NGO Name") {
                    const matchedItem = tenantsList.find(
                      (item) => item.value === filter.value
                    );

                    // Use the key of matchedItem if found, otherwise display the ID itself
                    const displayValue = matchedItem ? matchedItem.key : id;

                    return (
                      <Chip
                        key={filter.key} // Ensure unique key for each chip
                        label={`${filter.label}: ${displayValue}`}
                        onDelete={() => handleRemoveFilter(filter.key)} // Pass both filter key and ID
                        sx={{ mr: 1, mb: 1 }}
                      />
                    );
                  }
                  const displayValue =
                    filter.label === "Role" && matchedItem
                      ? matchedItem.key
                      : filter.value;

                  return (
                    filter.label && ( // Only render the Chip if label is not null or undefined
                      <Chip
                        key={filter.key}
                        label={`${filter.label}: ${displayValue}`}
                        onDelete={() => handleRemoveFilter(filter.key)}
                        sx={{ mr: 1, mb: 1 }}
                      />
                    )
                  );
                })}
              </Box>
            </>
            <div style={{ height: 430, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={userList}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
              )}
            </div>
          </CardContent>
          <DeleteDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />
        </Grid>

        <DonationHeadDialog
          open={openDialog}
          onClose={handleCloseCreateDialog}
          formData={donationHeadDetails || {}}
          fetchUsers={fetchUsers}
          page={page}
          pageSize={pageSize}
          searchKeyword={searchKeyword}
          tenantsList={tenantsList}
        />
      </>
    );
  } else {
    return null;
  }
};

export default LandingPage;
