const Typography = theme => {
  return {
    h1: {
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    h2: {
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    h3: {
      fontWeight: 500,
      color: theme.palette.text.primary
    },
    h4: {
      fontWeight: 500,
      color: theme.palette.text.primary,
      fontSize: '1.8rem',

    },
    h5: {
      fontWeight: 500,
      color: theme.palette.text.primary,
      fontSize: '1.25rem', // Add fontSize here

    },
    h6: {
      color: theme.palette.text.primary,
      fontSize: '1rem !important', // Add fontSize here

    },
    subtitle1: {
      color: theme.palette.text.primary
    },
    subtitle2: {
      color: theme.palette.text.secondary
    },
    body1: {
      color: theme.palette.text.primary,
      fontSize: '0.83rem', 
    },
    body2: {
      color: theme.palette.text.secondary,
      fontSize: '0.8rem', 

    },
    button: {
      color: theme.palette.text.primary,
      fontSize: '0.75rem', 

    },
    caption: {
      color: theme.palette.text.secondary
    },
    overline: {
      color: theme.palette.text.secondary
    }
  }
}

export default Typography
