import React, { useState, useEffect, useContext } from "react";
import {
  Box,
  Button,
  Card,
  Chip,
  Collapse,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  Link,
  Paper,
  TextField,
  Typography,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { Controller, useForm } from "react-hook-form";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";
import toast from "react-hot-toast";
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";

const DonorSelection = ({
  selectedUsers,
  setSelectedUsers,
  selectedFilters,
  setSelectedFilters,
  tenantId,
}) => {
  const auth = useAuth();
  const { user, getAllListValuesByListNameId } = useContext(AuthContext);

  // Donors data
  const [userData, setUserData] = useState([]);
  const [tagsList, setTagsList] = useState([]);
  const [selectedTags, setSelectedTags] = useState([]);
  const [keyword, setKeyword] = useState("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [rowCount, setRowCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const {
    control,
    formState: { errors },
  } = useForm();

  // Load tags list
  useEffect(() => {
    if (getAllListValuesByListNameId) {
      getAllListValuesByListNameId(
        authConfig.tagsListNameId,
        (data) =>
          setTagsList(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        (error) => console.error("Tags list error:", error)
      );
    }
  }, [getAllListValuesByListNameId]);

  // Handle tag selection
  useEffect(() => {
    if (selectedTags && selectedTags.length > 0) {
      const tagIds = selectedTags.map((tag) => tag.value);
      handleFilterChange("tagsFilter", tagIds);
    } else {
      handleFilterChange("tagsFilter", []);
    }
  }, [selectedTags]);

  const handleServiceChange = (event) => {
    const value = event.target?.value;
    setSelectedTags(value);
  };

  // Fetch donors
  const fetchDonors = async (currentPage, currentPageSize, selectedFilters) => {
    const url = getUrl(authConfig.donorsEndpoint) + "/all/donor";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserData(response.data?.donorsResponseList);
        setRowCount(
          response.data?.rowCount || response.data.donorsResponseList.length
        );
      }
    } catch (err) {
      console.error("Failed to fetch donors:", err);
    }
  };

  useEffect(() => {
    fetchDonors(page, pageSize, selectedFilters);
  }, [page, pageSize, selectedFilters]);

  // Handle filter changes
  const handleFilterChange = (filterKey, value) => {
    setSelectedFilters((prevFilters) => {
      // Remove any existing filter with this key
      const filteredFilters = prevFilters.filter(
        (filter) => filter.key !== filterKey
      );

      // Only add the filter if there's a value
      if (value) {
        return [
          ...filteredFilters,
          {
            key: filterKey,
            value: value,
          },
        ];
      }

      return filteredFilters;
    });
  };

  // Remove a filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters(
      selectedFilters.filter((filter) => filter.key !== filterKey)
    );
  };

  // Add selected donors to final list
  const addToFinalList = () => {
    if (selectedRows.length === 0) {
      toast.error("Please select at least one donor");
      return;
    }

    // Get selected donor objects
    const selectedDonors = userData.filter((donor) =>
      selectedRows.includes(donor.id)
    );

    // Check for duplicates
    const newDonors = selectedDonors.filter(
      (donor) => !selectedUsers.some((existing) => existing.id === donor.id)
    );

    if (newDonors.length === 0) {
      toast.error("All selected donors are already in the final list");
      return;
    }

    // Add to final list
    setSelectedUsers([...selectedUsers, ...newDonors]);
    setSelectedRows([]);
    toast.success(
      `${newDonors.length} donors have been added to the Final List`
    );
  };

  // Toggle filters visibility
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSelectedTags([]);
  };

  // Define columns for the donor table
  const columns = [
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.4,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "contactNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const contactNumber = params && params.value ? params.value : "";

        if (!contactNumber) return null;

        return (
          <Link
            href={`tel:${contactNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {contactNumber}
          </Link>
        );
      },
    },
    {
      field: "panNo",
      minWidth: 100,
      headerName: "Pan No",
      flex: 0.2,
    },
    {
      field: "state",
      minWidth: 90,
      headerName: "State",
      flex: 0.16,
      valueGetter: (params) => params.row?.donorMetaData?.state || "",
    },
    {
      field: "address",
      minWidth: 100,
      headerName: "Address",
      flex: 0.2,
      valueGetter: (params) => params.row?.donorMetaData?.address || "",
    },
    {
      field: "pinCode",
      minWidth: 60,
      headerName: "Pin Code",
      flex: 0.12,
      valueGetter: (params) => params.row?.donorMetaData?.pinCode || "",
    },
  ];

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  return (
    <Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
      <Typography variant="h6" sx={{ mb: 2 }}>
        Select Donors
      </Typography>

      {/* Search and Filter Bar */}
      <Box
        sx={{
          mb: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={toggleFilters}
            color={showFilters ? "primary" : "inherit"}
          >
            Filters
          </Button>
        </Box>
      </Box>

      {/* Filter Fields - Collapsible */}
      <Collapse in={showFilters}>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="nameFilter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Name"
                      placeholder="Filter by name"
                      value={
                        selectedFilters.find((f) => f.key === "nameFilter")
                          ?.value || ""
                      }
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleFilterChange("nameFilter", e.target.value);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="emailFilter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Email"
                      placeholder="Filter by email"
                      value={
                        selectedFilters.find((f) => f.key === "emailFilter")
                          ?.value || ""
                      }
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleFilterChange("emailFilter", e.target.value);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="mobileFilter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Mobile Number"
                      placeholder="Filter by mobile number"
                      value={
                        selectedFilters.find((f) => f.key === "mobileFilter")
                          ?.value || ""
                      }
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleFilterChange("mobileFilter", e.target.value);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="panNoFilter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="PAN Number"
                      placeholder="Filter by PAN number"
                      value={
                        selectedFilters.find((f) => f.key === "panNoFilter")
                          ?.value || ""
                      }
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleFilterChange("panNoFilter", e.target.value);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="addressFilter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      id="addressFilter"
                      placeholder="Search by address"
                      value={
                        selectedFilters.find((f) => f.key === "addressFilter")
                          ?.value || ""
                      }
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleFilterChange("addressFilter", e.target.value);
                      }}
                      sx={{
                        "& .MuiInputBase-root": {
                          fontSize: "0.9rem",
                          borderRadius: "5px",
                          backgroundColor: "white",
                        },
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon
                              sx={{
                                cursor: "pointer",
                              }}
                            />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="pinCodeFilter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      id="pinCodeFilter"
                      placeholder="Search by pin code"
                      value={field.value || ""}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleFilterChange("pinCodeFilter", e.target.value);
                      }}
                      sx={{
                        "& .MuiInputBase-root": {
                          fontSize: "0.9rem",
                          borderRadius: "5px",
                          backgroundColor: "white",
                        },
                      }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="start">
                            <SearchIcon
                              sx={{
                                cursor: "pointer",
                              }}
                            />
                          </InputAdornment>
                        ),
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <Controller
                  name="stateFilter"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="State"
                      placeholder="Filter by state"
                      value={
                        selectedFilters.find((f) => f.key === "stateFilter")
                          ?.value || ""
                      }
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        handleFilterChange("stateFilter", e.target.value);
                      }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth error={Boolean(errors.tags)}>
                <Controller
                  name="tags"
                  control={control}
                  render={({ field }) => (
                    <MultiSelectAutoComplete
                      id="tags"
                      label="Select Tags"
                      nameArray={tagsList}
                      value={selectedTags || []}
                      onChange={(e) => {
                        handleServiceChange(e);
                        field.onChange(e.target.value);
                      }}
                      error={Boolean(errors.tags)}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={clearAllFilters}
                >
                  Clear All
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </Collapse>

      {/* Applied Filters */}
      {selectedFilters.length > 0 && (
        <Box sx={{ mb: 2, display: "flex", flexWrap: "wrap", gap: 1 }}>
          {selectedFilters.map((filter, index) => {
            // Skip empty filters
            if (
              !filter.value ||
              (Array.isArray(filter.value) && filter.value.length === 0)
            ) {
              return null;
            }

            let label = "";
            if (filter.key === "nameFilter") label = `Name: ${filter.value}`;
            else if (filter.key === "emailFilter")
              label = `Email: ${filter.value}`;
            else if (filter.key === "mobileFilter")
              label = `Mobile: ${filter.value}`;
            else if (filter.key === "panNoFilter")
              label = `PAN: ${filter.value}`;
            else if (filter.key === "stateFilter")
              label = `State: ${filter.value}`;
            else if (filter.key === "addressFilter")
              label = `Address: ${filter.value}`;
            else if (filter.key === "tagsFilter") {
              const tagNames = selectedTags.map((tag) => tag.key).join(", ");
              label = `Tags: ${tagNames}`;
            }

            return (
              <Chip
                key={index}
                label={label}
                onDelete={() => handleRemoveFilter(filter.key)}
                color="primary"
                variant="outlined"
              />
            );
          })}
        </Box>
      )}

      {/* Donors Table */}
      <Card sx={{ flex: 1, mb: 2 }}>
        <DataGrid
          autoHeight
          rows={userData}
          columns={columns}
          pagination
          rowsPerPageOptions={rowsPerPageOptions}
          pageSize={pageSize}
          page={page - 1}
          onPageChange={handlePageChange}
          onPageSizeChange={handlePageSizeChange}
          rowCount={rowCount}
          loading={loading}
          checkboxSelection
          disableSelectionOnClick
          paginationMode="server"
          keepNonExistentRowsSelected
          onSelectionModelChange={(newSelectionModel) => {
            setSelectedRows(newSelectionModel);
          }}
          rowHeight={38}
          headerHeight={38}
          selectionModel={selectedRows}
          sx={{
            "& .MuiDataGrid-columnHeaders": { borderRadius: 0 },
            "& .MuiDataGrid-cell:focus-within": { outline: "none" },
          }}
        />
      </Card>

      {/* Selected Donors Preview */}

      <Box sx={{ mb: 2 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box>
            {selectedUsers.length > 0 && (
              <>
                <Typography variant="subtitle1" sx={{ mb: 1 }}>
                  Selected Donors: {selectedUsers.length}
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1 }}>
                  {selectedUsers.slice(0, 5).map((donor) => (
                    <Chip key={donor.id} label={donor.name} color="success" />
                  ))}
                  {selectedUsers.length > 5 && (
                    <Chip
                      label={`+${selectedUsers.length - 5} more`}
                      color="default"
                    />
                  )}
                </Box>
              </>
            )}
          </Box>
          <Button
            variant="contained"
            color="primary"
            disabled={selectedRows.length === 0}
            onClick={addToFinalList}
          >
            Add to Final List ({selectedRows.length})
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default DonorSelection;
