import { Box, Divider, Grid, Typography, useMediaQuery } from "@mui/material";
import { useContext, useEffect, useState } from "react";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";

const PreviewSection = ({ formData, employeesData }) => {

  const { listValues, getAllListValuesByListNameId } = useContext(AuthContext);
  const isMobile = useMediaQuery("(max-width:600px)");

  const [designationList, setDesignationList] = useState([]);
  const handleError = (error) => {
    console.error("designation get error:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.ChsDesignation,
        (data) =>
          setDesignationList(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const renderRow = (label, value) => {
    const specialLabels = [
      "G80 Certification File Location Page Two ",
      "G80 Certification File Location Page One ",
      "Logo File Location ",
      "State",
    ];

    if (specialLabels.includes(label)) {
      const mappedValue =
        listValues?.find((item) => item.id === value)?.name || "";
      value = mappedValue;
    }
    if(label === "Logo File Location" || label === "G80 Certification File Location Page One" || label === "G80 Certification File Location Page Two"){
      value = value?.split("/").pop()
    }

    if (label === "Assigned To") {
      const mappedValue =
        employeesData?.find((item) => item.id === value)?.name || "";
      value = mappedValue;
    }

    if (label === "Society Member Designation") {
      const mappedValue =
        designationList?.find((item) => item.value === value)?.key || "";
      value = mappedValue;
    }
    

    if (typeof value === "object" && !Array.isArray(value) && value !== null) {
      return (
        <Grid container spacing={2} sx={{ my: 1 }} key={label}>
          <Grid item xs={12} sm={4}>
            <Typography variant="body1">{label}&nbsp;:</Typography>
          </Grid>
          <Grid item xs={12} sm={7}>
            {Object.entries(value)?.map(([key, val], index) => (
              <div key={`${label}-${key}-${index}`}>
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: "bold",
                    wordBreak: "break-word",
                    overflowWrap: "break-word",
                  }}
                >
                  {key.replace(/([A-Z])/g, " $1")}: {val || ""}
                </Typography>
                {isMobile ? <Divider sx={{ my: 2 }} /> : null}
              </div>
            ))}
          </Grid>
        </Grid>
      );
    }

    return (
      <Grid container spacing={2} sx={{ my: 1 }} key={label}>
        <Grid item xs={12} sm={4}>
          <Typography variant="body1">{label}&nbsp;:</Typography>
        </Grid>
        <Grid item xs={12} sm={7}>
          <Typography
            variant="body1"
            sx={{
              fontWeight: "bold",
              wordBreak: "break-word",
              overflowWrap: "break-word",
            }}
          >
            {value || " "}
          </Typography>
          {isMobile ? <Divider sx={{ my: 2 }} /> : null}
        </Grid>
      </Grid>
    );
  };

  const renderSection = (sectionTitle, sectionData) => (
    <div key={sectionTitle}>
      <Typography variant="h6" gutterBottom>
        {sectionTitle}
      </Typography>
      {Object.entries(sectionData)?.map(([key, value]) => {
        const label = key
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase());

        if (Array.isArray(value)) {
          return null;
        }

        if (typeof value === "object" && value !== null) {
          return renderRow(label, value);
        } else {
          return renderRow(label, value);
        }
      })}
      <Divider sx={{ my: 2 }} />
    </div>
  );

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Review and Submit
      </Typography>
      <Divider sx={{ mb: 2 }} />
      {Object.entries(formData)?.map(([sectionKey, sectionData], index) => {
        if (sectionKey === "members") return null;
        const sectionTitle = `${index + 1}. ${sectionKey
          .replace(/([A-Z])/g, " $1")
          .replace(/^./, (str) => str.toUpperCase())}`;
        return renderSection(sectionTitle, sectionData);
      })}
    </Box>
  );
};

export default PreviewSection;
