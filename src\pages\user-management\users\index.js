import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  <PERSON>u,
  <PERSON>uItem,
  Typo<PERSON>,
} from "@mui/material";
import Chip from "@mui/material/Chip";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import AdvancedSearch from "./AdvancedSearch";
import Columns from "./Columns";
import UpdateUser from "./UpdateUser";
import DeActivateDialog from "./DeActivateDialog";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import Tooltip from "@mui/material/Tooltip";
import CustomAvatar from "src/@core/components/mui/avatar";
import Icon from "src/@core/components/icon";

const ParentComponent = () => {
  const { individualId, setIndividualId, individualDetails } =
    useContext(AuthContext);

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [initialRowCount, setInitialRowCount] = useState(null);
  const [currentRow, setCurrentRow] = useState(null);
  const [individualsList, setIndividualsList] = useState([]);
  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const [roles, setRoles] = useState([]);
  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.rolesEndpoint),
      headers: getAuthorizationHeaders(authConfig.getByParentRoleMIMEType),
    })
      .then((res) => {
        setRoles(res.data.rolesGetResponse);
      })
      .catch((err) => console.log("Categories error", err));
  }, []);

  const convertedArray = roles?.map((item) => ({
    value: item.id,
    key: item.name,
  }));

  const [selectedFilters, setSelectedFilters] = useState([]); // New state for filters

  // Callback to handle applied filters from AdvancedSearch
  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };

  // Function to remove a single filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  const fetchUsers = async (currentPage, currentPageSize, selectedFilters) => {
    const url = getUrl(authConfig.individualEndpoint) + "/all";
    const headers = getAuthorizationHeaders({
      contentType: authConfig.INDIVIDUAL_GET_ALL_REQ_V1,
      accept: authConfig.INDIVIDUAL_GET_ALL_RES_V1,
    });

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });
      if (initialRowCount == null) {
        setInitialRowCount(response.data.rowCount || 0);
      }

      if (response.data) {
        setIndividualsList(response.data?.individualResponseDTOS || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching Users:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers(page, pageSize, selectedFilters);
  }, [page, pageSize, selectedFilters]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const [anchorEl, setAnchorEl] = useState(null);
  const [menuPosition, setMenuPosition] = useState(null);

  // Handle Menu click
  const handleMenuClick = (params, event) => {
    setAnchorEl(event.currentTarget); // Set anchor to row's position
    setMenuPosition({ mouseX: event.clientX, mouseY: event.clientY }); // Capture mouse click position
    setIndividualId({
      ...individualId,
      id: params.row.id,
    });
    setCurrentRow(params.row); // Save the row details for later actions
  };

  const handleMenuClose = () => {
    setAnchorEl(null); // Close menu
    setMenuPosition(null); // Reset menu position
  };

  const onClickViewProfile = () => {
    setOpen(true);
    handleMenuClose();
  };

  const onClickToggleStatus = () => {
    setOpenDeleteDialog(true);
    handleMenuClose();
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchUsers(page, pageSize);
  };

  // Function to handle dialog open
  const handleOpen = () => {
    setOpen(true);
  };

  // Function to handle dialog close
  const handleClose = () => {
    setOpen(false);
  };

  const columns = Columns().concat([
    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <>
          <CustomAvatar
            skin="light"
            variant="rounded"
            sx={{
              mr: { xs: 2, lg: 4 },
              width: 34,
              height: 34,
              cursor: "pointer",
            }}
            onClick={(event) => handleMenuClick(params, event)}
          >
            <Icon icon="bi:three-dots-vertical" />
          </CustomAvatar>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            anchorReference="anchorPosition"
            anchorPosition={
              menuPosition
                ? { top: menuPosition.mouseY, left: menuPosition.mouseX }
                : undefined
            }
          >
            <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
            <MenuItem onClick={onClickToggleStatus}>
              {currentRow?.isActive ? "Deactivate" : "Activate"}
            </MenuItem>
          </Menu>
        </>
      ),
    },
  ]);

  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessUsers = (requiredPermission) =>
    canMenuPageSection(
      MENUS.TOP,
      PAGES.USER_MANAGEMENT,
      PAGES.USERS,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessUsers(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessUsers(PERMISSIONS.READ)) {
    return (
      <>
        <div>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">Users</Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                >
                  <Grid
                    item
                    sx={{
                      paddingTop: { xs: "15px", sm: "25px" },
                      mr: "6px",
                      ml: "6px",
                    }}
                  >
                    <AdvancedSearch
                      open={openAdvancedSearch}
                      toggle={handleAdvancedSearch}
                      searchingState={searchingState}
                      setSearchingState={setSearchingState}
                      convertedArray={convertedArray}
                      selectedFilters={selectedFilters}
                      clearAllFilters={clearAllFilters}
                      onApplyFilters={handleApplyFilters}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />
          <CardContent>
            {selectedFilters?.length > 0 && (
              <>
                Showing {rowCount} of {initialRowCount} Results |
              </>
            )}
            <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2, mt: 2 }}>
              {selectedFilters?.map((filter) => {
                // Find the corresponding object in convertedArray with the matching value
                const matchedItem = convertedArray.find(
                  (item) => item.value === filter.value
                );

                // Display the key of matchedItem if the label is "roleFilter", otherwise display the value
                const displayValue =
                  filter.label === "Role" && matchedItem
                    ? matchedItem.key
                    : filter.value;

                return (
                  filter.label && ( // Only render the Chip if label is not null or undefined
                    <Chip
                      key={filter.key}
                      label={`${filter.label}: ${displayValue}`}
                      onDelete={() => handleRemoveFilter(filter.key)}
                      sx={{ mr: 1, mb: 1 }}
                    />
                  )
                );
              })}
            </Box>
            <div style={{ height: 400, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <>
                  <DataGrid
                    rows={individualsList || []}
                    columns={columns}
                    pagination
                    pageSize={pageSize}
                    page={page - 1}
                    rowsPerPageOptions={rowsPerPageOptions}
                    rowCount={rowCount}
                    paginationMode="server"
                    onPageChange={handlePageChange}
                    onPageSizeChange={handlePageSizeChange}
                    // onRowClick={handleRowClick}
                    rowHeight={38}
                    headerHeight={38}
                  />
                </>
              )}
            </div>
          </CardContent>
        </div>

        <UpdateUser
          open={open}
          onClose={handleClose}
          UpdateUser={UpdateUser}
          data={individualDetails}
          fetchUsers={fetchUsers}
          page={page}
          pageSize={pageSize}
          selectedFilters={selectedFilters}
          rowData={currentRow}
        />

        <DeActivateDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        />
      </>
    );
  } else {
    return null;
  }
};

export default ParentComponent;
