// ** MUI Components
import { styled } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import IconTextBox from 'src/views/custom-components/IconTextBox'

const Img = styled('img')(({ theme }) => ({
  [theme.breakpoints.down('lg')]: {
    height: 330,
    marginTop: theme.spacing(5)
  },
  [theme.breakpoints.up('lg')]: {
    height: 340,
    marginTop: theme.spacing(5)
  },
  [theme.breakpoints.down('md')]: {
    height: 230
  }
}))

const data = [
  {
    title: 'To gain access to the untapped business potential of the Housing Society Market',
    icon: 'tabler:checkbox'
  },
  {
    title: 'Seeking redevelopment with relevant service providers and professionals',
    icon: 'tabler:layout-grid'
  },
  {
    title: 'For Enrollments and further details visit us',
    icon: 'tabler:square'
  },
  {
    title: 'Seeking redevelopment with relevant service providers and professionals',
    icon: 'tabler:layout-grid'
  }
]

const WhatYouGet = props => {
  // ** Props
  const { title, subtitle, src } = props

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          borderRadius: '5px',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: 'center',
          width: { xs: '100vw', md: '85vw' },
          background: 'rgb(101 229 245 / 16%)'
        }}
      >
        <Box sx={{ minWidth: '35%', display: 'flex', justifyContent: 'center' }}>
          <Img height='350' alt='image' src={'/images/pages/pricing-plan-basic.png'} />
        </Box>
        <Box
          sx={{
            p: { xs: 6, md: 14 },
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'center',
            minWidth: '65%'
          }}
        >
          <Typography sx={{ mb: 6, fontSize: '2.55rem', fontWeight: '700' }}>What You Get</Typography>
          <Typography variant='h4' sx={{ mb: 3.5, color: '#777' }}>
            {subtitle || null}
          </Typography>
          {data.map((item, index) => (
            <IconTextBox key={index} icon={item.icon} title={item.title} />
          ))}
          <Button variant='contained' sx={{ mt: 4 }}>
            Sign up for free
          </Button>
        </Box>

        {/* <Img height='500' alt='error-illustration' src='/images/pages/404.png' /> */}
      </Box>
    </>
  )
}

export default WhatYouGet
