// ** Theme Config Imports
import themeConfig from 'src/configs/themeConfig'

const Button = theme => {
  return {
    MuiButton: {
      styleOverrides: {
        root: {
          fontWeight: 500,
          lineHeight:'20px',
          letterSpacing: "0.47px",
          padding: `${theme.spacing(1.75, 4.75)}`,
          border: "2px solid",
          borderColor: theme.palette.primary.main,
          textTransform:'none',
        },
        contained: {
          boxShadow: theme.shadows[5],
          padding: `${theme.spacing(1.75, 4.75)}`,
          "&:hover, &.active": {
            boxShadow: "none",
            backgroundColor: '#9a9ae5', // Light blue color for hover
            transition: 'background 0.5s ease, transform 0.5s ease', // Smooth transition
            textTransform: 'none',
            // transform: 'scale(1.05)', // Slight scaling effect
            color: theme.palette.common.white, // Keep text color white
          },
        },
        outlined: {
          padding: `${theme.spacing(1.75, 4.85)}`,
        },
        sizeSmall: {
          lineHeight: 1.53846,
          borderRadius: "4px",
          padding: `${theme.spacing(1, 3.5)}`,
          "&.MuiButton-contained": {
            padding: `${theme.spacing(1, 3.5)}`,
          },
          "&.MuiButton-outlined": {
            padding: `${theme.spacing(0.75, 3.25)}`,
          },
        },
        sizeLarge: {
          lineHeight: 2,
          borderRadius: "8px",
          padding: `${theme.spacing(2.25, 6.5)}`,
          "&.MuiButton-contained": {
            padding: `${theme.spacing(2.25, 6.5)}`,
          },
          "&.MuiButton-outlined": {
            padding: `${theme.spacing(2, 6.25)}`,
          },
        },
      },
    },
    MuiButtonBase: {
      defaultProps: {
        disableRipple: themeConfig.disableRipple,
      },
    },
  };
}

export default Button
