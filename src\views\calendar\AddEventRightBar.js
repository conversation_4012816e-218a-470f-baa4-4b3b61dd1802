import { useState, useEffect, useCallback, useRef, useContext } from "react";
import axios from "axios";
import dynamic from "next/dynamic";
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  Button,
  TextField,
  IconButton,
  FormControl,
  FormHelperText,
  FormControlLabel,
  Grid,
  Divider,
  useMediaQuery,
  useTheme,
  Typography,
  InputAdornment,
  TableContainer,
  Table,
  TableBody,
  TableRow,
} from "@mui/material";
import Tooltip from "@mui/material/Tooltip";
import PeopleIcon from "@mui/icons-material/People";
import AddIcon from "@mui/icons-material/Add";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import PlaceIcon from "@mui/icons-material/Place";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useForm, Controller } from "react-hook-form";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import Header from "./Header";
import Footer from "./Footer";
import dayjs from "dayjs";
import Icon from "src/@core/components/icon";
import { useRouter } from "next/router";
import toast, { Toaster } from "react-hot-toast";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
import "react-quill/dist/quill.snow.css";
import MUITableCell from "src/pages/SP/MUITableCell";
import { AuthContext } from "src/context/AuthContext";
import ConfirmDeleteDialog from "./ConfirmDeleteDialog";
import EventDialog from "./EventDialog";

const capitalize = (string) =>
  string && string[0].toUpperCase() + string.slice(1);

// Function to remove <p> and </p> tags
function removeHtmlTags(description) {
  return description.replace(/<\/?p>/g, "");
}

const defaultState = {
  url: "",
  title: "",
  allDay: true,
  description: "",
  endDate: new Date(),
  startDate: new Date(),
  attendees: [],
  startTime: "00:00",
  endTime: "23:59",
  inPersonEvent: false,
  googleMeet: false,
  searchForRoom: "",
};

const AddEventRightBar = (props) => {
  const [attendeesId, setAttendeesId] = useState([]);
  const [attendeesData, setListOfAttendees] = useState([]);
  const [listAttendeesOptions, setListAttendeesOptions] = useState([]);
  const allDayRef = useRef(false);   

  const [allDay, setAllDay] = useState(false);
  const [inPersonEvent, setInPersonEvent] = useState(false);

  const { user } = useContext(AuthContext);

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down(375));
  const isExtraSmallScreen = useMediaQuery(theme.breakpoints.down(320));

  const [employeesData, setEmployeesData] = useState([]);
  const [employeesOptions, setEmployeesOptions] = useState([]);

  useEffect(() => {
    // Fetch all employees
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=EMPLOYEES",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setEmployeesData(res.data.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    if (!!employeesData) {
      const data = employeesData.map((entry) => ({
        value: entry.id,
        key: entry.name,
      }));
      setEmployeesOptions(data);
    }
  }, [employeesData]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.selectDropdownNew) + "?selectionType=INDIVIDUALS",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setListOfAttendees(res.data.data);
      })
      .catch((err) => console.log("Locations error", err));
  }, []);

  useEffect(() => {
    if (attendeesData?.length) {
      const uniqueData = new Map();
  
      attendeesData.forEach((entry) => {
        if (!uniqueData.has(entry.name)) {
          uniqueData.set(entry.name, { value: entry.id, key: entry.name });
        }
      });
  
      setListAttendeesOptions([...uniqueData.values()]);
    }
  }, [attendeesData]);

  const {
    store,
    dispatch,
    addEvent,
    updateEvent,
    calendarApi,
    deleteEvent,
    handleSelectEvent,
    addEventSidebarOpen,
    handleAddEventSidebarToggle,
    fetchEventById,
  } = props;

  const [values, setValues] = useState(defaultState);

  const today = dayjs().format("YYYY-MM-DD"); // Get today's date in 'YYYY-MM-DD' format
  const [startDate, setStartDate] = useState(today);
  const [startTime, setStartTime] = useState("00:00");

  const {
    control,
    setValue,
    clearErrors,
    handleSubmit,
    getValues,
    reset,
    setError,
    formState: { errors },
  } = useForm({
    defaultValues: {
      title: "",
      url: "",
      title: "",
      allDay: true,
      description: "",
      endDate: new Date(),
      startDate: new Date(),
      attendees: [],
      startTime: "00:00",
      endTime: "23:59",
      inPersonEvent: false,
      googleMeet: false,
      searchForRoom: "",
    },
  });

  const [eventData, setEventData] = useState({});

  const handleDialogClose = async () => {
    clearErrors();
    reset();
    setAttendeesId([]);
    setStartDate(today); // Reset start date to today's date
    setStartTime("00:00"); // Reset start time to the default time
    dispatch(handleSelectEvent(null));
    handleAddEventSidebarToggle();
  };
  const addTime = (time, hours, minutes) => {
    if (!time || typeof time !== "string") {
        console.warn(`Invalid time input: ${time}`);
        return null; // Return null or handle the error case as needed
    }

    const [hh, mm] = time.split(":").map(Number); // Split and convert to numbers

    if (isNaN(hh) || isNaN(mm)) {
        console.warn(`Invalid time format: ${time}`);
        return null; // Handle incorrect format
    }

    const date = new Date();
    date.setHours(hh + hours, mm + minutes, 0, 0); // Add hours and minutes

    return date.toTimeString().slice(0, 5); // Format as HH:mm
};

  const [onLoad,setOnLoad] = useState(false)
  const [usedLinks, setUsedLinks] = useState([]); // State to store used links

  const formatPayload = (data) => {
    return {
      id: store.selectedEvent ? store.selectedEvent.id : "",
      url: data?.url,
      searchForRoom: data?.searchForRoom,
      title: data?.title,
      startDate: startDate,
      endDate: data?.endDate ? new Date(data.endDate).toISOString().split('T')[0] : null,
      startTime:
    data?.startTime && data.startTime.trim() !== ""
      ? addTime(data.startTime, 5, 30)
      : "00:00",
      endTime:
        data?.endTime && data.endTime.trim() !== "" ? addTime(data.endTime, 5,40) : "23:59",
      allDay: allDay,
      isActive: true,
      inPersonEvent: inPersonEvent,
      googleMeet: data?.googleMeet,
      googleMeetOrLocationUrl: data?.googleMeetOrLocationUrl,
      attendees: attendeesId.map((attendee) => attendee.value),
      description: removeHtmlTags(data?.description),
    };
  };

  async function onSubmit(data) {
    setOnLoad(true);
    data.allDay = allDayRef.current;
    const createPayload = formatPayload(data);

    try {
      if (store.selectedEvent !== null && store.selectedEvent.title.length) {
        dispatch(updateEvent(createPayload));
      } else {
        const response = await dispatch(addEvent(createPayload)); // Wait for API call to complete
      if (response.meta.requestStatus === "fulfilled") {
        setUsedLinks([...usedLinks, data.url]); // Update used links only if API call succeeds
      } 
      }
    } catch (error) {
      console.error("Error saving event:", error);
    }
    setOnLoad(false);
    handleDialogClose();
  }
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const handleDeleteEvent = () => {
    setOpenDeleteDialog(true);
  };

  const handleDeleteConfirm = () => {
    if (store.selectedEvent) {
      dispatch(deleteEvent(store.selectedEvent?._def?.publicId));
    }
    handleDialogClose();
    setOpenDeleteDialog(false);
  };

  const handleDeleteCancel = () => {
    setOpenDeleteDialog(false);
  };

  const handleStartDate = (date) => {
    setValues((prevState) => ({
      ...prevState,
      startDate: date,
      endDate: date && date > prevState.endDate ? date : prevState.endDate,
    }));
  };

  const [url, setUrl] = useState("");

  const resetToStoredValues = useCallback(() => {
    if (store.selectedEvent !== null) {
      const event = store.selectedEvent;

      const date = new Date(store.selectedEvent.start);

      // Extract year, month, and day
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");

      // Format the date to 'YYYY-MM-DD'
      const formattedDate = `${year}-${month}-${day}`;

      // setValue("startDate", formattedDate);
      setStartDate(formattedDate);

      const time = date.toTimeString().split(" ")[0].slice(0, 5);

      dispatch(fetchEventById(event?._def?.publicId))
        .then((eventDataFetched) => {
          setEventData(eventDataFetched?.payload);
          setValue("id", eventDataFetched?.payload?.id || "");
          setValue("url", eventDataFetched?.payload?.url || "");
          setUrl(eventDataFetched?.payload?.url);
          setValue(
            "searchForRoom",
            eventDataFetched?.payload?.extendedProps?.searchForRoom || ""
          );
          setValue("startTime", eventDataFetched?.payload?.startTime || "");
          setValue("title", eventDataFetched?.payload?.title || "");
          setStartTime( eventDataFetched?.payload?.startTime);
          setValue("endDate", eventDataFetched?.payload?.endDate || "");
          setValue("endTime", eventDataFetched?.payload?.endTime || "");
          setAllDay(eventDataFetched?.payload?.allDay);
          setInPersonEvent(
            eventDataFetched?.payload?.extendedProps?.inPersonEvent
          );
          setValue(
            "googleMeet",
            eventDataFetched?.payload?.extendedProps?.googleMeet || false
          );
          setValue(
            "googleMeetOrLocationUrl",
            eventDataFetched?.payload?.extendedProps?.googleMeetOrLocationUrl ||
              ""
          );
          setValue("isActive", eventDataFetched?.payload?.isActive || false);

          //  eventDataFetched?.payload?.extendedProps?.attendees
          // check above ids in this listAttendeesOptions and form array --
          const attendeesArray =
            eventDataFetched?.payload?.extendedProps?.attendees || [];

          const matchedAttendees = attendeesArray
            .map((attendeeId) => {
              const matchedAttendee = listAttendeesOptions.find(
                (option) => option.value === attendeeId
              );
              return matchedAttendee
                ? { value: matchedAttendee.value, key: matchedAttendee.key }
                : null;
            })
            .filter((attendee) => attendee !== null);

          setAttendeesId(matchedAttendees || []);
          setValue(
            "description",
            eventDataFetched?.payload?.extendedProps?.description || ""
          );
        })
        .catch((error) => {
          console.error("Error fetching event by ID:", error);
        });
    }
  }, [setValue, store.selectedEvent, dispatch]);

  const resetToEmptyValues = useCallback(() => {
    reset();
    setAttendeesId([]);
    setStartDate(today); // Reset start date to today's date
    setStartTime("00:00"); // Reset start time to the default time
  }, [reset, today]);

  const handleStartDateChange = (date) => {
    setStartDate(date);
  };

  const handleGoogleMeet = () => {
    const url = "https://meet.google.com/landing";
    window.open(url, "_blank");
  };

  const handleCopyClick = () => {
    const url = getValues("url");
    const pattern = /^(https:\/\/)?meet.google.com\/[a-zA-Z0-9-]+$/;

    if (toastDisplay !== null) {
      toast.dismiss(toastDisplay);
    }

    if (!url) {
      setToastDisplay(toast.error("Please enter a URL."));
    } else if (!pattern.test(url)) {
      setToastDisplay(toast.error("Please enter a correct Google Meet link."));
    } else {
      navigator.clipboard
        .writeText(url)
        .then(() => {
          setToastDisplay(toast.success("Copied to clipboard"));
        })
        .catch((err) => {
          console.error("Failed to copy: ", err);
          setToastDisplay(toast.error("Failed to copy"));
        });
    }
  };

  const [toastDisplay, setToastDisplay] = useState(null);
  const handleIconHover = () => {
    if (toastDisplay !== null) {
      toast.dismiss(toastDisplay);
    }

    setToastDisplay(
      toast(
        "Click on the icon to navigate to Google Meet.\n1. Click on 'New Meeting'.\n2. Select 'Create a meeting for later'.\n3. Copy the generated link.\n4. Paste it in the field.",
        {
          autoClose: false,
        }
      )
    );
  };

  useEffect(() => {
    if (store.selectedEvent !== null) {
      resetToStoredValues();
    } else {
      resetToEmptyValues();
    }
  }, [
    addEventSidebarOpen,
    resetToStoredValues,
    resetToEmptyValues,
    store.selectedEvent,
  ]);

  const handleJoinClick = () => {
    if (url) {
      // Open the URL in a new tab
      window.open(url, "_blank");
    } else {
      // Handle the case when the URL is not available
      alert("Please enter a valid URL");
    }
  };

  return (
    <>
      <EventDialog
        addEventSidebarOpen={addEventSidebarOpen}
        handleDialogClose={handleDialogClose}
        handleSubmit={handleSubmit}
        onSubmit={onSubmit}
        store={store}
        attendeesId={attendeesId}
        setAttendeesId={setAttendeesId}
        control={control}
        errors={errors}
        user={user}
        eventData={eventData}
        listAttendeesOptions={listAttendeesOptions}
        employeesOptions={employeesOptions}
        startDate={startDate}
        setStartDate={setStartDate}
        handleStartDateChange={handleStartDateChange}
        handleJoinClick={handleJoinClick}
        usedLinks={usedLinks}
        allDay={allDay}
        setAllDay={setAllDay}
        inPersonEvent={inPersonEvent}
        setInPersonEvent={setInPersonEvent}
        isSmallScreen={isSmallScreen}
        startTime={startTime}
        handleGoogleMeet={handleGoogleMeet}
        handleIconHover={handleIconHover}
        setStartTime={setStartTime}
        getValues={getValues}
        resetToEmptyValues={resetToEmptyValues}
        handleDeleteEvent={handleDeleteEvent}
        handleCopyClick={handleCopyClick}
        onLoad={onLoad}
      />
      <>
        <ConfirmDeleteDialog
          open={openDeleteDialog}
          onClose={handleDeleteCancel}
          onConfirm={handleDeleteConfirm}
        />
      </>
    </>
  );
};

export default AddEventRightBar;
