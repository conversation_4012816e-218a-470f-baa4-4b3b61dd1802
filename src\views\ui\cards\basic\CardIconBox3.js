// ** Next Import
import Link from 'next/link'

// ** MUI Imports
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'
import { styled } from '@mui/material/styles'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

// ** Styled Components
const Illustration = styled('img')(({ theme }) => ({
  maxHeight: 250,
  marginBottom: 20
}))

const CardIconBox3 = () => {
  return (
    <Card>
      <CardContent
        sx={{
          display: 'flex',
          textAlign: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          p: theme => `${theme.spacing(9.75, 5, 9.25)} !important`
        }}
      >
        <CustomAvatar skin='light' sx={{ width: 50, height: 50, mb: 2.25 }}>
          <Icon icon='tabler:help' fontSize='2rem' />
        </CustomAvatar>
        <Typography variant='h6' sx={{ mb: 6 }}>
          Title3
        </Typography>
        <Button variant='outlined' component={Link} href='/'>
          Read More
        </Button>
      </CardContent>
    </Card>
  )
}

export default CardIconBox3
