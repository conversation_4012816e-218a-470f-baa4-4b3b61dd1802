import React, { Fragment } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Table,
  TableBody,
  TableRow,
  TableCell,
  TableContainer,
  Box,
  IconButton,
} from "@mui/material";
import { useTheme } from "@emotion/react";
import { Icon } from "@iconify/react";

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" },
  padding: "4px",
  textAlign: "right",
  verticalAlign: "middle",
  textIndent: { lg: "80px", md: "80px", sm: "100px", xs: "0" },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" },
  height: "10px",
};

const field = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  color: "#108A00",
  fontSize: "14px",
  lineHeight: "1.2",
};

const ViewProfileDialog = ({ open, onClose, data, formatCategory }) => {
  const theme = useTheme();
  return (
    <Dialog open={open} onClose={onClose} fullWidth>
      <DialogTitle
        sx={{
          position: "sticky",
          top: 0,
          zIndex: 10,
          backgroundColor: "background.paper",
          borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 7)} !important`,
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          fontSize: { xs: 19, md: 20 },
          height: "50px",
        }}
        textAlign={"center"}
      >
        View Notifications
        <Box sx={{ top: "9px", right: "10px" }}>
          <IconButton
            size="small"
            onClick={onClose}
            sx={{
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#66BB6A",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        <TableContainer>
          <Table>
            <TableBody
              sx={{
                "& .MuiTableCell-root": {
                  p: `${theme.spacing(1.35, 1.125)} !important`,
                },
              }}
            >
              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Message:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {data?.levelOneContent || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Type:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {formatCategory(data?.type) || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Category:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {data?.metaData?.data?.Category || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Created On:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {new Date(data?.createdOn).toLocaleString() || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Updated On:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {new Date(data?.updatedOn).toLocaleString() || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Created By:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {data?.createdBy || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Updated By:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {data?.updatedBy || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Status:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  <Typography style={fieldValueStyle}>
                    {data?.isActive ? "Active" : "Inactive" || "-"}
                  </Typography>
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Recipient Status:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  {data?.recipients?.length > 0 ? (
                    data.recipients.map((recipient, index) => (
                      <Fragment key={index}>
                        <Typography style={fieldValueStyle}>
                          {recipient?.status ? "Read" : "Yet To Read" || "-"}
                        </Typography>
                        {index < data.recipients?.length - 1 && <span>, </span>}
                      </Fragment>
                    ))
                  ) : (
                    <Typography style={fieldValueStyle}>
                      No Recipients
                    </Typography>
                  )}
                </TableCell>
              </TableRow>

              <TableRow>
                <TableCell sx={tablecellLabelStyle}>
                  <Typography style={field}>Notifications&nbsp;Sent&nbsp;Count:</Typography>
                </TableCell>
                <TableCell sx={tablecellValueStyle}>
                  {data?.recipients?.length > 0 ? (
                    data.recipients.map((recipient, index) => (
                      <Fragment key={index}>
                        <Typography style={fieldValueStyle}>
                          {recipient?.sentCount || "-"}
                        </Typography>
                        {index < data.recipients?.length - 1 && <span>, </span>}
                      </Fragment>
                    ))
                  ) : (
                    <Typography style={fieldValueStyle}>
                      No Notifications Sent
                    </Typography>
                  )}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions
        sx={{
          position: "sticky",
          bottom: 0,
          zIndex: 10,
          backgroundColor: "background.paper",
          justifyContent: "end",
          borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          p: (theme) => `${theme.spacing(1.75, 7)} !important`,
        }}
      >
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ViewProfileDialog;
