// ** Next Import
import Link from 'next/link'

// ** MUI Components
import Button from '@mui/material/Button'
import { styled } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'

// ** Layout Import
import BlankLayout from 'src/@core/layouts/BlankLayout'
import { useRBAC } from "src/pages/permission/RBACContext";


import { useRouter } from "next/router";
import { useEffect } from 'react'

// ** Demo Imports
import FooterIllustrations from 'src/views/pages/misc/FooterIllustrations'

// ** Styled Components
const BoxWrapper = styled(Box)(({ theme }) => ({
  [theme.breakpoints.down('md')]: {
    width: '90vw'
  }
}))


const search = () => {

  const { can,rbacRoles } = useRBAC();
  const router = useRouter();


  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0){
      if (!can('advancedSearch_READ')) {
        router.push("/401");
      }
    }  
  }, [rbacRoles]);

  return (
    <>
    {can('advancedSearch_READ') &&
      <Box className='content-center'>
      <Box sx={{ p: 5, display: 'flex', flexDirection: 'column', alignItems: 'center', textAlign: 'center' }}>
        <BoxWrapper>
        <Typography sx={{ mb: 6, color: 'text.secondary',fontSize: '2.5rem' }}>
            Work in progress
          </Typography>
        </BoxWrapper>
       
      </Box>
      <FooterIllustrations />
    </Box>
}
    </> 
  )
}


export default search;
