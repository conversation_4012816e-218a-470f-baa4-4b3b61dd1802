// ** MUI imports
import { styled } from '@mui/material/styles'

import Box from '@mui/material/Box'

// ** Styled Components
const BoxWrapper = styled(Box)(({ theme }) => ({
    margin: 'auto',
    position: 'relative',
    [theme.breakpoints.up('md')]: {
        width: '100%',
        padding: theme.spacing(6)
    },
    [theme.breakpoints.up(1300)]: {
        width: '100%',
        padding: theme.spacing(4, 10)
    },
    [theme.breakpoints.up(1440)]: {
        width: '1366px',
        padding: theme.spacing(6)
    },
    [theme.breakpoints.down('md')]: {
        width: '100%',
        padding: theme.spacing(4, 0)
    },
}))

export default BoxWrapper