# Semgrep configuration for React/Next.js project
rules:
  # Security rules
  - id: javascript.crypto.insecure-random
    pattern-either:
      - pattern: Math.random()
      - pattern: crypto.pseudoRandomBytes(...)
    message: |
      Using insecure random number generation. Use crypto.randomBytes() or
      crypto.getRandomValues() for cryptographic purposes.
    languages: [javascript, typescript]
    severity: WARNING
    metadata:
      category: security
      technology:
        - javascript
        - react
        - nextjs

  - id: javascript.cors.allow-all-origins
    patterns:
      - pattern-either:
          - pattern: res.header("Access-Control-Allow-Origin", "*")
          - pattern: res.setHeader("Access-Control-Allow-Origin", "*")
          - pattern: 'cors({origin: "*"})'
    message: |
      CORS is configured to allow all origins (*). This can be a security risk
      in production. Consider restricting to specific domains.
    languages: [javascript, typescript]
    severity: WARNING
    metadata:
      category: security

  - id: javascript.lang.security.audit.sqli.node-postgres-sqli
    pattern-either:
      - pattern: pool.query($X + $Y)
      - pattern: client.query($X + $Y)
      - pattern: 'pool.query(`${...}`)'
      - pattern: 'client.query(`${...}`)'
    message: |
      Possible SQL injection. Use parameterized queries instead of string
      concatenation.
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      category: security

  - id: javascript.express.security.audit.xss.direct-response-write
    pattern-either:
      - pattern: res.write($INPUT)
      - pattern: res.end($INPUT)
      - pattern: res.send($INPUT)
    pattern-not:
      - pattern: res.write("...")
      - pattern: res.end("...")
      - pattern: res.send("...")
    message: |
      Data is written directly to HTTP response. This could be vulnerable
      to XSS if user input is included. Ensure proper sanitization.
    languages: [javascript, typescript]
    severity: WARNING
    metadata:
      category: security

  # React-specific rules
  # Note: dangerouslySetInnerHTML rule disabled due to pattern parsing issues
  # This is typically caught by ESLint security plugins

  # Note: React missing key prop rule disabled due to false positives
  # Most modern linters like ESLint already catch this issue effectively

  # Code quality rules
  - id: javascript.lang.correctness.useless-assign
    pattern-either:
      - pattern: |
          $X = $Y;
          $X = $Z;
      - pattern: |
          var $X = $Y;
          $X = $Z;
      - pattern: |
          let $X = $Y;
          $X = $Z;
    message: |
      Variable is assigned but the value is immediately overwritten.
      The first assignment is useless.
    languages: [javascript, typescript]
    severity: INFO
    metadata:
      category: correctness

  - id: javascript.lang.security.audit.hardcoded-secret
    pattern-either:
      - pattern: const password = "..."
      - pattern: const secret = "..."
      - pattern: const apiKey = "..."
      - pattern: const api_key = "..."
      - pattern: const token = "..."
    message: |
      Potential hardcoded secret detected. Consider using environment
      variables or a secure secret management system.
    languages: [javascript, typescript]
    severity: WARNING
    metadata:
      category: security

  - id: javascript.express.security.audit.express-data-exfiltration
    pattern-either:
      - pattern: console.log($REQ.body)
      - pattern: console.log($REQ.query)
      - pattern: console.log($REQ.params)
      - pattern: console.error($REQ.body)
      - pattern: console.error($REQ.query)
      - pattern: console.error($REQ.params)
    message: |
      Request data logged to console. This could expose sensitive information
      in logs. Consider sanitizing or removing in production.
    languages: [javascript, typescript]
    severity: INFO
    metadata:
      category: security 