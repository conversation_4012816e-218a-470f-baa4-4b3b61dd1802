import React from 'react';
import {
    Box,
    Typography,
    Card,
    CardContent,
    Avatar,
    CircularProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import CategoryOutlinedIcon from '@mui/icons-material/CategoryOutlined';

const DonationHeadsCard = ({ 
    title = "Total Donation Heads",
    value = 0,
    loading = false,
    icon = <CategoryOutlinedIcon />,
    color = 'info'
}) => {
    const theme = useTheme();
    const cardColor = theme.palette[color]?.main || theme.palette.info.main;

    const formatValue = (val) => {
        return val?.toString() || '0';
    };

    return (
        <Card sx={{ 
            position: 'relative', 
            overflow: 'visible',
            borderRadius: '8px',
            backgroundColor: 'white',
            minHeight: '120px'
            }} 
            elevation={1}
        >
            <CardContent sx={{ p: 3 }}>
                <Box>
                    <Typography 
                        variant="body1" 
                        color="text.secondary" 
                        sx={{ 
                            mb: 1,
                            fontSize: '0.875rem',
                            fontWeight: 500
                        }}
                    >
                        {title}
                    </Typography>
                    <Typography 
                        variant="h4" 
                        component="div" 
                        sx={{ 
                            fontWeight: 600,
                            color: '#000',
                            fontSize: '1.5rem'
                        }}
                    >
                        {loading ? <CircularProgress size={20} /> : formatValue(value)}
                    </Typography>
                </Box>
                <Avatar 
                    sx={{ 
                        position: 'absolute',
                        top: 16,
                        right: 16,
                        bgcolor: '#F4F7FE',
                        color: cardColor,
                        width: 48,
                        height: 48
                    }}
                >
                    {icon}
                </Avatar>
            </CardContent>
        </Card>
    );
};

export default DonationHeadsCard;