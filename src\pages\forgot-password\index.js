import {
  <PERSON><PERSON>,
  Box,
  <PERSON>ton,
  CircularProgress,
  Container,
  <PERSON>rid,
  Snackbar,
  TextField,
  Typography,
} from "@mui/material";
// ** Layout Import
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
// ** Next Imports
import { useAuth } from "src/hooks/useAuth";
import BlankLayout from "src/@core/layouts/BlankLayout";
import { useRouter } from "next/router";
import { useState } from "react";
import ResetPage from "./ResetPage";

const ForgotPasswordPage = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();
  const auth = useAuth();
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [emailSent, setEmailSent] = useState(false);
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");

  const [loading, setLoading] = useState(false);

  const handleLogin = () => {
    router.push("/login");
  };

  const handleFailure = (res) => {
    if (res?.response.status === 400) {
      setToastMessage("No user found. Please sign up");
      setShowToast(true);
    } else {
      setToastMessage("Failed to Send Email. Please try again later.");
      setShowToast(true);
    }
  };

  async function handleResetPassword() {
    setLoading(true);
    try {
      const response = await auth.postForgotPassword(email, handleFailure);
      if (response === true) {
        router.replace("/forgot-password/email")
      }
    } catch (error) {
      console.error("forgot password failed:", error);
      handleFailure(response);
      setEmailSent(false);
    }
    setLoading(false);
  }
  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  return (
    <>
      
        <Container
          maxWidth="xs"
          sx={{
            height: "90vh",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "start",
            boxShadow: 3,
            p: 8,
            mt: 8,
            mb: 8,
            borderRadius: 6,
            bgcolor: "white",
            position: "relative",
            overflowY: "auto",
            flexGrow: 1,
            transform: {
              xs: "scale(0.9)", // 90% of original size on extra-small screens (mobile)
              sm: "none", // Normal size for tablets and above
            },
          }}
        >
          <Box
            sx={{
              width: "80%",
              position: "absolute",
              mr: 7,
              "&:hover": {
                cursor: "pointer",
              },
            }}
          >
            <Icon
              icon="ooui:arrow-previous-ltr"
              fontSize={20}
              onClick={handleLogin}
            />
          </Box>
          <Box
            sx={{
              mt: 8,
            }}
          >
            <Grid
              container
              spacing={3}
              sx={{
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Grid item xs={8.5}>
                <Typography
                  variant="h5"
                  fontWeight={500}
                  color="primary"
                  gutterBottom
                >
                  Forgot Your Password
                </Typography>
              </Grid>
              <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
                <Typography variant="body2" sx={{ my: 2 }}>
                  Enter your email address and <br />
                  we’ll send you a link to reset your password
                </Typography>
              </Grid>
              <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Enter a valid email address",
                    },
                    minLength: {
                      value: 8,
                      message: "Email must be at least 8 characters long",
                    },
                    maxLength: {
                      value: 100,
                      message: "Email cannot be longer than 100 characters",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Email*"
                      size="small"
                      InputLabelProps={{
                        shrink: true,
                        sx: { fontSize: "1rem" },
                      }}
                      helperText={errors.email?.message}
                      error={Boolean(errors.email)}
                      placeholder="<EMAIL>"
                      fullWidth
                      inputProps={{
                        minLength: 8,
                        maxLength: 100,
                      }}
                      onChange={(event) => {
                        if (event.target.value.length <= 100) {
                          field.onChange(event);
                          setEmail(event.target.value);
                        }
                      }}
                    />
                  )}
                />
              </Grid>
            </Grid>
          </Box>
          <Box
            sx={{
              width: "80%",
              py: 2,
              textAlign: "center",
              mt: "auto",
            }}
          >
            <>
              <Button
                variant="contained"
                color="primary"
                sx={{ mt: 4, width: "100%" }}
                onClick={handleSubmit(handleResetPassword)}
              >
                {loading ? (
                  <CircularProgress color="inherit" size={24} />
                ) : (
                  "Submit"
                )}
              </Button>
            </>
          </Box>
        </Container>
     
      <Snackbar
        open={showToast}
        autoHideDuration={5000} // Toast will be visible for 5 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity={"error"}
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "12px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

ForgotPasswordPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
ForgotPasswordPage.guestGuard = true;

export default ForgotPasswordPage;
