// pages/oauth2/redirect.js

import React from 'react';
import OAuth2RedirectHandler from './OAuth2RedirectHandler';
import BlankLayout from 'src/@core/layouts/BlankLayout';




// Access your environment variable

const OAuth2RedirectPage = () => {
  return <OAuth2RedirectHandler />;
};

OAuth2RedirectPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
OAuth2RedirectPage.guestGuard = true;

export default OAuth2RedirectPage;
