// ** MUI Components
import { styled } from '@mui/material/styles'
import Grid from '@mui/material/Grid'
import MuiLink from '@mui/material/Link'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import IconTextBox from './IconTextBox'
import Button from '@mui/material/Button'

const Img = styled('img')(({ theme }) => ({
  [theme.breakpoints.down('lg')]: {
    height: 330,
    marginTop: theme.spacing(5)
  },
  [theme.breakpoints.up('lg')]: {
    height: 340,
    marginTop: theme.spacing(5)
  },
  [theme.breakpoints.down('md')]: {
    height: 230
  }
}))

const ImageTextSlide = props => {
  // ** Props
  const { title, subtitle, src } = props

  return (
    <>
      <Box
        sx={{
          border: theme => `1px solid ${theme.palette.divider}`,
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          alignItems: 'center',
          width: { xs: '100vw', md: '85vw' }
        }}
      >
        <Box
          sx={{
            p: 8,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'start',
            justifyContent: 'center',
            minWidth: '60%',
            backgroundColor: 'rgb(40 199 111 / 16%)'
          }}
        >
          <Typography variant='h3' sx={{ mb: 6, fontWeight: '700' }}>
            {title}
          </Typography>
          <Typography variant='h6' sx={{ mb: 3.5, color: '#777' }}>
            {subtitle || null}
          </Typography>
          <IconTextBox
            icon={'tabler:checkbox'}
            title={'Independent market place to appoint agencies for specific tasks'}
          />
          <IconTextBox icon={'tabler:user-check'} title={'Guidance to societies on realistic grounds'} />
          <IconTextBox
            icon={'tabler:user'}
            title={'Guidance on standard Industry practices fees and services in Industry'}
          />
          <IconTextBox icon={'tabler:layout-grid'} title={'Enable Transpernt and poistive communication'} />
          <IconTextBox icon={'tabler:chart-pie'} title={'Safe and secure payments'} />
          <IconTextBox icon={'tabler:shield'} title={'Partner with Houzer for end to end support'} />
          <Button variant='contained' sx={{ mt: 4 }}>
            Sign up for free
          </Button>
        </Box>
        <Box sx={{ minWidth: '40%', display: 'flex', justifyContent: 'center' }}>
          <Img alt='image' src={src} />
        </Box>
        {/* <Img height='500' alt='error-illustration' src='/images/pages/404.png' /> */}
      </Box>
    </>
  )
}

export default ImageTextSlide
