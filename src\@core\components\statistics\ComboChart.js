import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const ComboChart = () => {
  const state = {
    series: [
      {
        name: 'Service Providers',
        type: 'column',
        data: [12, 15, 20, 25, 30, 35, 40, 45]  // Example data representing the number of service providers
      },
      {
        name: 'Service Requests',
        type: 'column',
        data: [10, 20, 18, 30, 25, 28, 35, 40]  // Example data representing the number of service requests
      },
      {
        name: 'Satisfaction Rating',
        type: 'line',
        data: [3.5, 4.0, 4.2, 4.5, 4.3, 4.6, 4.7, 4.8]  // Example data representing the average satisfaction rating
      }
    ],
    options: {
      chart: {
        height: 350,
        type: 'line',
        stacked: false
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        width: [1, 1, 4]
      },
      title: {
        text: 'Service Provider Metrics',  // Updated title
        align: 'left',
        offsetX: 20
      },
      xaxis: {
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug'],  // Example months
      },
      yaxis: [
        {
          seriesName: 'Service Providers',
          axisTicks: {
            show: true,
          },
          axisBorder: {
            show: true,
            color: '#008FFB'
          },
          labels: {
            style: {
              colors: '#008FFB',
            }
          },
          title: {
            text: "Number of Providers",
            style: {
              color: '#008FFB',
            }
          },
          tooltip: {
            enabled: true
          }
        },
        {
          seriesName: 'Service Requests',
          opposite: true,
          axisTicks: {
            show: true,
          },
          axisBorder: {
            show: true,
            color: '#00E396'
          },
          labels: {
            style: {
              colors: '#00E396',
            }
          },
          title: {
            text: "Number of Requests",
            style: {
              color: '#00E396',
            }
          },
        },
        {
          seriesName: 'Satisfaction Rating',
          opposite: true,
          axisTicks: {
            show: true,
          },
          axisBorder: {
            show: true,
            color: '#FEB019'
          },
          labels: {
            style: {
              colors: '#FEB019',
            },
          },
          title: {
            text: "Rating (out of 5)",
            style: {
              color: '#FEB019',
            }
          }
        },
      ],
      tooltip: {
        fixed: {
          enabled: true,
          position: 'topLeft', // topRight, topLeft, bottomRight, bottomLeft
          offsetY: 30,
          offsetX: 60
        },
      },
      legend: {
        horizontalAlign: 'left',
        offsetX: 40
      }
    },
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="line" height={350} />
      </div>
    </div>
  );
};

export default ComboChart;
