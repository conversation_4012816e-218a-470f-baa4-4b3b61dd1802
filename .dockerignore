# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
.next/
out/
dist/

# Development files
.git
.gitignore
README.md
.env*.local
.env.local

# Testing
coverage/
.nyc_output
*.lcov

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files (exclude only .local versions, keep main env files for Docker)
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo

# OS files
Thumbs.db

# Docker files
Dockerfile*
docker-compose*
.dockerignore

# Documentation
docs/
*.md

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# Temporary folders
tmp/
temp/

# SonarQube files
.scannerwork/
sonar-project.properties

# Build artifacts
*.tgz
*.tar.gz 