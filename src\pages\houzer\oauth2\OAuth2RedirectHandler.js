import axios from "axios";
import React, { useEffect, useContext, useState } from "react";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/AuthContext";
import jwt from "jsonwebtoken";
import {
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogContentText,
  Button,
  Box,
  Menu,
  MenuItem,
} from "@mui/material";
import BlankLayout from "src/@core/layouts/BlankLayout";

const jwtConfig = {
  secret: process.env.NEXT_PUBLIC_JWT_SECRET,
  expirationTime: process.env.NEXT_PUBLIC_JWT_EXPIRATION,
  refreshTokenSecret: process.env.NEXT_PUBLIC_JWT_REFRESH_TOKEN_SECRET,
};

const OAuth2RedirectHandler = () => {
  const router = useRouter();
  const { fetchProfile } = useContext(AuthContext);
  const [dialogOpen, setDialogOpen] = useState(false); // State to control dialog visibility
  const [dialogMessage, setDialogMessage] = useState(""); // State to store the dialog message
  const [loginExist, setLoginExist] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  useEffect(() => {
    const handleAuthTokens = async () => {
      if (!router.isReady) return;

      const { accessToken, refreshToken, error } = router.query;
      if (error) {
        let message = "An error occurred. Please try again.";
        if (error.includes("Individual Already Exists")) {
          message = "A user with this account already exists. Please log in";
        } else if (error === "Individual Not Found") {
          setLoginExist(true);
          message =
            "No user found with this account. Please go to the home page to register.";
        }
        setDialogMessage(message);
        setDialogOpen(true);
        return;
      }

      if (!accessToken || !refreshToken) {
        console.error("Access or refresh token missing, redirecting to login.");
        router.push("/login");
        return;
      }

      try {
        jwt.verify(accessToken, Buffer.from(jwtConfig.secret, "base64"), {
          algorithms: ["HS512"],
        });
        await new Promise((resolve) => setTimeout(resolve, 3000));
        window.localStorage.setItem("accessToken", accessToken);
        window.localStorage.setItem("refreshToken", refreshToken);

        await fetchProfile(accessToken);
        router.replace("/dashboard").then(() => router.reload());
      } catch (error) {
        console.error(
          "Error validating tokens or fetching profile, redirecting to login:",
          error
        );
        router.push("/login");
      }
    };

    handleAuthTokens();
  }, [router, fetchProfile]);

  const handleOpenMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (role) => {
    localStorage.setItem("role", role);
    setAnchorEl(null);
    router.push(`/register?role=${role}`);
    handleCloseMenu();
  };

  return (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100vh",
        }}
      >
        <CircularProgress />
      </div>
      {/* Dialog for user authentication status */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogTitle>Account Info </DialogTitle>

          <DialogContent>
            <DialogContentText>{dialogMessage}</DialogContentText>
          </DialogContent>

          <DialogActions>
            {loginExist ? (
              <Button onClick={handleOpenMenu} color="primary">
                Sign Up
              </Button>
            ) : (
              <Button onClick={() => router.push("/login")} color="primary">
                Login
              </Button>
            )}
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleCloseMenu}
              anchorReference="anchorPosition"
              anchorPosition={{
                top: anchorEl ? anchorEl.getBoundingClientRect().bottom : 0,
                left: anchorEl ? anchorEl.getBoundingClientRect().left : 0,
              }}
            >
              <MenuItem
                onClick={() => handleMenuItemClick("service-provider")}
                style={{ padding: "3px 6px" }}
              >
                Service Provider
              </MenuItem>
              <MenuItem
                onClick={() => handleMenuItemClick("society")}
                style={{ padding: "3px 6px" }}
              >
                Society Member
              </MenuItem>
            </Menu>

            <Button onClick={() => router.push("/")} color="secondary">
              Go to Home
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

OAuth2RedirectHandler.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
OAuth2RedirectHandler.guestGuard = true;

export default OAuth2RedirectHandler;
