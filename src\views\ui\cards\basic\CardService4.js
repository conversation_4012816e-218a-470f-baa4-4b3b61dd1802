// ** Next Import
import Link from 'next/link'

// ** MUI Imports
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import CardActions from '@mui/material/CardActions'

const CardService4 = () => {
  return (
    <Card>
      {/* <Card sx={{ background: 'rgb(234 84 85 / 16%)' }}> */}
      <CardHeader title='Service4' />
      <CardContent>
        <Typography variant='body2'>
          If you’re in the market for new desktops, notebooks, or PDAs, there are a myriad of choices. Here’s a rundown
          of some of the best systems available.
        </Typography>
      </CardContent>
      <CardActions className='card-action-dense' sx={{ justifyContent: 'center' }}>
        <Button sx={{ mb: 5 }} component={Link} variant='contained' href='/'>
          Read More
        </Button>
      </CardActions>
    </Card>
  )
}

export default CardService4
