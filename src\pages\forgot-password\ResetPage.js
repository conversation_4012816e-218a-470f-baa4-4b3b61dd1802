import {
  <PERSON><PERSON>,
  Box,
  Container,
  Grid,
  <PERSON><PERSON><PERSON>bar,
  Typography,
} from "@mui/material";
// ** Layout Import
import { useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
// ** Next Imports
import { useTheme } from "@mui/material/styles";
import { useEffect, useState } from "react";
import { useAuth } from "src/hooks/useAuth";

const ResetPage = ({ setEmailSent, email }) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const auth = useAuth();
  const theme = useTheme();

  const [countdown, setCountdown] = useState(0);
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");
  const [sentStatus, setSentStatus] = useState(false);
  useEffect(() => {
    if (countdown > 0) {
      const timerId = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timerId);
    }
  }, [countdown]);

  const handleReset = () => {
    setEmailSent(false);
  };

  const handleFailure = () => {
    setSentStatus(true);
    setToastMessage("Failed to ReSend Email. Please try again later.");
    setShowToast(true);
  };

  const handleSuccess = () => {
    setSentStatus(false);
    setToastMessage(
      " An email with a link to reset your password was sent to your mail."
    );
    setShowToast(true);
  };
  async function handleResend() {
    try {
      const response = await auth.postForgotPassword(email, handleFailure);
      handleSuccess();
    } catch (error) {
      console.error("Forgot password failed:", error);
      handleFailure();
    }
  }

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  return (
    <>
      <Container
        maxWidth="xs"
        sx={{
          height: "90vh",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "start",
          boxShadow: 3,
          p: 8,
          mt: 8,
          mb: 8,
          borderRadius: 6,
          bgcolor: "white",
          position: "relative",
          overflowY: "auto",
          flexGrow: 1,
        }}
      >
        <Box
          sx={{
            width: "80%",
            position: "absolute",
            mr: 7,
            "&:hover": {
              cursor: "pointer",
            },
          }}
        >
          <Icon
            icon="ooui:arrow-previous-ltr"
            fontSize={20}
            onClick={handleReset}
          />
        </Box>
        <Box
          sx={{
            mt: 8,
          }}
        >
          <Grid
            container
            spacing={3}
            sx={{
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Grid item xs={8.5}>
              <Typography
                variant="h5"
                fontWeight={500}
                color="primary"
                gutterBottom
              >
                Check your inbox
              </Typography>
            </Grid>
            <Grid item xs={8.5} sx={{ marginBottom: 2 }}>
              <Typography variant="body2" sx={{ my: 2 }}>
                An email with a link to reset your password was <br />
                sent to the email address associated with your account.
              </Typography>
            </Grid>
          </Grid>
        </Box>
        <Box
          sx={{
            width: "80%",
            py: 2,
            textAlign: "center",
            mt: "auto",
          }}
        >
          <>
            <Typography variant="body2" sx={{ mt: 2 }}>
              Didn't get an email ?{" "}
              {countdown > 0 ? (
                <Typography
                  variant="body1"
                  sx={{
                    marginTop: "2px",
                    marginBottom: "10px",
                    color: "primary.main",
                  }}
                >
                  Resend Email in: {countdown}s
                </Typography>
              ) : (
                <span
                  style={{
                    cursor: "pointer",
                    color: theme.palette.primary.main,
                    textDecoration: "underline",
                  }}
                  onClick={handleResend}
                >
                  Resend
                </span>
              )}
            </Typography>
          </>
        </Box>
      </Container>
      <Snackbar
        open={showToast}
        autoHideDuration={5000} // Toast will be visible for 5 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity={sentStatus ? "error" : "success"}
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "12px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
    </>
  );
};

export default ResetPage;
