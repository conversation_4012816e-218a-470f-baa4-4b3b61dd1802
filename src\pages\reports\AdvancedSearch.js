import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    clearAllFilters,
    onApplyFilters,
  } = props;

  const {
    setValue,
    control,
    reset,
    handleSubmit,
  } = useForm();

  useEffect(() => {
    const filterMap = new Map(selectedFilters?.map((filter) => [filter.key, filter.value]));

    setValue("donationName", filterMap.get("donationName") || "");
    setValue("mobileNumber", filterMap.get("mobileNumber") || "");
    setValue("emailAddress", filterMap.get("emailAddress") || "");
    setValue("panNumber", filterMap.get("panNumber") || "");
    setValue("aadharNumber", filterMap.get("aadharNumber") || "");
  }, [selectedFilters, setValue]);

  const handleCancel = () => {
    reset();
    setSearchingState(false);
    clearAllFilters();
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = (data) => {
    const filters = [];

    if (data?.donationName) {
      filters.push({ key: "donationName", label: "Donation Name", value: data.donationName });
    }
    if (data?.mobileNumber) {
      filters.push({ key: "mobileNumber", label: "Mobile Number", value: data.mobileNumber });
    }
    if (data?.emailAddress) {
      filters.push({ key: "emailAddress", label: "Email Address", value: data.emailAddress });
    }
    if (data?.panNumber) {
      filters.push({ key: "panNumber", label: "PAN Number", value: data.panNumber });
    }
    if (data?.aadharNumber) {
      filters.push({ key: "aadharNumber", label: "Aadhar Number", value: data.aadharNumber });
    }

    onApplyFilters(filters);
    setSearchingState(true);
    toggle();
  };

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
        <Header
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            position: "relative"
          }}
        >
          <Typography variant="h5" sx={{ ml: 3 }}>
            Advanced Search
          </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>

        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems="center">
              {/* Donation Name */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="donationName"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Donation Name"
                        placeholder="Enter Donation Name"
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* Mobile Number */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="mobileNumber"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Mobile Number"
                        placeholder="Enter Mobile Number"
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* Email Address */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="emailAddress"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Email Address"
                        placeholder="Enter Email Address"
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* PAN Number */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="panNumber"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="PAN Number"
                        placeholder="Enter PAN Number"
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* Aadhar Number */}
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <Controller
                    name="aadharNumber"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label="Aadhar Number"
                        placeholder="Enter Aadhar Number"
                        InputLabelProps={{ shrink: true }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="contained" onClick={handleSubmit(handleApply)} sx={{ mr: 4 }}>
            Apply
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default AdvancedSearch;
