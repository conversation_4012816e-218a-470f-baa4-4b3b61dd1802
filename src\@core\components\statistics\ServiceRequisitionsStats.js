import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>board<PERSON>ist, FaCalendarAlt, FaTasks, FaCheckCircle, FaHourglassHalf } from "react-icons/fa";
import { Box, Typography, Grid, Paper, Card } from "@mui/material";
import { BiLineChart } from "react-icons/bi";
import { IoAnalyticsOutline } from "react-icons/io5";

const ServiceRequisitionsStats = () => {
  const stats = [
    {
      actor: "Daily Requisitions",
      icon: <FaClipboardList size={32} color="#1abc9c" />,
      value: 25,
    },
    {
      actor: "Monthly Requisitions",
      icon: <FaCalendarAlt size={32} color="#3498db" />,
      value: 750,
    },
    {
      actor: "Yearly Requisitions",
      icon: <FaTasks size={32} color="#f39c12" />,
      value: 9000,
    },
    {
      actor: "Completed Requisitions",
      icon: <FaCheckCircle size={32} color="#2ecc71" />,
      value: 6400,
    },
    {
      actor: "Pending Requisitions",
      icon: <FaHourglassHalf size={32} color="#ff9800" />,
      value: 2600,
    },
  ];

  return (
    <Card sx={{ mb: 2, p: 2, borderRadius: 2, boxShadow: 2 }}>
      <Box sx={{ mb: 1 }}>
        {/* Header Section */}
        <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
          <IoAnalyticsOutline size={20} color="#2ecc71" style={{ marginRight: "8px" }} />
          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
            Service Requisitions Overview
          </Typography>
        </Box>

        {/* Statistics Grid */}
        <Grid container spacing={3}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={2.4} key={index}>
              <Paper
                elevation={3}
                sx={{
                  p: 2,
                  textAlign: "center",
                  borderRadius: 2,
                  backgroundColor: "#f9f9f9",
                }}
              >
                {/* Icon */}
                <Box sx={{ mb: 1 }}>{stat.icon}</Box>

                {/* Actor Name */}
                <Typography
                  variant="body1"
                  sx={{ fontWeight: "bold", color: "#555", mb: 0.5 }}
                >
                  {stat.actor}
                </Typography>

                {/* Value */}
                <Typography
                  variant="h5"
                  sx={{ fontWeight: "bold", color: "#333", mb: 0.5 }}
                >
                  {stat.value}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Card>
  );
};

export default ServiceRequisitionsStats;
