import {
  <PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>r,
  <PERSON>rid,
  <PERSON>u,
  <PERSON>u<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useEffect, useState } from "react";
import Icon from "src/@core/components/icon";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import axios from "axios";
import CustomAvatar from "src/@core/components/mui/avatar";
import FallbackSpinner from "src/@core/components/spinner";
import { useForm } from "react-hook-form";
import { Box } from "@mui/system";
import { useRBAC } from "src/pages/permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";
import CustomChip from "src/@core/components/mui/chip";
import { format } from "date-fns";
import ViewProfileDialog from "./ViewProfileDialog";
const userStatusObj = {
  true: "Active",
  false: "InActive",
};
export const formatCategory = (category) => {
  if (!category) return "";
  return category
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};


const Notifications = () => {
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };
  const [anchorEl, setAnchorEl] = useState(null);
  const [viewProfileDialogOpen, setViewProfileDialogOpen] = useState(false);
  const handleMenuClick = (event, params) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget); // Set the anchor element to the clicked icon
    setCurrentRow(params.row); // Save the row details for later actions
  };
  const handleMenuClose = () => {
    setAnchorEl(null); // Close menu
  };
  const onClickViewProfile = () => {
    setViewProfileDialogOpen(true);
    handleMenuClose(); // Close menu after action
  };
  const handleDialogClose = () => {
    setViewProfileDialogOpen(false);
    setCurrentRow(null);
  };
  
  // Define columns
  const columns = [
    {
      field: "levelOneContent",
      minWidth: 115,
      headerName: "Message",
      flex: 0.4,
      renderCell: (params) => (
        <Tooltip title={params.value}>
          <span>{params.value}</span>
        </Tooltip>
      ),
    },
    {
      field: "type",
      minWidth: 80,
      headerName: "Type",
      flex: 0.15,
      renderCell: (params) => (
        <Tooltip title={formatCategory(params.value)}>
          <span>{formatCategory(params.value)}</span>
        </Tooltip>
      ),
    },
    {
      field: "Category",
      minWidth: 135,
      headerName: "Category",
      flex: 0.15,
      renderCell: ({row}) => (
        <Tooltip title={row.metaData.data.Category}>
          <span>{row.metaData.data.Category}</span>
        </Tooltip>
      ),
    },
    {
      field: "createdOn",
      minWidth: 115,
      headerName: "Created On",
      flex: 0.01,
      renderCell: (params) => (
        <Tooltip
          title={format(new Date(params.value), "MMMM dd, yyyy HH:mm:ss")}
        >
          <span>
            {format(new Date(params.value), "MMMM dd, yyyy HH:mm:ss")}
          </span>
        </Tooltip>
      ),
    },
    {
      field: "createdBy",
      minWidth: 135,
      headerName: "Created By",
      flex: 0.15,
      renderCell: ({row}) => (
        <Tooltip title={row.createdBy}>
          <a
            href={`mailto:${row.createdBy}`}
            style={{ textDecoration: "none", color: "blue" }}
          >
            {row.createdBy}
          </a>
        </Tooltip>
      ),
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.11,
      minWidth: 80,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },

    {
      field: "actions",
      headerName: "Actions",
      width: 100,
      sortable: false,
      renderCell: (params) => (
        <>
          <CustomAvatar
            skin="light"
            variant="rounded"
            sx={{
              mr: { xs: 2, lg: 4 },
              width: 34,
              height: 34,
              cursor: "pointer",
            }}
            onClick={(event) => handleMenuClick(event, params)}
          >
            <Icon icon="bi:three-dots-vertical" />
          </CustomAvatar>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={onClickViewProfile}>View</MenuItem>
          </Menu>
        </>
      ),
    },
  ].filter(Boolean); // Filter out null values if the condition is false

  // Use States
  const {
    formState: { errors },
  } = useForm();

  const [userList, setUserList] = useState([]);
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [currentRow, setCurrentRow] = useState("");

  useEffect(() => {
    fetchNotifications(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const fetchNotifications = async (currentPage, currentPageSize, searchKeyword) => {
    setLoading(true);

    const url = getUrl(authConfig.notificationV2Endpoint) + "/all";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserList(response.data?.notificationGetByIdResponseList || []);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };
  const { canMenuPageSection, rbacRoles } = useRBAC();

  const router = useRouter();

  const canAccessAllNotifications = (requiredPermission) =>
    canMenuPageSection(
      MENUS.LEFT,
      PAGES.TOOLS,
      PAGES.ALL_NOTIFICATIONS,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessAllNotifications(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  if (canAccessAllNotifications(PERMISSIONS.READ)) {
    return (
      <>
        <div>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">Notifications</Typography>
              </Grid>

              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  alignItems="center"
                  justifyContent="flex-end"
                ></Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />
          <CardContent>
            <div style={{ height: 380, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={userList || []}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                  components={{
                    NoRowsOverlay: () => (
                      <Typography
                      variant="body1"
                      align="center"
                      sx={{ marginTop: "120px" }}
                      >
                        {userList?.length === 0 ? "No Data" : "No Rows"}
                      </Typography>
                    )
                  }}
                />
              )}
            </div>
          </CardContent>
        </div>
        <>
        <ViewProfileDialog
          open={viewProfileDialogOpen}
          onClose={handleDialogClose}
          data={currentRow}
          formatCategory={formatCategory}
        />
        </>
      </>
    );
  } else {
    return null;
  }
};

export default Notifications;
