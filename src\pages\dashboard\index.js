import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { useContext, useEffect, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import { useRouter } from "next/router";
import axios from "axios";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRBAC } from "src/pages/permission/RBACContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";

import TotalDonationsCard from "./donation-receipt-widgets/TotalDonationsCard";
import LastThirtyDaysCard from "./donation-receipt-widgets/LastThirtyDaysCard";
import AverageDonationsCard from "./donation-receipt-widgets/AverageDonationsCard";
import TotalTenantsRegisteredCard from "./donation-receipt-widgets/TotalTenantsRegisteredCard";
import UniqueDonorsCard from "./donation-receipt-widgets/UniqueDonorsCard";
import CalendarTodayOutlinedIcon from "@mui/icons-material/CalendarTodayOutlined";
import TrendingUpOutlinedIcon from "@mui/icons-material/TrendingUpOutlined";
import MonetizationOnOutlinedIcon from "@mui/icons-material/MonetizationOnOutlined";
import { Typography } from "@mui/material";
import TotalDonationsPerMonthBarGraphTenant from "./donation-receipt-widgets/TotalDonationsPerMonthBarGraphTenant";
import DonationByHeadsLineGraphTenant from "./donation-receipt-widgets/DonationByHeadsLineGraphTenant";
import NgoSignupOverviewAdmin from "./donation-receipt-widgets/NgoSignupOverviewBarGraphAdmin";
import TotalDonationsGrowthLineChart from "./donation-receipt-widgets/TotalDonationsGrowthLineChartAdmin";
import DonationDistributionPieChartAdmin from "./donation-receipt-widgets/DonationDistributionPieChartAdmin";

const Dashboard = () => {
  const { user } = useContext(AuthContext);
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  const { canMenuPage, canMenuPageSection, rbacRoles } = useRBAC();
  const router = useRouter();

  const canAccessDashboard = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.DASHBOARD, requiredPermission);

  const canAccessActions = (requiredPermission, section) =>
    canMenuPageSection(
      MENUS.LEFT,
      PAGES.DASHBOARD,
      section,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessDashboard(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const actor =
          user?.organisationCategory === "TENANT" ? "TENANT" : "SUPER_ADMIN";
        const orgId =
          user?.organisationCategory === "TENANT" ? user.orgId : null;

        const url = `${getUrl(
          authConfig.dashboardStatisticsEndpoint + "/cards"
        )}`;
        const params = { actor };
        if (orgId) params.orgId = orgId;

        setLoading(true);
        const response = await axios({
          method: "get",
          url: url,
          params: params,
          headers: getAuthorizationHeaders(),
        });
        console.log("Dashboard data:", response.data);
        setData(response.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);

        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  const formatCurrency = (value) =>
    value ? `₹${value.toLocaleString()}` : "-";
  const formatNumber = (value) => (value ? value.toLocaleString() : "-");
  const formatDonations = (value) => (value ? `${value}` : "-");

  return (
    <Grid container spacing={6}>
      {user?.organisationCategory === "SUPER_ADMIN" && (
        <>
          <Grid item xs={12} sm={6} md={3}>
            <TotalTenantsRegisteredCard
              title="Total Tenants Registered"
              value={data?.totalTenantsRegistered}
              loading={loading}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TotalDonationsCard
              title="Total donations received"
              value={data?.totalTenantsDonationsReceived}
              icon={<MonetizationOnOutlinedIcon />}
              formatValue={formatCurrency}
              loading={loading}
              color="#1F1F7A"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <LastThirtyDaysCard
              value={data?.last30DaysDonations}
              loading={loading}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <AverageDonationsCard
              value={data?.averageDonation}
              loading={loading}
              currencySymbol="₹"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <NgoSignupOverviewAdmin />
          </Grid>
          <Grid item xs={12} sm={6}>
            <DonationDistributionPieChartAdmin />
          </Grid>
          <Grid item xs={12} sm={7}>
            <TotalDonationsGrowthLineChart />
          </Grid>
        </>
      )}
      {user?.organisationCategory === "TENANT" && (
        <>
          {canAccessActions(
            PERMISSIONS.READ,
            "Total_Donations_Widget"
          ) && (
            <Grid item xs={12} sm={6} md={3}>
              <TotalDonationsCard
                title="Total Donations"
                value={data?.totalDonations}
                icon={<MonetizationOnOutlinedIcon />}
                formatValue={formatCurrency}
                loading={loading}
                color="#1F1F7A"
              />
            </Grid>
          )}
          {canAccessActions(
            PERMISSIONS.READ,
            "Unique_Donations_Widget"
          ) && (
            <Grid item xs={12} sm={6} md={3}>
              <UniqueDonorsCard value={data?.uniqueDonors} loading={loading} />
            </Grid>
          )}
          {canAccessActions(
            PERMISSIONS.READ,
            "Last_30Days_Donations_Widget"
          ) && (
            <Grid item xs={12} sm={6} md={3}>
              <TotalDonationsCard
                title="Last 30 Days Donations"
                value={data?.last30DaysDonations}
                icon={<CalendarTodayOutlinedIcon />}
                formatValue={formatDonations}
                loading={loading}
                color="#1F1F7A"
              />
            </Grid>
          )}
          {canAccessActions(
            PERMISSIONS.READ,
            "Average_Donation_Widget"
          ) && (
            <Grid item xs={12} sm={6} md={3}>
              <TotalDonationsCard
                title="Average Donation"
                value={data?.averageDonation}
                icon={<TrendingUpOutlinedIcon />}
                formatValue={formatCurrency}
                loading={loading}
                color="#1F1F7A"
              />
            </Grid>
          )}
          {canAccessActions(
            PERMISSIONS.READ,
            "Monthly_Donations_Bar_Graph_Widget"
          ) && (
            <Grid item xs={12} sm={6}>
              <TotalDonationsPerMonthBarGraphTenant />
            </Grid>
          )}
          {canAccessActions(
            PERMISSIONS.READ,
            "Donations_By_Head_Line_Graph_Widget"
          ) && (
            <Grid item xs={12} sm={6}>
              <DonationByHeadsLineGraphTenant />
            </Grid>
          )}
        </>
      )}
    </Grid>
  );
};

export default Dashboard;
