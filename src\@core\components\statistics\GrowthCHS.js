import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const GrowthCHS = () => {
  const state = {
    series: [
      {
        name: "Leads Generated",
        data: [120, 180, 150, 220, 300, 280, 330, 400, 450, 500] // Leads per year
      },
      {
        name: "Customers Acquired",
        data: [80, 120, 100, 140, 200, 180, 250, 320, 350, 380] // Customers per year
      },
      {
        name: 'Society Member Registrations (CHS)',
        data: [50, 80, 70, 100, 120, 150, 90, 140, 160, 200] // CHS registrations per year
      }
    ],
    options: {
      chart: {
        height: 350,
        type: 'line',
        zoom: {
          enabled: false
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        width: [5, 7, 5],
        curve: 'straight',
        dashArray: [0, 8, 5]
      },
      title: {
        text: 'Society (CHS) Data Over Years', // Updated title to reflect CHS data
        align: 'left'
      },
      legend: {
        tooltipHoverFormatter: function(val, opts) {
          return `${val} - <strong>${opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex]}</strong>`;
        }
      },
      markers: {
        size: 0,
        hover: {
          sizeOffset: 6
        }
      },
      xaxis: {
        categories: ['2015', '2016', '2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024'], // Years
      },
      tooltip: {
        y: [
          {
            title: {
              formatter: function (val) {
                return `${val} leads`;
              }
            }
          },
          {
            title: {
              formatter: function (val) {
                return `${val} customers`;
              }
            }
          },
          {
            title: {
              formatter: function (val) {
                return `${val} societies`;
              }
            }
          }
        ]
      },
      grid: {
        borderColor: '#f1f1f1'
      }
    }
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="line" height={350} />
      </div>
    </div>
  );
};

export default GrowthCHS;
