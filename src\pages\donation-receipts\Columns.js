import CustomChip from "src/@core/components/mui/chip";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import MenuItem from "@mui/material/MenuItem";
import Menu from "@mui/material/Menu";
import { useContext, useState } from "react";
import { AuthContext } from "src/context/AuthContext";
import { Tooltip } from "@mui/material";
import { useRBAC } from "../permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const mapIsActiveToLabel = (isActive) => {
  return userStatusObj[isActive] || "Unknown";
};

const useColumns = ({
  menu,
  setMenu,
  setOpenDialog,
  setOpenViewDialog,
  setOpenDeleteDialog,
  setCurrentRow,
  currentRow,
  donationTypesData,
  setSendPDF,
  setSendPaymentLink,
  setSendWhatsAppDialog,
}) => {
  const { canMenuPageSectionField } = useRBAC();
  const canAccessReceipts = (requiredPermission, field) =>
    canMenuPageSectionField(
      MENUS.LEFT,
      PAGES.DONATION_RECEIPT,
      "Donation_Receipts_DataGrid_Table",
      field,
      requiredPermission
    );

    const { listValues } = useContext(AuthContext)

  const {
    donationReceiptId,
    setDonationReceiptId,
    setDonationReceiptDetails,
    user,
  } = useContext(AuthContext);
  return [
    {
      field: "receiptNo",
      headerName: "Receipt No",
      flex: 0.15,
      minWidth: 120,
    },
    user?.organisationCategory === "SUPER_ADMIN"
      ? {
          field: "orgName",
          headerName: "NGO Name",
          flex: 0.11,
          minWidth: 120,
        }
      : null,
    {
      field: "name",
      headerName: "Name",
      flex: 0.1,
      minWidth: 120,
      valueGetter: (params) => params?.row?.tenantDonorsDTO?.name || params?.row?.selfRegisteredDonorDTO?.donorName,
    },
    {
      field: "mobileNumber",
      headerName: "Mobile Number",
      flex: 0.09,
      minWidth: 120,
      valueGetter: (params) => params?.row?.tenantDonorsDTO?.mobileNumber || params?.row?.selfRegisteredDonorDTO?.donorMobileNumber,
    },
    // {
    //   field: "type",
    //   headerName: "Type",
    //   flex: 0.08,
    //   minWidth: 120,
    //   renderCell: (params) => {
    //     const org = donationTypesData?.find(
    //       (item) => item?.value === params?.row?.donationTypeId
    //     );
    //     return (
    //       <Tooltip title={org ? org?.key : ""}>
    //         <span>{org ? org?.key : ""}</span>
    //       </Tooltip>
    //     );
    //   },
    // },
    {
      field: "donationHead",
      headerName: "Donation Head",
      flex: 0.11,
      minWidth: 120,
    },
    {
      field: "amount",
      headerName: "Amount",
      flex: 0.06,
      minWidth: 120,
      valueGetter: (params) => params?.row?.metaData?.amount,
    },
    {
      field: "receiptDate",
      headerName: "Recipient Date",
      flex: 0.08,
      minWidth: 120,
    },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.06,
      minWidth: 100,
      renderCell: ({ row }) => (
        <CustomChip
          rounded
          skin="light"
          size="small"
          label={mapIsActiveToLabel(row.isActive)}
          color={row.isActive ? "success" : "error"}
          sx={{ textTransform: "capitalize" }}
        />
      ),
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.05,
      minWidth: 50,
      sortable: false,
      disableClickEventBubbling: true,
      renderCell: (params) => {
        const handleClickMenu = (event) => {
          event.stopPropagation();
          setMenu(event.currentTarget);
          setCurrentRow(params.row);
          setDonationReceiptId({
            ...donationReceiptId,
            id: params.row.id,
          });
        };

        const onClickEditProfile = () => {
          setOpenDialog(true);
          setMenu(null);
        };

        const onClickViewProfile = () => {
          setOpenViewDialog(true);
          setMenu(null);
        };

        const onClickSendPDF = () => {
          setSendPDF(true);
          setMenu(null);
        };

        const onClickSendPaymentLink = () => {
          setSendPDF(true);
          setSendPaymentLink(true);
          setMenu(null);
        };

        const onClickSendWhatsApp = () => {
          setSendWhatsAppDialog(true);
          setMenu(null);
        };

        const onClickToggleStatus = () => {
          setOpenDeleteDialog(true);
          setMenu(null); // Close menu after action
        };

        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CustomAvatar
              skin="light"
              variant="rounded"
              sx={{
                mr: { xs: 2, lg: 4 },
                width: 34,
                height: 34,
                cursor: "pointer",
              }}
              onClick={handleClickMenu}
            >
              <Icon icon="bi:three-dots-vertical" />
            </CustomAvatar>
            <Menu
              anchorEl={menu}
              open={Boolean(menu)}
              onClose={() => {
                setDonationReceiptDetails(null);
                setMenu(null);
              }}
            >
              {canAccessReceipts(PERMISSIONS.FULL_ACCESS, "Edit") && (
                <MenuItem onClick={onClickEditProfile}>Edit</MenuItem>
              )}
              {canAccessReceipts(PERMISSIONS.FULL_ACCESS, "View") && (
                <MenuItem onClick={onClickViewProfile}>View</MenuItem>
              )}
              {listValues?.find(
                (item) => item.id === currentRow?.metaData?.paymentType
              )?.name === "Online" && (
                <MenuItem onClick={onClickSendPaymentLink}>
                  Send Payment Link
                </MenuItem>
              )}

              <MenuItem onClick={onClickSendPDF}>
                Send Receipt via Email
              </MenuItem>
              <MenuItem onClick={onClickSendWhatsApp}>
                Send Receipt via WhatsApp
              </MenuItem>
              {canAccessReceipts(
                PERMISSIONS.FULL_ACCESS,
                "Activate_Or_Deactivate"
              ) && (
                <MenuItem onClick={onClickToggleStatus}>
                  {currentRow?.isActive ? "Deactivate" : "Activate"}
                </MenuItem>
              )}
            </Menu>
          </div>
        );
      },
    },
  ].filter(Boolean);
};

export default useColumns;
