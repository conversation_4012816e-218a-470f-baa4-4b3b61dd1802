// ** React Imports
import { useContext, useEffect, useState } from "react";

// ** MUI Imports
import { Autocomplete } from "@mui/material";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import authConfig from "src/configs/auth";

// ** Third Party Imports
import { Controller, useForm } from "react-hook-form";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import { AuthContext } from "src/context/AuthContext";

const ReferenceSection = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      ...formData?.reference,
    },
  });

  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const [reference, setReference] = useState(
    formData?.reference?.referenceType
  );

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  const [referenceOptions, setReferenceOptions] = useState([]);

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.referenceListNameId,
        (data) =>
          setReferenceOptions(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const watchedFields = watch();

  useEffect(() => {
    onUpdate({
      ...watchedFields,
      referenceType: reference,
    });
  }, [watchedFields, reference, onUpdate]);

  
  return (
    <Grid
      container
      spacing={3}
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        marginTop: "2rem",
      }}
    >
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <Autocomplete
          id="referenceType"
          options={referenceOptions} // Provide the full referenceOptions array
          value={
            referenceOptions.find((option) => option.value === reference) ||
            null
          } // Match the reference (id) to the option object
          onChange={(event, newValue) => {
            setReference(newValue ? newValue.value : null); // Set the selected id (value)
          }}
          getOptionLabel={(option) => option.key || ""} // Display the key (label) for each option
          isOptionEqualToValue={(option, value) => option.value === value} // Compare the value (id) to find the match
          renderInput={(params) => (
            <TextField
              {...params}
              label="Reference Type"
              variant="outlined"
              size="small"
            />
          )}
        />
      </Grid>
      {reference && (
        <>
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="referralName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Referral Name"
                    size="small"
                    placeholder="Enter Referral Name"
                    error={Boolean(errors.referralName)}
                    helperText={errors.referralName?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="referralNumber"
                control={control}
                rules={{ required: true }}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    type="tel"
                    label="Referral Number"
                    size="small"
                    InputLabelProps={{ shrink: true }}
                    error={Boolean(errors.referralNumber)}
                    helperText={errors.referralNumber?.message}
                    placeholder="+91 1234560789"
                    aria-describedby="validation-referralNumber"
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="referralEmail"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    onChange={(e) =>
                      field.onChange(e.target.value.toLowerCase())
                    }
                    label="Referral Email"
                    size="small"
                    placeholder="Enter email"
                    error={Boolean(errors.referralEmail)}
                    helperText={errors.referralEmail?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
            <FormControl fullWidth>
              <Controller
                name="referralCompanyName"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Referral Company Name"
                    size="small"
                    placeholder="Enter Referral Company Name"
                    error={Boolean(errors.referralCompanyName)}
                    helperText={errors.referralCompanyName?.message}
                  />
                )}
              />
            </FormControl>
          </Grid>
        </>
      )}
    </Grid>
  );
};

export default ReferenceSection;
