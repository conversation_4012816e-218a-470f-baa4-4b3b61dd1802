import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Box, Tabs, Tab, Typography, Paper, useTheme, Grid, Card, CardContent, Avatar, CircularProgress } from '@mui/material';
import MonetizationOnOutlinedIcon from '@mui/icons-material/MonetizationOnOutlined';
import GroupOutlinedIcon from '@mui/icons-material/GroupOutlined';
import CalendarTodayOutlinedIcon from '@mui/icons-material/CalendarTodayOutlined';
import TrendingUpOutlinedIcon from '@mui/icons-material/TrendingUpOutlined';

const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

// Simulated NGO signup data
const mockSignupData = {
  yearly: {
    series: [{ name: 'NGO Signups', data: [12, 20, 18, 22, 35, 40, 30, 25, 28, 50, 45, 60] }],
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
  },
  monthly: {
    series: [{ name: 'NGO Signups', data: [2, 3, 5, 4, 6, 7] }],
    categories: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'],
  },
  weekly: {
    series: [{ name: 'NGO Signups', data: [1, 0, 3, 2, 4, 5, 2] }],
    categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  },
  daily: {
    series: [{ name: 'NGO Signups', data: [1, 2, 0, 3, 1, 4, 2] }],
    categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'],
  },
};

// Simulated total donations data
const mockDonationData = {
    yearly: {
      series: [{ name: 'Total Donations', data: [1500, 2200, 1800, 2500, 3000, 2800, 3500, 3200, 4000, 4500, 4200, 5000] }],
      categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    },
    monthly: {
      series: [{ name: 'Total Donations', data: [200, 150, 300, 250, 350, 100] }],
      categories: ['Nov', 'Dec', 'Jan', 'Feb', 'Mar', 'Apr'],
    },
    weekly: {
      series: [{ name: 'Total Donations', data: [50, 30, 80, 60, 90, 70, 100] }],
      categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    daily: {
      series: [{ name: 'Total Donations', data: [10, 15, 5, 20, 12, 25, 8] }],
      categories: ['0-4h', '4-8h', '8-12h', '12-16h', '16-20h', '20-24h'],
    },
  };

// Simulated donation distribution data
const mockDistributionData = {
    series: [4400, 5500, 3500, 4300, 2400], // Example amounts per category
    labels: ['Education', 'Healthcare', 'Food Aid', 'Shelter', 'Environment'] // Example categories
};

// Mock data fetching for stats cards
const fetchStatsData = async () => {
    console.log('Fetching stats data...');
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate delay
    return {
        totalDonations: 512986,
        uniqueDonors: 20,
        last30DaysDonations: 0,
        averageDonation: 25649
    };
};

// Reusable Stats Card Component
const StatsCard = ({ title, value, icon, formatValue, loading }) => {
    const theme = useTheme();
    const cardColor = theme.palette.primary.main;

    // Default formatting
    const displayValue = formatValue ? formatValue(value) : value;

    return (
        <Card sx={{ 
            position: 'relative', 
            overflow: 'visible', 
            borderLeft: `5px solid ${cardColor}`,
            borderRadius: '8px' // Slightly more rounded corners
            }} 
            elevation={2} // Subtle elevation
        >
            <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
                <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                        {title}
                    </Typography>
                    <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
                        {loading ? <CircularProgress size={20} /> : displayValue}
                    </Typography>
                </Box>
                <Avatar sx={{ 
                    bgcolor: `${cardColor}20`, // Lighter background based on color
                    color: cardColor, 
                    width: 40, 
                    height: 40 
                    }}>
                    {icon}
                </Avatar>
            </CardContent>
        </Card>
    );
};

// Renaming back to reflect bar chart
const NgoSignupBarAnalytics = () => {
  const [tab, setTab] = useState(0);
  const theme = useTheme();
  const handleChange = (_, newValue) => setTab(newValue);

  const tabKey = ['yearly', 'monthly', 'weekly', 'daily'][tab];
  const chartData = mockSignupData[tabKey];

  const chartOptions = {
    chart: {
      type: 'bar', // Back to bar chart
      toolbar: { show: false },
      zoom: { enabled: false },
      foreColor: theme.palette.text.secondary,
    },
    colors: [theme.palette.primary.main], // Use theme primary color for bars
    plotOptions: { // Bar chart specific options
      bar: {
        borderRadius: 4, // Slightly rounded corners
        columnWidth: '35%', // Reduced column width
        distributed: false, // Use single color unless needed otherwise
      }
    },
    dataLabels: {
      enabled: false,
    },
    // stroke: { ... }, // Removed stroke specific to line charts
    // markers: { ... }, // Removed markers specific to line charts
    xaxis: {
      categories: chartData.categories,
      axisBorder: { show: false }, // Hide x-axis border for cleaner look
      axisTicks: { show: false },
      labels: {
          style: {
              fontSize: '12px',
          },
      },
    },
    yaxis: {
      title: {
         text: 'NGOs Signed Up', // Re-added Y-axis title
         style: {
             fontSize: '12px',
             fontWeight: 500,
         }
      },
      labels: {
        style: {
            fontSize: '12px',
        },
        formatter: (value) => `${Math.floor(value)}`, // Format to integer
      },
    },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 3,
      xaxis: { // Show vertical grid lines if desired
        lines: {
          show: false // Set to true to show vertical lines
        }
      },
      yaxis: { // Ensure horizontal grid lines are shown
          lines: {
              show: true
          }
      }
    },
    tooltip: {
        theme: theme.palette.mode,
        y: {
            formatter: (value) => `${Math.floor(value)} Signups`,
        }
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
      <Typography variant="h5" gutterBottom>
        NGO Signups Overview
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        Number of NGOs registered over different periods
      </Typography>

      <Tabs value={tab} onChange={handleChange} sx={{ mb: 2 }}>
        <Tab label="Yearly" />
        <Tab label="Monthly" />
        <Tab label="Weekly" />
        <Tab label="Daily" />
      </Tabs>

      <Chart
        options={chartOptions}
        series={chartData.series}
        type="bar" // Ensure type prop matches options
        height={300}
      />
    </Paper>
  );
};

// Total Donations Line Chart Component
const TotalDonationsLineAnalytics = () => {
    const [tab, setTab] = useState(0); // Default to Yearly
    const theme = useTheme();
    const handleChange = (_, newValue) => setTab(newValue);

    const tabKey = ['yearly', 'monthly', 'weekly', 'daily'][tab];
    const chartData = mockDonationData[tabKey]; // Use donation data
    const currencySymbol = '₹'; // Define currency symbol

    const chartOptions = {
      chart: {
        type: 'line', // Changed to line
        toolbar: { show: false },
        zoom: { enabled: false },
        foreColor: theme.palette.text.secondary,
      },
      colors: [theme.palette.primary.main], // Different color for donations (e.g., success green)
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: 'smooth',
        width: 2,
      },
      markers: { // Added markers
          size: 4,
          colors: [theme.palette.primary.main],
          strokeColors: '#fff',
          strokeWidth: 2,
          hover: {
            size: 6,
          }
      },
      xaxis: {
        categories: chartData.categories,
        axisBorder: { show: true, color: theme.palette.divider },
        axisTicks: { show: false },
        labels: {
            style: {
                fontSize: '12px',
            },
        },
      },
      yaxis: {
        // title: { text: 'Total Donations' }, // Optional Y-axis title
        labels: {
          style: {
              fontSize: '12px',
          },
          // Format Y-axis labels with currency symbol
          formatter: (value) => `${currencySymbol}${Math.floor(value)}`,
        },
      },
      grid: {
        borderColor: theme.palette.divider,
        strokeDashArray: 3,
      },
      tooltip: {
          theme: theme.palette.mode,
          y: {
              // Format tooltip value with currency symbol
              formatter: (value) => `${currencySymbol}${Number(value).toFixed(2)}`,
              title: {
                  formatter: (seriesName) => `${seriesName}:`
              }
          }
      }
    };

    return (
      <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
        <Typography variant="h5" gutterBottom>
          Total Donations Growth
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          Donation trends over different periods
        </Typography>

        <Tabs value={tab} onChange={handleChange} sx={{ mb: 2 }}>
          <Tab label="Yearly" />
          <Tab label="Monthly" />
          <Tab label="Weekly" />
          <Tab label="Daily" />
        </Tabs>

        <Chart
          options={chartOptions}
          series={chartData.series}
          type="line"
          height={300}
        />
      </Paper>
    );
  };

// Donation Distribution Pie Chart Component
const DonationDistributionPieChart = () => {
    const theme = useTheme();
    const currencySymbol = '₹';
    const chartData = mockDistributionData; // Use distribution data

    const chartOptions = {
        chart: {
            type: 'pie',
            toolbar: { show: true } // Maybe show toolbar for export options
        },
        colors: [ // Use a nice color palette
            theme.palette.primary.main,
            theme.palette.success.main,
            theme.palette.warning.main,
            theme.palette.info.main,
            theme.palette.error.main,
        ],
        labels: chartData.labels,
        dataLabels: { // Configure data labels
            enabled: true,
            formatter: function (val, opts) {
                // Show percentage on the slices
                return `${opts.w.globals.seriesNames[opts.seriesIndex]}: ${val.toFixed(1)}%`;
            },
            style: {
                fontSize: '10px', // Smaller font size for labels
                // fontWeight: 'bold',
                // colors: ['#000'], 
            },
            dropShadow: { // Add shadow for better readability
              enabled: true,
              top: 1,
              left: 1,
              blur: 1,
              color: '#000',
              opacity: 0.45
            }
        },
        legend: { // Configure legend
            position: 'bottom',
            horizontalAlign: 'center', 
            fontSize: '12px',
            markers: {
                width: 12,
                height: 12,
            },
            itemMargin: {
                horizontal: 10,
                vertical: 5
            },
        },
        tooltip: { // Configure tooltip
            theme: theme.palette.mode,
            y: {
                formatter: (value) => `${currencySymbol}${value.toLocaleString()}`,
                title: {
                    formatter: (seriesName) => `${seriesName}:`
                }
            }
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 200
                },
                legend: {
                    position: 'bottom'
                }
            }
        }]
    };

    return (
        <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
            <Typography variant="h5" gutterBottom>
                Donation Distribution
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
                Breakdown by Donation Head 
            </Typography>
            {/* No Tabs for pie chart distribution */}
            <Box sx={{ mt: 2 }}> {/* Add some margin top */}
                <Chart
                    options={chartOptions}
                    series={chartData.series}
                    type="pie"
                    height={350} // Adjust height as needed for pie chart + legend
                />
            </Box>
        </Paper>
    );
};

// Main Page Component
const DonationAdminPage = () => {
    // State for stats cards
    const [stats, setStats] = useState(null);
    const [loadingStats, setLoadingStats] = useState(true);
    const currencySymbol = '₹';

    // Fetch stats data on mount
    useEffect(() => {
        const loadStats = async () => {
            setLoadingStats(true);
            try {
                const data = await fetchStatsData();
                setStats(data);
            } catch (error) {
                console.error("Error fetching stats data:", error);
                setStats(null); // Handle error case
            } finally {
                setLoadingStats(false);
            }
        };
        loadStats();
    }, []); // Empty dependency array means run once on mount

    return (
        <Grid container spacing={3}>
            {/* Stats Cards Row */}
            <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                    title="Total Donations"
                    value={stats?.totalDonations}
                    loading={loadingStats}
                    icon={<MonetizationOnOutlinedIcon />}
                    formatValue={(val) => `${currencySymbol}${val?.toLocaleString() || '0'}`}
                />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                    title="Unique Donors"
                    value={stats?.uniqueDonors}
                    loading={loadingStats}
                    icon={<GroupOutlinedIcon />}
                />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                 <StatsCard
                    title="Last 30 Days"
                    value={stats?.last30DaysDonations}
                    loading={loadingStats}
                    icon={<CalendarTodayOutlinedIcon />}
                    formatValue={(val) => `${val ?? '0'} donations`}
                />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                    title="Average Donation"
                    value={stats?.averageDonation}
                    loading={loadingStats}
                    icon={<TrendingUpOutlinedIcon />}
                    formatValue={(val) => `${currencySymbol}${val?.toLocaleString() || '0'}`}
                />
            </Grid>

            {/* Charts Row 1 */}
            <Grid item xs={12} md={6}>
                <NgoSignupBarAnalytics />
            </Grid>
            <Grid item xs={12} md={6}>
                <TotalDonationsLineAnalytics />
            </Grid>

            {/* Charts Row 2 */}
            <Grid item xs={12}>
                <DonationDistributionPieChart />
            </Grid>
        </Grid>
    );
};

// Configure page access
// Adjust authGuard/guestGuard as needed for your application
DonationAdminPage.authGuard = false;


export default DonationAdminPage;
