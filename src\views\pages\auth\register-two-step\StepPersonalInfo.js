// ** MUI Components
import { CircularProgress } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { Controller } from "react-hook-form";
import { styled, useTheme } from "@mui/material/styles";
import EmailTextField from "src/@core/components/custom-components/EmailTextField";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import Link from "next/link";

// ** Axios

// ** Config

// ** Icon Imports

import Icon from "src/@core/components/icon";

const StepPersonalDetails = ({
  submit,
  handlePrev,
  handleSubmit,
  errors,
  control,
  disableSubmitButton,
  loading
}) => {


  const LinkStyled = styled(Link)(({ theme }) => ({
    display: "flex",
    fontSize: "1rem",
    alignItems: "center",
    textDecoration: "none",
    justifyContent: "center",
    color: theme.palette.primary.main,
  }));

 

  return (
    <>
      <Box sx={{ mb: 9 }}>
        <Typography variant="h5" sx={{ mb: 1.5 }}>
          Personal Information
        </Typography>
        <Typography sx={{ color: "text.secondary" }}>
          Enter Your Personal Information
        </Typography>
      </Box>
      <Grid container spacing={5}>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="firstName"
              control={control}
              render={({ field }) => (
                <NameTextField
                  {...field}
                  InputLabelProps={{ shrink: true }}
                  placeholder="Name"
                  id="name"
                  label="Name"
                  error={Boolean(errors?.firstName)}
                  helperText={errors?.firstName?.message}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <FormControl fullWidth>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <EmailTextField
                  {...field}
                  type="email"
                  label="Email"
                  id="email"
                  name="email"
                  placeholder="<EMAIL>"
                  InputLabelProps={{ shrink: true }}
                  helperText={errors?.email?.message}
                  error={Boolean(errors?.email)}
                />
              )}
            />
          </FormControl>
        </Grid>
       
        <Grid
          item
          xs={12}
          sx={{ pt: (theme) => `${theme.spacing(6)} !important` }}
        >
          <Box sx={{ mt: 2, display: "flex", justifyContent: "space-between" }}>
            <Button
              color="secondary"
              variant="outlined"
              onClick={handlePrev}
              sx={{ "& svg": { mr: 2 } }}
            >
              <Icon fontSize="1.125rem" icon="tabler:arrow-left" />
              Previous
            </Button>
            
            <Button
              color="success"
              variant="contained"
              onClick={handleSubmit(submit)}
              disabled={disableSubmitButton}
              sx={{
                color: "primary.contrastText",
                borderColor: "success.main",
              }}
            >
              {loading ? (
                <CircularProgress color="inherit" size={24} />
              ) : (
                "Submit")}
            </Button>
          </Box>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "right" }}>
          <Typography sx={{ color: "text.secondary", mr: 2 }}>
                  Already have an account?
                </Typography>
                <Typography variant="body2">
                  <LinkStyled
                    href="/login"
                    sx={{ fontSize: "1rem" }}
                  >
                    login
                  </LinkStyled>
                </Typography>

          </Box>
        
          
             
        </Grid>
            
      </Grid>
    </>
  );
};

export default StepPersonalDetails;
