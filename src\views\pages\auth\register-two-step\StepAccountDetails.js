// ** <PERSON><PERSON> Components
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import { styled, useTheme } from "@mui/material/styles";
import Link from "next/link";

// ** Icon Imports
import Icon from 'src/@core/components/icon'
import SelectBasic from 'src/@core/components/custom-components/SelectBasic'

// ** React Imports

const categories = [
  {
    value: 'ARCHITECT',
    key: 'Architect'
  },{value: 'LEGAL', key: 'Legal'}, { value: 'BUILDER', key: 'Builder' },{ value: 'STRUCTURAL_ENGINEER', key: 'Structural Engineer' },
  { value: 'PMC', key: 'PMC' }, { value: 'BROKER', key: 'Broker' }, { value: 'CHARTERED_ACCOUNTANT', key: 'CA' },
  {value: 'PLUMBER',key: 'Plumber'},{value: 'ELECTRICAL',key: 'Electrical'},{value: 'PAINTING',key: 'Painter'},
  {value: 'HVAC',key: 'HVAC'},{value: 'GLASS_FACADE',key: 'Glass Facade'},{value: 'CIVIL_CONTRACTORS',key: 'Civil contractors'},
  {value: 'LEGAL_SERVICE',key: 'Legal Service'},{value: 'WATER_PROOFING',key: 'Water Proofing'},{value: 'SOCIETY_ACCOUNTING',key: 'Society Accounting'},
  {value: 'MANAGERS',key: 'Managers'},{value: 'LANDSCAPING',key: 'Landscaping'},{value: 'HOUSE_KEEPING',key: 'House Keeping'},
  {value: 'FACILITY_MANAGEMENT',key: 'Facility Management'},{value: 'PEST_CONTROL',key: 'Pest Control'},{value: 'SEWAGE_CLEANING',key: 'Sewage Cleaning'},
  {value: 'ELEVATOR',key: 'Elevator'},{value: 'SOLAR_PANEL',key: 'Solar Panel'},{value: 'RAIN_WATER_HARVESTING',key: 'Rain Water Harvesting'},
  {value: 'COMPOSTING',key: 'Composting'},{value: 'SWIMMING_POOL',key: 'Swimming pool'},{value: 'GYM',key: 'Gym'},
];

const types = [{ value: 'INDIVIDUAL', key: 'Individual' }, { value: 'TYPE_ENTITY', key: 'Entity' }];

const StepAccountDetails = ({ handleNext, register, watchFields }) => {

  const styles = {
    opacity: 0.5,
    pointerEvents: 'none'
  }

  const entityCategory = watchFields.entityCategory;
  const entityType = watchFields.entityType;

  const LinkStyled = styled(Link)(({ theme }) => ({
    display: "flex",
    fontSize: "1rem",
    alignItems: "center",
    textDecoration: "none",
    justifyContent: "center",
    color: theme.palette.primary.main,
  }));
  



  return (
    <>
      <Box sx={{ mb: 9 }}>
        <Typography variant='h5' sx={{ mb: 1.5 }}>
          Account Information
        </Typography>
        <Typography sx={{ color: 'text.secondary' }}>Let Us Know Who You are</Typography>
      </Box>

      <Grid container spacing={5}>
        <Grid item xs={12} sm={6}>
          <SelectBasic register={register} id='entityCategory' name="entityCategory" label={'Entity Category'} nameArray={categories}
            defaultValue={entityCategory || ''} />
        </Grid>
        <Grid item xs={12} sm={6}>
          <SelectBasic register={register} id='entityType' name="entityType" label={'Entity Type'} nameArray={types}
            defaultValue={entityType || ''} />
        </Grid>

           

        <Grid item xs={12} sx={{ pt: theme => `${theme.spacing(6)} !important` }}>
          <Box sx={{ mt: 9, display: 'flex', justifyContent: 'flex-end' }}>
        
            {
              (entityCategory && entityType) ? (
                <Button
                variant='contained' onClick={handleNext} sx={{ '& svg': { ml: 2 } }}>
                  Next
                  <Icon fontSize='1.125rem' icon='tabler:arrow-right' />
                </Button>
              ) : (
                
                <Button
                style={styles} 
                variant='contained' onClick={handleNext} sx={{ '& svg': { ml: 2 } }}>
                  Next
                  <Icon fontSize='1.125rem' icon='tabler:arrow-right' />
                </Button>
              )
              
            }
            

       

          </Box>
          <Box sx={{ mt: 2, display: "flex", justifyContent: "right" }}>
          <Typography sx={{ color: "text.secondary", mr: 2 }}>
                  Already have an account?
                  </Typography>
         
                <LinkStyled href="/login">
                <Icon fontSize="1.25rem" icon="tabler:chevron-left" />
                <span>login</span>
                </LinkStyled>
            

        </Box>
        </Grid>
      </Grid>
    </>
  )
}

export default StepAccountDetails
