import CloseIcon from "@mui/icons-material/Close";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  TextField,
  Typography,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import { AuthContext } from "src/context/AuthContext";
import DialogData from "../roles/DialogData";


const UpdateUser = ({
  open,
  onClose,
  fetchUsers,
  data,
  page,
  pageSize,
  selectedFilters,
  rowData,
}) => {
  const [roleId, setRoleId] = useState("");
  const { user } = useContext(AuthContext);

  const { rolesDetails, setRolesDetails } = useContext(AuthContext);

  const { setValue, control, handleSubmit } = useForm();

  const [initialSiteMapData, setInitialSiteMapData] = useState([]);

   const [roles, setRoles] = useState([]);
      const rolesArray = roles?.map((item) => ({
        value: item?.id,
        key: item?.roleName,
        description: item?.roleDescription,
      }));
    
      useEffect(() => {
        axios({
          method: "get",
          url:
            getUrl(authConfig.rolesDropdownEndpoint) +
            "/get-roles-by-orgId/" +
            rowData?.organisationId,
          headers: getAuthorizationHeaders(),
        })
          .then((res) => {
            setRoles(res.data);
          })
          .catch((err) => console.log("error", err));
      }, [rowData]);

  useEffect(() => {
    setRoleId(data?.roleId);
    setInitialSiteMapData(data?.permissionsDTOList);
  }, [data]);


  const auth = useAuth();

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);



  const [roleChange, setRoleChange] = useState(false);

  useEffect(() => {
    if (roleId && roleChange) {
      axios({
        method: "get",
        url: getUrl(authConfig.rolesEndpoint) + "/" + roleId,
        // headers: getAuthorizationHeaders(authConfig.getMIMEType),
      })
        .then((res) => {
          setInitialSiteMapData(res.data.permissions);
        })
        .catch((err) => console.log("Categories error", err));
    }
  }, [roleId]);

  const handleClose = () => {
    onClose();
    setRolesDetails({});
    setInitialSiteMapData([]);
    setRoleChange(false);
    setOpenDialog(false);
  };

  const handleCancel = () => {
    const message = `
        <div>
          <h3> Are you sure you want to leave the page? Unsaved changes may be lost</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleCancelClose = () => {
    setOpenDialog(false);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleRole = (event) => {
    setRoleId(event);
    setRoleChange(true);
  };

  const handleSuccess = () => {
    setDialogMessage(
      "<div><h3>User Permissions updated successfully.</h3></div>"
    );
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    setDialogMessage(
      "<div><h3>Failed to update User Permissions. Please try again later.</h3></div>"
    );
    setOpenDialogContent(true);
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const isApiCalling = useRef(false);

  async function handleUpdate() {
    if (isApiCalling.current) return;

    const fields = {
      roleId: roleId,
      permissionsDTOList: initialSiteMapData,
    };
    setIsSubmitting(true);

    try {
      await auth.patchIndividualPermissions(
        rowData?.id,
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("User update failed:", error);
      handleFailure();
    } finally {
      setIsSubmitting(false);
      isApiCalling.current = false;
      fetchUsers(page, pageSize, selectedFilters);
    }
  }

  return (
    <>
      <Dialog open={open} onClose={handleCancel} maxWidth="md" fullScreen>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // height
            marginLeft: { xl: 3.8, lg: 4, md: 3.8, sm: 4, xs: 3.4 },
          }}
          textAlign="center"
        >
          Update User
          <Box
            sx={{
              position: "absolute",
              top: "8px",
              right: "65px",
              marginRight: { xs: 4, sm: 4.5, md: 5, lg: 6, xl: 10 },
            }}
          >
            <Button
              variant="contained"
              color="primary"
              size="small"
              disabled={isSubmitting}
              onClick={handleUpdate}
            >
              Update
            </Button>
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "8px",
              right: "10px",
              marginRight: { xs: 0.2, sm: 4.5, md: 5, lg: 5.5, xl: 5.8 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <CloseIcon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={5} alignItems={"center"} sx={{ height: "100%" }}>
            <Grid item xs={12} sm={2}>
              <Typography fontSize="body1" sx={{ mr: 1, ml: 0 }}>
                <strong>Name : </strong> {rowData?.name}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={3}>
              <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                <strong>Email : </strong> {rowData?.email}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={2}>
              <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                <strong>Mobile Number : </strong> {rowData?.mobileNumber}
              </Typography>
            </Grid>
            {user?.organisationCategory === "SUPER_ADMIN" && (<>
              <Grid item xs={12} sm={2}>
              <Typography fontSize="body1" sx={{ mr: 1, ml: 1 }}>
                <strong>NGO Name : </strong> {rowData?.companyName}
              </Typography>
            </Grid>

            </>)}

           

            <Grid item xs={12} sm={3}>
              <SelectAutoComplete
                id={"roleId"}
                label={"Select Role"}
                name="roleId"
                nameArray={rolesArray}
                defaultValue={roleId}
                value={roleId}
                onChange={(event) => handleRole(event.target.value)}
                aria-describedby="roleId"
              />
            </Grid>
          
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              marginTop: 3,
              border: (theme) => `1px solid ${theme.palette.divider}`,
            }}
          ></Box>
          <Grid item xs={12} sm={12}>
            <DialogData
              initialSiteMapData={initialSiteMapData}
              setInitialSiteMapData={setInitialSiteMapData}
            />
          </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
      <Dialog
        open={openDialog}
        onClose={handleCancelClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Yes
            </Button>
            <Button
              variant="contained"
              onClick={handleCancelClose}
              sx={{ margin: "auto", width: 100 }}
            >
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default UpdateUser;
