import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const PolarAreaChart = () => {
  const state = {
    series: [45, 70, 55, 60, 50, 65, 75, 85], // Example generic data points
    options: {
      chart: {
        type: 'polarArea',
        width: '100%',  // Set to 100% to fill the container
        height: '50%', // Set to 100% to fill the container
      },
      labels: ['Requests Received', 'Requests Completed', 'Pending Tasks', 'Feedback Positive', 'Feedback Neutral', 'Feedback Negative', 'Support Tickets Open', 'Support Tickets Closed'], // Custom labels
      stroke: {
        colors: ['#fff'], // White border color for the chart
      },
      fill: {
        opacity: 0.8, // Adjust the opacity of the fill
      },
      title: {
        text: 'SR Metrics Overview',
        align: 'center'
      },
      colors: ['#FF4560', '#00E396', '#008FFB', '#FF9800', '#9C27B0', '#E91E63', '#3F51B5', '#4CAF50'], // Custom colors for different data points
      legend: {
        position: 'right',
        offsetY: 0,
        itemMargin: {
          horizontal: 10, // Increase horizontal margin between legend items
          vertical: 10, // Increase vertical margin between legend items
        },
        markers: {
          width: 12, // Increase marker size
          height: 12, // Increase marker size
        },
        fontSize: '14px', // Increase legend text font size
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            width: 300, // Width of the chart on smaller screens
          },
          legend: {
            position: 'bottom', // Position legend at the bottom on smaller screens
          },
        },
      }],
    },
  };

  return (
    <div style={{ width: '500px', height: '500px', display: 'flex', justifyContent: 'center' }}>
      <div id="chart" style={{ width: '100%', height: '50%' }}>
        <ApexChart options={state.options} series={state.series} type="polarArea" />
      </div>
    </div>
  );
};

export default PolarAreaChart;
