// ** Next Import
import Link from 'next/link'

// ** MUI Imports
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import CardHeader from '@mui/material/CardHeader'
import CardContent from '@mui/material/CardContent'
import CardActions from '@mui/material/CardActions'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

const CardService = () => {
  return (
    <Card>
      {/* <Card sx={{ background: 'rgb(0 207 232 / 16%)' }}> */}
      <CardHeader title='Service1' />
      <CardContent>
        <Typography variant='body2'>
          If you’re in the market for new desktops, notebooks, or PDAs, there are a myriad of choices. Here’s a rundown
          of some of the best systems available.
        </Typography>
      </CardContent>
      <CardActions className='card-action-dense' sx={{ justifyContent: 'center' }}>
        <Button sx={{ mb: 5 }} component={Link} variant='contained' href='/'>
          Read More
        </Button>
      </CardActions>
    </Card>
  )
}

export default CardService
