import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Typography,
  DialogActions,
  DialogContentText,
  TableContainer,
  Snackbar,
  Alert, // Import Snackbar and Alert components
} from "@mui/material";
import Icon from "src/@core/components/icon"; // Custom icon component
import { AttachFile, Fullscreen, FullscreenExit } from "@mui/icons-material";
import mammoth from "mammoth"; // Include for DOCX to HTML conversion


function UploadFile2({ selectedDocument,selectedFiles, setSelectedFiles,setUpdatedFile }) {


  const [fileError, setFileError] = useState("");
  const [selectedFileIndex, setSelectedFileIndex] = useState(null);
  const [docxContent, setDocxContent] = useState("");
  const [pdfUrl, setPdfUrl] = useState(null);
  const [expanded, setExpanded] = useState(false);
  const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState(""); // State for toast message
  const fileInputRef = useRef(null);
 
  useEffect(() => {
    const preventDefaultDragDrop = (event) => {
      event.preventDefault();
    };
    document.addEventListener("dragover", preventDefaultDragDrop);
    document.addEventListener("drop", preventDefaultDragDrop);
    return () => {
      document.removeEventListener("dragover", preventDefaultDragDrop);
      document.removeEventListener("drop", preventDefaultDragDrop);
    };
  }, []);
 
  const removeFile = () => {
    setSelectedFiles((prevFiles) => {
      const newFiles = prevFiles.filter((file) => file !== fileToDelete);
      return newFiles;
    });
    setFileToDelete(null);
    setConfirmDeleteDialogOpen(false);
    setToastMessage("File deleted successfully!");
    setShowToast(true);
  };
 
  const isValidFileType = (file) => {
    const validTypes = [
      ".pdf",
      ".doc",
      ".docx",
      ".jpg",
      ".jpeg",
      ".gif",
      ".bmp",
      ".png",
    ];
    const fileType = "." + file.name.split(".").pop();
    return validTypes.includes(fileType);
  };
 
  const isValidFileSize = (file) => {
    const maxSize = 5 * 1024 * 1024; // 5 MB in bytes
    return file.size <= maxSize;
  };
 
  const validateFileName = (fileName) => {
    return fileName.length <= 100;
  };
 
  const handleFileChange = (event) => {
    const fileInput = event.target;
    const files = Array.from(fileInput.files);
    fileInput.value = "";
 
    if (selectedFiles.length > 0 || selectedDocument) {
      // If there's already a file selected, prompt user to confirm replacement
      setConfirmReplaceDialogOpen(true);
      setNewFile(files[0]);
    } else {
      // If no file is selected, add the new file
      setSelectedFiles(files);
      setUpdatedFile(true)
      setToastMessage("File added successfully!");
      setShowToast(true);
    }
   
  };

  const [newFile, setNewFile] = useState(null);
 
  // Add a new state to store the confirmation dialog open state
  const [confirmReplaceDialogOpen, setConfirmReplaceDialogOpen] =
    useState(false);
 
  const handleDrop = (event) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    files.forEach((file) => {
      if (
        isValidFileType(file) &&
        isValidFileSize(file) &&
        validateFileName(file.name)
      ) {
        setSelectedFiles((prevFiles) => [...prevFiles, file]);
        setUpdatedFile(true)
        setFileError("");
        setToastMessage("Files added successfully!");
        setShowToast(true);
      } else {
        setFileError("Invalid file type, size, or name.");
      }
    });
  };
 
  const handleDragOver = (event) => {
    event.preventDefault();
  };
 
  const openDialog = async (index) => {
    setSelectedFileIndex(index);
    const file = selectedFiles[index];
 
    if (
      file.type ===
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      try {
        const arrayBuffer = await file.arrayBuffer();
        const result = await mammoth.convertToHtml({ arrayBuffer });
        setDocxContent(result.value);
      } catch (error) {
        console.error("Error converting DOCX to HTML:", error);
      }
    }
 
    if (file.type === "application/pdf") {
      try {
        const url = URL.createObjectURL(file);
        setPdfUrl(url);
      } catch (error) {
        console.error("Error creating object URL for PDF:", error);
      }
    }
  };
 
  const closeDialog = () => {
    setSelectedFileIndex(null);
    setDocxContent("");
    if (pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
    }
  };
 
  const handleExpand = () => {
    setExpanded(!expanded);
  };
 
  const handleClick = () => {
    fileInputRef.current.click();
  };
 
  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };
 
  return (
    <Grid container spacing={2} direction="column">
      <Grid
        item
        xs={12}
        sx={{ display: "flex", justifyContent: "center", mt: 2, mb: 2 }}
      >
        <FormControl error={!!fileError}>
          <div
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              padding: "10px",
              border: "1px solid #ddd",
              borderRadius: "3px",
              backgroundColor: "#f2f7f2",
              cursor: "pointer",
              width: "100%",
              maxWidth: "400px",
              height: 120,
              "@media (max-width: 600px)": {
                height: 80,
                padding: "5px",
              },
            }}
            onClick={handleClick}
          >
            <input
              type="file"
              onChange={handleFileChange}
              style={{ display: "none" }}
              ref={fileInputRef}
              multiple
              accept=".pdf, .doc, .docx, .jpg, .jpeg, .gif, .bmp, .png"
            />
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                flexDirection: "column",
                border: "2px dashed #ccc",
                borderRadius: "10px",
                padding: "10px",
                textAlign: "center",
                width: "100%",
                height: "auto",
                "@media (max-width: 600px)": {
                  padding: "5px",
                },
              }}
            >
              <Box sx={{ display: "flex", gap: 2, mb: 1 }}>
                <IconButton>
                  <AttachFile sx={{ fontSize: 20, color: "#1976d2" }} />
                </IconButton>
              </Box>
              <Typography
                variant="body2"
                color="textSecondary"
                sx={{
                  fontSize: "0.875rem",
                  "@media (max-width: 600px)": { fontSize: "0.75rem" },
                }}
              >
                Drag & drop files (or) click to upload (Max 5 MB)
              </Typography>
            </Box>
          </div>
          {fileError && (
            <FormHelperText
              sx={{
                maxWidth: "400px",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                "@media (max-width: 600px)": {
                  fontSize: "0.75rem",
                },
                "&:hover": {
                  overflow: "visible",
                  whiteSpace: "normal",
                  wordWrap: "break-word",
                  backgroundColor: "#f9f9f9",
                  padding: "5px",
                  borderRadius: "3px",
                },
              }}
              title={fileError}
            >
              {fileError}
            </FormHelperText>
          )}
        </FormControl>
      </Grid>
 
      <Grid item xs={12} sx={{ mt: 2 }}>
        {selectedFiles && selectedFiles.length > 0 && (
          <TableContainer style={{ overflowX: "auto", maxHeight: "90px" }}>
            <Table sx={{ width: "100%" }}>
              <TableHead>
                <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "12px",
                      height: "10px",
                      paddingTop: "1px",
                      paddingBottom: "1px",
                    }}
                  >
                    File Name
                  </TableCell>
                  <TableCell
                    style={{
                      fontWeight: "bold",
                      fontSize: "12px",
                      textAlign: "end",
                      height: "10px",
                      paddingTop: "1px",
                      paddingBottom: "1px",
                      mr: "3px !important",
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {selectedFiles?.map((file, index) => (
                  <TableRow key={index}>
                    <TableCell
                      style={{
                        fontWeight: "bold",
                        fontSize: "14px",
                        padding: "4px",
                      }}
                    >
                      <Typography
                        className="data-field"
                        sx={{
                          wordBreak: "break-word",
                          maxWidth: "calc(100% - 100px)",
                          ml: { xs: 4 },
                        }}
                      >
                        {file?.name}
                      </Typography>
                    </TableCell>
                    <TableCell
                      style={{
                        fontWeight: "bold",
                        fontSize: "14px",
                        padding: "4px",
                        textAlign: "right",
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          justifyContent: "flex-end",
                          gap: 1,
                        }}
                      >
                        <IconButton
                          onClick={() => openDialog(index)}
                          color="primary"
                        >
                          <Icon icon="iconamoon:eye" />
                        </IconButton>
                        <IconButton
                          onClick={() => {
                            setFileToDelete(file);
                            setConfirmDeleteDialogOpen(true);
                          }}
                          color="error"
                        >
                          <Icon icon="iconamoon:trash" />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Grid>
 
      {/* File Preview Dialog */}
      <Dialog
        open={selectedFileIndex !== null}
        onClose={closeDialog}
        maxWidth={expanded ? "xl" : undefined}
        maxHeight={expanded ? "xl" : undefined}
        fullWidth={expanded}
        fullScreen={expanded}
      >
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            overflow: "hidden",
            textOverflow: "ellipsis",
            whiteSpace: "nowrap",
            maxWidth: "100%",
          }}
          textAlign={"center"}
        >
          <Typography
            variant="body1"
            noWrap={selectedFiles[selectedFileIndex]?.name.length > 100}
            sx={{
              maxWidth: "calc(100% - 48px)",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {selectedFileIndex !== null &&
              selectedFiles[selectedFileIndex]?.name}
          </Typography>
          <Box sx={{ position: "absolute", top: "4px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleExpand}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
                mr: 2,
                mb: 1,
              }}
            >
              {expanded ? (
                <FullscreenExit sx={{ fontSize: "1rem" }} />
              ) : (
                <Fullscreen sx={{ fontSize: "1rem" }} />
              )}
            </IconButton>
            <IconButton
              size="small"
              onClick={closeDialog}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
                mb: 1,
              }}
            >
              <Icon icon="iconamoon:close" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
 
        <DialogContent
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {selectedFileIndex !== null &&
            selectedFiles[selectedFileIndex]?.type?.startsWith("image/") && (
              <img
                src={URL.createObjectURL(selectedFiles[selectedFileIndex])}
                alt={selectedFiles[selectedFileIndex]?.name}
                style={{ maxWidth: "100%", maxHeight: "80vh" }}
              />
            )}
          {selectedFileIndex !== null &&
            selectedFiles[selectedFileIndex]?.type === "application/pdf" &&
            pdfUrl && (
              <iframe
                src={pdfUrl}
                style={{ width: "100%", height: "80vh" }}
                title={selectedFiles[selectedFileIndex]?.name}
              />
            )}
          {selectedFileIndex !== null &&
            selectedFiles[selectedFileIndex]?.type ===
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document" && (
              <div
                dangerouslySetInnerHTML={{ __html: docxContent }}
                style={{
                  maxWidth: "100%",
                  maxHeight: "80vh",
                  overflowY: "auto",
                }}
              />
            )}
          {selectedFileIndex !== null &&
            !(
              selectedFiles[selectedFileIndex]?.type?.startsWith("image/") ||
              selectedFiles[selectedFileIndex]?.type === "application/pdf" ||
              selectedFiles[selectedFileIndex]?.type ===
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            ) && <p>Preview not available for this file type.</p>}
        </DialogContent>
      </Dialog>
 
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={confirmDeleteDialogOpen}
        onClose={() => setConfirmDeleteDialogOpen(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div
                dangerouslySetInnerHTML={{
                  __html: `<h4>Are you sure you want to delete ${fileToDelete?.name}?</h4>`,
                }}
              />
            </DialogContentText>
          </DialogContent>
 
          <DialogActions>
            <Button onClick={removeFile} variant="contained">
              Yes
            </Button>
            <Button onClick={() => setConfirmDeleteDialogOpen(false)}>
              No
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
 
      {/* Snackbar for toast notifications */}
      <Snackbar
        open={showToast}
        autoHideDuration={2000} // Toast will be visible for 2 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity="success"
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "2px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
 
      <>
        <Dialog
          open={confirmReplaceDialogOpen}
          onClose={() => setConfirmReplaceDialogOpen(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText id="alert-dialog-description">
                <div
                  dangerouslySetInnerHTML={{
                    __html: `<h4>Do you want to proceed with replacing the existing file?</h4>`,
                  }}
                />
              </DialogContentText>
            </DialogContent>
            <DialogActions
              sx={{
                justifyContent: "center",
              }}
            >
              <Button
                variant="contained"
                onClick={() => {
                  setSelectedFiles([newFile]);
                  setUpdatedFile(true)
                  setConfirmReplaceDialogOpen(false);
                  setToastMessage("File replaced successfully!");
                  setShowToast(true);
                }}
              >
                Yes
              </Button>
              <Button onClick={() => setConfirmReplaceDialogOpen(false)}>
                No
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </>
    </Grid>
  );
}

export default UploadFile2;

