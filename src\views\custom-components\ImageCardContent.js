// ** MUI Imports
import { styled, useTheme } from '@mui/material/styles'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'
import MuiCard from '@mui/material/Card'
import Grid from '@mui/material/Grid'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'

// Styled Card component
const Card = styled(MuiCard)(({ theme }) => ({
  padding: '3.25rem 2.5rem 2.5rem 2.5rem',
  width: '85vw',
  minHeight: '75vh',
  border: 0,
  boxShadow: 'none',
  backgroundSize: 'cover',
  background: '#d5e0d5',

  // backgroundImage: 'url(/images/pages/header-bg.png)',

  [theme.breakpoints.down('md')]: {
    width: '100%',
    padding: '1.55rem'
  }
}))

const CardTab = styled(MuiCard)(() => ({
  display: 'flex',
  alignItems: 'start',
  minHeight: '100px',
  padding: '14px',
  borderRadius: '5px',
  cursor: 'pointer'
}))

const ImageCardContent = () => {
  return (
    <Card>
      <CardContent sx={{ pb: theme => `${theme.spacing(5)} !important` }}>
        <Grid container spacing={5}>
          <Grid item xs={12} sm={12} lg={7}>
            <Typography sx={{ mb: 6, fontWeight: 600, fontSize: '3.5rem', lineHeight: 1.12 }}>
              Feasibility
              <br /> calculation FREE
            </Typography>

            <Typography sx={{ fontSize: '2rem', fontWeight: 600, color: 'text.secondary' }}>
              Just enter your data and get your feasibility - its quick easy and free
            </Typography>
            <Button variant='contained' sx={{ mt: 7 }}>
              Sign up for free
            </Button>
          </Grid>
          <Grid item xs={12} sm={5}>
            <Grid container spacing={6}>
              <Grid item xs={12}>
                <CardTab>
                  <Typography variant='h6' sx={{}}>
                    Get free feasibility calculation done for free
                  </Typography>
                </CardTab>
              </Grid>
              <Grid item xs={12}>
                <CardTab>
                  <Typography variant='h6' sx={{}}>
                    Enter your data
                  </Typography>
                </CardTab>
              </Grid>
              <Grid item xs={12}>
                <CardTab>
                  <Typography variant='h6' sx={{}}>
                    Get free feasibility report via email for 33(7)
                  </Typography>
                </CardTab>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  )
}

export default ImageCardContent
