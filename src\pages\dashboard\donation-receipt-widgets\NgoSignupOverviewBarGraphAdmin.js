import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
    Box,
    Typography,
    Paper,
    CircularProgress,
    Tabs,
    Tab
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";

// Dynamically import ApexCharts
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

const NgoSignupOverviewAdmin = () => {
    // Use theme from Material-UI context
    const theme = useTheme();

    // Define initial state for dynamic data
    const [title] = useState("NGO Signup Overview");
    const [tab, setTab] = useState(0);
    const [series, setSeries] = useState([]);
    const [categories, setCategories] = useState([]); // Last 5 years
    const [loading, setLoading] = useState(false);

    const handleChange = (_, newValue) => {
        setTab(newValue);
    };

    // Placeholder for future GET endpoint integration
    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            let freq = tab === 0 ? "YEARLY" : tab === 1 ? "MONTHLY" : tab === 2 ? "WEEKLY" : tab === 3 ? "DAILY" :"";
            
            try {
               const url = `${getUrl(
                         authConfig.dashboardStatisticsEndpoint + "/org-signup-data?graphType=" + freq
                       )}`;
               
                       const response = await axios({
                         method: "get",
                         url: url,
                         headers: getAuthorizationHeaders(),
                       });
                       const data = response.data;
                       const extractedSeries = data.map(item => parseFloat(item.count));
                       const extractedLabels = data.map(item => item.label);
               
                       setSeries([{ name: "Signups", data: extractedSeries }]);
                       setCategories (extractedLabels);
               
            } catch (error) {
                console.error('Error fetching signup data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [tab]); // Re-run when tab changes to fetch relevant data

    // Chart options
    const chartOptions = {
        chart: {
            type: 'bar',
            toolbar: { show: false },
            zoom: { enabled: false },
            foreColor: theme.palette.text.secondary,
        },
        colors: [theme.palette.primary.main],
        plotOptions: {
            bar: {
                borderRadius: 4,
                columnWidth: '35%',
                distributed: false,
            }
        },
        dataLabels: {
            enabled: false,
        },
        xaxis: {
            categories: categories,
            axisBorder: { show: false },
            axisTicks: { show: false },
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        yaxis: {
            title: {
                text: 'NGOs Signed Up',
                style: {
                    fontSize: '12px',
                    fontWeight: 500,
                }
            },
            labels: {
                style: {
                    fontSize: '12px',
                },
                formatter: (value) => `${Math.floor(value)}`,
            },
        },
        grid: {
            borderColor: theme.palette.divider,
            strokeDashArray: 3,
            xaxis: {
                lines: {
                    show: false
                }
            },
            yaxis: {
                lines: {
                    show: true
                }
            }
        },
        tooltip: {
            theme: theme.palette.mode,
            y: {
                formatter: (value) => `${Math.floor(value)} Signups`,
            }
        }
    };

    return (
        <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
                {title}
            </Typography>
            
            <Box sx={{ mb: 2 }}>
                <Tabs
                    value={tab}
                    onChange={handleChange}
                    variant="scrollable"
                    scrollButtons="auto"
                    aria-label="signup period tabs"
                >
                    <Tab label="Yearly" />
                    <Tab label="Monthly" />
                    <Tab label="Weekly" />
                    <Tab label="Daily" />
                </Tabs>
            </Box>

            {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                    <CircularProgress />
                </Box>
            ) : series.length > 0 && categories.length > 0 ? (
                <Chart
                    options={chartOptions}
                    series={series}
                    type="bar"
                    height={300}
                />
            ) : (
                <Typography color="error">
                    Could not load NGO signup data.
                </Typography>
            )}
        </Paper>
    );
};

export default NgoSignupOverviewAdmin;