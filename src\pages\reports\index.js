import React, { useState } from "react";
import { <PERSON>, Button, Typography, Grid, Tabs, Tab } from "@mui/material";
import { Download } from "@mui/icons-material";
import Statistics from "./Statistics";
import AdvancedSearch from "./AdvancedSearch";
import LandingPage from "./DonationReciept";
import DonationHead from "./DonationHead";
import DonorReport from "./DonorReport";
import DonationTypeReport from "./DonationTypeReport";
import PaymentMode from "./PaymentMode";
import { useRBAC } from "../permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

const Report = () => {
  const [userList, setUserList] = useState([]);
  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [tabIndex, setTabIndex] = useState(0);

  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const handleTabChange = (event, newValue) => {
    setTabIndex(newValue);
  };

  const convertedArray = userList?.map((item) => ({
    value: item.id,
    key: item.name,
  }));

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false);
  };

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters);
    setSearchingState(true);
  };

  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };

  const { canMenuPage, rbacRoles } = useRBAC();
  const router = useRouter();

  const canAccessReports = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.REPORTS, requiredPermission);

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessReports(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);
  if (canAccessReports(PERMISSIONS.READ)) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h5" fontWeight="bold">
          Donation Reports
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Filter and analyze your donation data
        </Typography>

        <Box
          sx={{
            display: "flex",
            flexWrap: "wrap",
            gap: 2,
            alignItems: "center",
            mb: 3,
          }}
        >
          <Grid sx={{ textTransform: "none", ml: "auto" }}>
            <AdvancedSearch
              open={openAdvancedSearch}
              toggle={handleAdvancedSearch}
              searchingState={searchingState}
              setSearchingState={setSearchingState}
              roles={convertedArray}
              selectedFilters={selectedFilters}
              clearAllFilters={clearAllFilters}
              onApplyFilters={handleApplyFilters}
            />
          </Grid>

          <Button
            variant="contained"
            startIcon={<Download />}
            sx={{ textTransform: "none" }}
          >
            Export to Excel
          </Button>
        </Box>

        {/* TABS SECTION */}
        <Box sx={{ bgcolor: "#f5f7fa", borderRadius: 2, mb: 3 }}>
          <Tabs
            value={tabIndex}
            onChange={handleTabChange}
            sx={{ px: 2 }}
            TabIndicatorProps={{
              style: {
                backgroundColor: "#1f1f7a",
                height: 3,
                borderRadius: 2,
              },
            }}
          >
            <Tab label="Receipt Report" sx={{ textTransform: "none" }} />
            <Tab label="Head-wise Report" sx={{ textTransform: "none" }} />
            <Tab label="Donor Report" sx={{ textTransform: "none" }} />
            <Tab label="Donation Type Report" sx={{ textTransform: "none" }} />
            <Tab label="Payment Mode Report" sx={{ textTransform: "none" }} />
          </Tabs>
        </Box>

        {/* CONTENT FOR ACTIVE TAB (Optional) */}
        {tabIndex === 0 && (
          <div>
            <LandingPage />
          </div>
        )}
        {tabIndex === 1 && (
          <div>
            <DonationHead />
          </div>
        )}
        {tabIndex === 2 && (
          <div>
            <DonorReport />
          </div>
        )}
        {tabIndex === 3 && (
          <div>
            <DonationTypeReport />
          </div>
        )}
        {tabIndex === 4 && (
          <div>
            <PaymentMode />
          </div>
        )}
      </Box>
    );
  } else {
    return null;
  }
};

export default Report;
