import React, { useState } from "react";
import {
  ClickAwayListener,
  Box,
  Typography,
  Grid,
  IconButton,
  Stack,
} from "@mui/material";

import { useRouter } from "next/router";
import Icon from "src/@core/components/icon";
import { ExpandMore, ExpandLess } from "@mui/icons-material";
import TopNavigation from "src/top-menu";


function TopMenu() {
  const [isOpen, setIsOpen] = useState(true);
  const router = useRouter();

  const menuActions = TopNavigation();

  console.log("MENU ACTIONS", TopNavigation());

  const handleClickAway = () => {
    setIsOpen(false);
  };

  const handleActionClick = (path) => {
    router.push(path);
    setIsOpen(false);
  };

  const [expandedCategories, setExpandedCategories] = useState([]);

  const handleCategoryToggle = (title) => {
    setExpandedCategories((prevExpandedCategories) => {
      if (prevExpandedCategories.includes(title)) {
        return prevExpandedCategories.filter((menu) => menu !== title);
      } else {
        return [...prevExpandedCategories, title];
      }
    });
  };

  return (
    <>
      {isOpen && (
        <ClickAwayListener onClickAway={handleClickAway}>
          <Box
            sx={{
              position: "fixed",
              top: 0,
              left: 0,
              zIndex: 1100,
              background: "white",
              borderRadius: "10px",
              boxShadow: "0 8px 16px rgba(0,0,0,0.1)",
              width: "60%",
              height: "400px",
              overflow: "auto",
              marginLeft: 100,
              marginTop: 12.5,
              "@media (max-width: 600px)": {
                width: "90%",
                marginLeft: "35px",
              },
              "@media (min-width : 601px) and (max-width: 768px)": {
                width: "70%",
                marginLeft: "220px",
              },
              padding: "10px",
              boxSizing: "border-box",
            }}
          >
            <Grid container spacing={2}>
              {menuActions?.map((action, index) => (
                <Grid item xs={12} key={index}>
                  <Grid onClick={() => handleCategoryToggle(action.title)}>
                    <Typography
                      variant="h6"
                      style={{
                        background: "#fff7e6",
                        fontSize: "14px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "space-between",
                        paddingLeft: "15px",
                        cursor: "pointer",
                        transition: "background-color 0.3s",
                        ":hover": {
                          background: "#fff7e6",
                        },
                      }}
                    >
                      {action.title}
                      <IconButton size="small">
                        {expandedCategories.includes(action.title) ? (
                          <ExpandLess />
                        ) : (
                          <ExpandMore />
                        )}
                      </IconButton>
                    </Typography>
                  </Grid>

                  {expandedCategories.includes(action.title) && (
                    <Grid container spacing={2}>
                      {action.path !== null && (
                        <Grid item xs={6} sm={4} lg={3}>
                          <Box
                            sx={{
                              width: "100%",
                              display: "flex",
                              justifyContent: "center",
                              alignItems: "center",
                              height: "80px",
                              margin: "5px",
                              "&:hover": {
                                background: "#f2f7f2",
                                cursor: "pointer",
                              },
                            }}
                            onClick={() => handleActionClick(action.path)}
                          >
                            <Stack direction="column" alignItems="center">
                              <IconButton color="inherit">
                                <Icon fontSize="1.5rem" icon={action.icon} />
                              </IconButton>
                              <Typography variant="caption">
                                {action.title}
                              </Typography>
                            </Stack>
                          </Box>
                        </Grid>
                      )}

                      {action.children && (
                        <>
                          {action.children.map((child, childIndex) => (
                            <Grid item xs={6} sm={4} lg={3} key={childIndex}>
                              <Box
                                onClick={() => handleActionClick(child.path)}
                                sx={{
                                  textAlign: "center",
                                  width: "100%",
                                  height: "80px",
                                  display: "flex",
                                  flexDirection: "column",
                                  justifyContent: "center",
                                  alignItems: "center",
                                  margin: "5px",
                                  "&:hover": {
                                    background: "#f2f7f2",
                                    cursor: "pointer",
                                  },
                                }}
                              >
                                <IconButton color="inherit">
                                  <Icon fontSize="1.5rem" icon={child.icon} />
                                </IconButton>
                                <Typography variant="caption" component="div">
                                  {child.title}
                                </Typography>
                              </Box>
                            </Grid>
                          ))}
                        </>
                      )}
                    </Grid>
                  )}
                </Grid>
              ))}
            </Grid>
          </Box>
        </ClickAwayListener>
      )}
    </>
  );
}

export default TopMenu;
