import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  Ty<PERSON><PERSON>,
  Button,
  Box,
  Grid,
  TextField,
  FormControl,
  FormControlLabel,
  Switch,
  InputAdornment,
  Tooltip,
  IconButton,
  FormHelperText,
  CircularProgress,
} from "@mui/material";
import PeopleIcon from "@mui/icons-material/People";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import PlaceIcon from "@mui/icons-material/Place";
import { Controller } from "react-hook-form";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";
import toast, { Toaster } from "react-hot-toast";
import dynamic from "next/dynamic";
import Header from "./Header";
import DatePickerWrapper from "src/@core/styles/libs/react-datepicker";
import Icon from "src/@core/components/icon";

const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });

const EventDialog = ({
  addEventSidebarOpen,
  handleDialogClose,
  handleSubmit,
  onSubmit,
  control,
  errors,
  user,
  store,
  eventData,
  attendeesId,
  setAttendeesId,
  listAttendeesOptions,
  employeesOptions,
  startDate,
  setStartDate,
  handleStartDateChange,
  handleJoinClick,
  usedLinks,
  allDay,
  setAllDay,
  inPersonEvent,
  setInPersonEvent,
  isSmallScreen,
  startTime,
  handleGoogleMeet,
  handleIconHover,
  setStartTime,
  getValues,
  resetToEmptyValues,
  handleDeleteEvent,
  handleCopyClick,
  onLoad,
}) => {
  const dialogTitle =
    store?.selectedEvent !== null && store?.selectedEvent?.title?.length
      ? user?.id === eventData?.createdBy
        ? "Update Event"
        : "View Event"
      : "Add Event";

  function removeHtmlTags(text) {
    return text?.replace(/<[^>]*>/g, " ");
  }
  return (
    <>
      <Dialog
        fullWidth
        open={addEventSidebarOpen}
        onClose={handleDialogClose}
        keepMounted
        PaperProps={{
          sx: {
            top: 0,
            marginTop: 0,
            width: "1000px",
            display: "flex",
            flexDirection: "column",
            maxWidth: "none",
          },
        }}
      >
        <DialogTitle>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "5px",
            }}
          >
            <Header
              title={
                store?.selectedEvent !== null &&
                store?.selectedEvent?.title?.length ? (
                  <Typography
                    sx={{
                      fontSize: {
                        md: "1.125rem !important", // Font size for extra-small screens
                        sm: "inherit", // Inherit font size for small screens and up
                      },
                      "@media (max-width:320px)": {
                        fontSize: "0.9rem !important", // Specific font size for max-width 320px
                      },
                    }}
                  >
                    {user?.id === eventData?.createdBy ? (
                      <span>Update Event</span>
                    ) : (
                      <span>View Event</span>
                    )}
                  </Typography>
                ) : (
                  <Typography
                    sx={{
                      fontSize: {
                        md: "1.125rem !important", // Font size for extra-small screens
                        sm: "inherit", // Inherit font size for small screens and up
                      },
                      "@media (max-width:320px)": {
                        fontSize: "0.9rem !important", // Specific font size for max-width 320px
                      },
                    }}
                  >
                    Add Event
                  </Typography>
                )
              }
              onClose={handleDialogClose}
              showCloseButton={false}
            />
            <IconButton sx={{ display: "none !important" }} />
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                "@media (max-width:320px)": {
                  gap: "4px !important", // Override gap for max-width 320px
                },
              }}
            >
              {store?.selectedEvent !== null &&
                store?.selectedEvent?.title?.length > 0 && (
                  <Button
                    variant="contained"
                    color="primary"
                    sx={{
                      fontSize: {
                        xs: "0.7rem !important",
                        lg: "0.873rem !important",
                      },
                      padding: {
                        xs: "5px 11px !important",
                        sm: "6px 13px !important",
                      },
                      "@media (max-width:320px)": {
                        fontSize: "0.6rem !important",
                        padding: "4px 7px !important",
                      },
                    }}
                    onClick={handleJoinClick}
                  >
                    Join
                  </Button>
                )}
              <Button
                variant="outlined"
                onClick={handleDialogClose}
                sx={{
                  color: "black !important",
                  fontSize: {
                    xs: "0.7rem !important",
                    lg: "0.873rem !important",
                  },
                  padding: {
                    xs: "5px 11px !important",
                    sm: "6px 13px !important",
                  },
                  "@media (max-width:320px)": {
                    fontSize: "0.6rem !important",
                    padding: "4px 5px !important",
                  },
                  marginRight: { xl: 4, lg: 0, md: 0, sm: 0, xs: 0 },
                }}
              >
                Close
              </Button>
            </Box>
          </div>
          <Divider sx={{ border: "1px solid black" }} />
        </DialogTitle>
        <DialogContent sx={{ flex: "1 1 auto", overflowY: "auto" }}>
          {user?.id === eventData?.createdBy ? (
            <DatePickerWrapper>
              <Grid container spacing={5}>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                    // error={Boolean(errors.attendeesId)}
                  />
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <Icon icon="pepicons-pencil:pen" />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <FormControl fullWidth sx={{ mb: 2 }}>
                      <Controller
                        name="title"
                        control={control}
                        rules={{
                          required: true,
                          pattern: /^[a-zA-Z0-9]+(\s?[a-zA-Z0-9]+)*$/,
                          message: "Special characters are not allowed",
                        }}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="Title"
                            value={value}
                            onChange={(e) => {
                              if (e.target.value?.length <= 255) {
                                onChange(e);
                              }
                            }}
                            error={Boolean(errors.title) || value?.length > 255}
                            sx={{ flexGrow: 1 }}
                            size="small"
                            InputProps={{
                              maxLength: 255,
                            }}
                            helperText={
                              value?.length > 255
                                ? "Character limit exceeded! Please enter 255 characters or less."
                                : `${value.length} / 255 characters`
                            }
                          />
                        )}
                      />
                      {errors?.title && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-title-error"
                        >
                          {errors.title.type === "pattern"
                            ? "Special characters and multiple whitespace are not allowed"
                            : "This field is required"}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <PeopleIcon />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <MultiSelectAutoComplete
                      id="attendeesId"
                      label="Add required attendees"
                      nameArray={
                        user.organisationCategory === "EMPLOYEE"
                          ? listAttendeesOptions
                          : employeesOptions
                      }
                      value={attendeesId}
                      onChange={(e) => {
                        setAttendeesId(e.target.value);
                      }}
                      error={Boolean(errors.attendeesId)}
                    />
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={1}
                    md={1}
                    lg={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <AccessTimeIcon />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                        md: "0 !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                      <Controller
                        name="startDate"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <TextField
                            label="start date"
                            type="date"
                            inputProps={{
                              min: startDate,
                            }}
                            value={startDate}
                            onChange={(e) => {
                              field.onChange(e.target.value);
                              handleStartDateChange(e.target.value);
                            }}
                            InputLabelProps={{ shrink: true }}
                            error={Boolean(errors.start)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                          />
                        )}
                      />
                      {errors.start && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-start-date-time-error"
                        >
                          This field is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        lg: "25px",
                        md: "25px !important",
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    {!allDay && (
                      <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                        <Controller
                          name="startTime"
                          control={control}
                          rules={{ required: "Start Time is required" }}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              size="small"
                              label="Start Time"
                              type="time"
                              InputLabelProps={{ shrink: true }}
                              error={Boolean(errors.startTime)}
                              value={field.value || startTime}
                              helperText={errors.startTime?.message}
                              aria-describedby="startTime"
                              onChange={(e) => {
                                field.onChange(e); // This is important to update the form state
                                setStartTime(e.target.value); // Update your state
                              }}
                            />
                          )}
                        />
                      </FormControl>
                    )}
                  </Grid>
                  <Grid
                    item
                    xs={12} // Full width on extra small screens
                    sm={3}
                    md={2}
                    lg={3}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "0px !important",
                        lg: "1rem !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                      <FormControlLabel
                        label="All Day"
                        sx={{ marginLeft: { sm: "1rem !important" } }}
                        control={
                          <Switch
                            checked={allDay}
                            onChange={(e) => {
                              setAllDay(e.target.checked);
                            }}
                          />
                        }
                      />
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>

                  <Grid
                    container
                    item
                    lg={8}
                    md={8}
                    sm={10}
                    xs={12}
                    spacing={2}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "1rem !important",
                        md: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <Grid
                      item
                      xs={12}
                      sm={4.7}
                      md={6}
                      sx={{ paddingLeft: { sm: "0px !important" } }}
                    >
                      <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                        <Controller
                          name="endDate"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <TextField
                              label="end date"
                              type="date"
                              value={field.value}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                              }}
                              InputLabelProps={{ shrink: true }}
                              error={Boolean(errors.end)}
                              sx={{ flexGrow: 1 }}
                              size="small"
                              inputProps={{
                                min: startDate, // Minimum selectable date
                              }}
                            />
                          )}
                        />
                        {errors.end && (
                          <FormHelperText
                            sx={{ color: "error.main" }}
                            id="event-end-date-error"
                          >
                            This field is required
                          </FormHelperText>
                        )}
                      </FormControl>
                    </Grid>

                    <Grid
                      item
                      xs={12}
                      sm={4.9}
                      md={6}
                      sx={{
                        paddingLeft: {
                          lg: "25px",
                          md: "25px !important",
                          xs: "0.5rem !important",
                        },
                      }}
                    >
                      {!allDay && (
                        <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                          <Controller
                            name="endTime"
                            control={control}
                            defaultValue=""
                            rules={{
                              required: "End Time is required",
                              validate: (value) => {
                                const startTimeValue = getValues("startTime");
                                if (startTimeValue && value) {
                                  const startTime = new Date(
                                    `1970-01-01T${startTimeValue}:00`
                                  );
                                  const endTime = new Date(
                                    `1970-01-01T${value}:00`
                                  );
                                  if (endTime < startTime) {
                                    return "End Time cannot be before Start Time";
                                  }
                                }
                                return true;
                              },
                            }}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                size="small"
                                label="End Time"
                                type="time"
                                InputLabelProps={{ shrink: true }}
                                error={Boolean(errors.endTime)}
                                helperText={errors.endTime?.message}
                                aria-describedby="endTime"
                              />
                            )}
                          />
                        </FormControl>
                      )}
                    </Grid>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2}>
                  <Grid
                    item
                    xs={1}
                    sx={{ paddingLeft: { md: "1.5rem !important" } }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          md: "block !important",
                        },
                      }}
                    >
                      <PlaceIcon />
                    </IconButton>
                  </Grid>
                  <Grid item xs={11}>
                    <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                      <FormControlLabel
                        label="In-person Meet"
                        sx={{ marginLeft: { sm: "1rem !important" } }}
                        control={
                          <Switch
                            checked={inPersonEvent}
                            onChange={(e) => {
                              setInPersonEvent(e.target.checked);
                            }}
                          />
                        }
                      />
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{
                      paddingLeft: { xs: "25px", md: "0rem !important" },
                      paddingTop: { xs: "0rem !important" },
                    }}
                  >
                    <FormControl fullWidth>
                      <Controller
                        name="searchForRoom"
                        control={control}
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="search for room"
                            value={value}
                            onChange={onChange}
                            error={Boolean(errors.searchForRoom)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                          />
                        )}
                      />
                      {errors.searchForRoom && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-search-for-room"
                        >
                          This field is required
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                    mt={1.5}
                    mb={1.5}
                  >
                    <FormControl fullWidth>
                      <Controller
                        name="url"
                        control={control}
                        rules={
                          store?.selectedEvent !== null &&
                          store?.selectedEvent?.title?.length &&
                          user?.id === eventData?.createdBy
                            ? false
                            : {
                                required: true,
                                pattern: {
                                  value:
                                    /^(https:\/\/)?meet\.google\.com\/[a-zA-Z0-9-]+$/,
                                  message: "Invalid Google Meet link",
                                },
                                validate: (value) =>
                                  !usedLinks.includes(value) ||
                                  "This Google Meet link has already been used. Please use a different link.",
                              }
                        }
                        render={({ field: { value, onChange } }) => (
                          <TextField
                            label="Meeting Link"
                            value={value}
                            onChange={onChange}
                            error={Boolean(errors.url)}
                            sx={{ flexGrow: 1 }}
                            size="small"
                            placeholder="Click on the Google-meet icon to navigate & paste the url here"
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="end">
                                  <Box
                                    sx={{
                                      display: "flex",
                                      alignItems: "center",
                                      gap: "0.5rem",
                                    }}
                                  >
                                    <Tooltip title="Copy" arrow>
                                      <Icon
                                        onClick={() => handleCopyClick(value)}
                                        cursor="pointer"
                                        icon="ph:copy"
                                        width="30"
                                        height="30"
                                        mr={1}
                                      />
                                    </Tooltip>
                                    <Icon
                                      onClick={handleGoogleMeet}
                                      onMouseOver={handleIconHover}
                                      cursor="pointer"
                                      icon="logos:google-meet"
                                    />
                                  </Box>
                                  <Toaster position="top-right" />
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                      {errors.url && (
                        <FormHelperText
                          sx={{ color: "error.main" }}
                          id="event-url-error"
                        >
                          {errors.url.message}
                        </FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    <FormControl fullWidth sx={{ mb: 4 }}>
                      <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                          <ReactQuill
                            {...field}
                            theme="snow"
                            modules={{
                              toolbar: [
                                [
                                  { header: "1" },
                                  { header: "2" },
                                  { font: [] },
                                ],
                                [{ size: [] }],
                                [
                                  "bold",
                                  "italic",
                                  "underline",
                                  "strike",
                                  "blockquote",
                                ],
                                [
                                  { list: "ordered" },
                                  { list: "bullet" },
                                  { indent: "-1" },
                                  { indent: "+1" },
                                ],
                                ["clean"],
                              ],
                            }}
                            formats={[
                              "header",
                              "font",
                              "size",
                              "bold",
                              "italic",
                              "underline",
                              "strike",
                              "blockquote",
                              "list",
                              "bullet",
                              "indent",
                              "link",
                              "image",
                            ]}
                            placeholder="Enter description"
                            style={{ height: "200px" }}
                            onChange={(content) => field.onChange(content)}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                </Grid>
              </Grid>
            </DatePickerWrapper>
          ) : (
            <DatePickerWrapper>
              <Grid container spacing={5}>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                    error={Boolean(errors.attendeesId)}
                  />
                </Grid>
                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <Icon icon="pepicons-pencil:pen" />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    {store.selectedEvent !== null && eventData ? (
                      <Grid container item xs={12} sm={12} spacing={2}>
                        <Grid item>
                          <Typography className="data-field">
                            Title:&nbsp;
                            <b>{store.selectedEvent?.title || "No Title"}</b>
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <FormControl fullWidth sx={{ mb: 2 }}>
                        <Controller
                          name="title"
                          control={control}
                          rules={{
                            required: true,
                            pattern: /^[a-zA-Z0-9]+(\s?[a-zA-Z0-9]+)*$/,
                          }}
                          render={({ field: { value, onChange } }) => (
                            <TextField
                              label="Title"
                              value={value}
                              onChange={(e) => {
                                if (e.target.value.length <= 255) {
                                  onChange(e);
                                }
                              }}
                              error={
                                Boolean(errors.title) || value.length > 255
                              }
                              sx={{ flexGrow: 1 }}
                              size="small"
                              InputProps={{
                                readOnly:
                                  store.selectedEvent !== null && eventData,
                                maxLength: 255,
                              }}
                              helperText={
                                value.length > 255
                                  ? "Character limit exceeded! Please enter 255 characters or less."
                                  : `${value.length} / 255 characters`
                              }
                            />
                          )}
                        />
                        {errors.title && (
                          <FormHelperText
                            sx={{ color: "error.main" }}
                            id="event-title-error"
                          >
                            {errors.title.type === "pattern"
                              ? "Special characters and multiple whitespace are not allowed"
                              : "This field is required"}
                          </FormHelperText>
                        )}
                      </FormControl>
                    )}
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <PeopleIcon />
                    </IconButton>
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    {store.selectedEvent !== null && eventData ? (
                      <Grid container item xs={12} sm={12} spacing={2}>
                        <Grid item>
                          <Typography className="data-field">
                            Attendees:&nbsp;
                            <b>
                              {attendeesId && Array.isArray(attendeesId)
                                ? attendeesId.map((att) => att.key).join(", ")
                                : "No attendees"}
                            </b>
                          </Typography>
                        </Grid>
                      </Grid>
                    ) : (
                      <MultiSelectAutoComplete
                        id="attendeesId"
                        label="Add required attendees"
                        nameArray={
                          user.organisationCategory === "EMPLOYEE"
                            ? listAttendeesOptions
                            : employeesOptions
                        }
                        value={attendeesId}
                        onChange={(e) => {
                          setAttendeesId(e.target.value);
                        }}
                        error={Boolean(errors.attendeesId)}
                      />
                    )}
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  {/* Icon Section */}
                  <Grid
                    item
                    xs={12}
                    sm={1}
                    md={1}
                    lg={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          sm: "block !important",
                        },
                      }}
                    >
                      <AccessTimeIcon />
                    </IconButton>
                  </Grid>

                  {/* Start Date Field */}
                  <Grid
                    item
                    xs={12}
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                        md: "0 !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    {store.selectedEvent !== null && eventData ? (
                      <Typography className="data-field">
                        Start Date:&nbsp;
                        <b>{eventData?.startDate || "-"}</b>
                      </Typography>
                    ) : (
                      <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                        <Controller
                          name="startDate"
                          control={control}
                          rules={{ required: true }}
                          render={({ field }) => (
                            <TextField
                              label="start date"
                              type="date"
                              inputProps={{
                                min: startDate,
                              }}
                              value={startDate}
                              onChange={(e) => {
                                field.onChange(e.target.value);
                                handleStartDateChange(e.target.value);
                              }}
                              InputProps={{
                                readOnly:
                                  store.selectedEvent !== null && eventData,
                              }}
                              InputLabelProps={{ shrink: true }}
                              error={Boolean(errors.start)}
                              sx={{ flexGrow: 1 }}
                              size="small"
                            />
                          )}
                        />
                        {errors.start && (
                          <FormHelperText
                            sx={{ color: "error.main" }}
                            id="event-start-date-time-error"
                          >
                            This field is required
                          </FormHelperText>
                        )}
                      </FormControl>
                    )}
                  </Grid>

                  {/* Start Time Field */}
                  <Grid
                    item
                    xs={12}
                    sm={4}
                    md={4}
                    lg={4}
                    sx={{
                      paddingLeft: {
                        lg: "25px",
                        md: "25px !important",
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    {store.selectedEvent !== null && eventData ? (
                      <Typography className="data-field">
                        Start Time:&nbsp;
                        <b>{eventData?.startTime || "-"}</b>
                      </Typography>
                    ) : (
                      !allDay && (
                        <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                          <Controller
                            name="startTime"
                            control={control}
                            rules={{ required: "Start Time is required" }}
                            render={({ field }) => (
                              <TextField
                                {...field}
                                size="small"
                                label="Start Time"
                                type="time"
                                InputLabelProps={{ shrink: true }}
                                error={Boolean(errors.startTime)}
                                value={field.value || startTime}
                                helperText={errors.startTime?.message}
                                aria-describedby="startTime"
                                onChange={(e) => {
                                  field.onChange(e); // This is important to update the form state
                                  setStartTime(e.target.value); // Update your state
                                }}
                              />
                            )}
                          />
                        </FormControl>
                      )
                    )}
                  </Grid>

                  {/* All Day Switch */}
                  <Grid
                    item
                    xs={12}
                    sm={3}
                    md={2}
                    lg={3}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "0px !important",
                        lg: "1rem !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                      <FormControlLabel
                        label="All Day"
                        sx={{ marginLeft: { sm: "1rem !important" } }}
                        control={
                          <Switch
                            checked={allDay}
                            onChange={(e) => setAllDay(e.target.checked)}
                            disabled={store.selectedEvent !== null && eventData}
                          />
                        }
                      />
                    </FormControl>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  {/* Empty Grid for Alignment (Icon Space) */}
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>

                  {/* End Date & End Time Fields Container */}
                  <Grid
                    container
                    item
                    lg={8}
                    md={8}
                    sm={10}
                    xs={12}
                    spacing={2}
                    sx={{
                      paddingLeft: {
                        xs: isSmallScreen
                          ? "35px !important"
                          : "39px !important",
                        sm: "1rem !important",
                        md: "8px !important",
                      },
                      "@media (max-width: 375px)": {
                        paddingLeft: "35px !important",
                      },
                      "@media (max-width: 320px)": {
                        paddingLeft: "30px !important",
                      },
                    }}
                  >
                    {/* End Date Field */}
                    <Grid
                      item
                      xs={12}
                      sm={4.7}
                      md={6}
                      sx={{ paddingLeft: { sm: "0px !important" } }}
                    >
                      {store.selectedEvent !== null && eventData ? (
                        <Typography className="data-field">
                          End Date:&nbsp;
                          <b>{eventData?.endDate || "-"}</b>
                        </Typography>
                      ) : (
                        <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                          <Controller
                            name="endDate"
                            control={control}
                            rules={{ required: true }}
                            render={({ field }) => (
                              <TextField
                                label="End Date"
                                type="date"
                                value={field.value}
                                onChange={(e) => {
                                  field.onChange(e.target.value);
                                }}
                                InputLabelProps={{ shrink: true }}
                                error={Boolean(errors.endDate)}
                                sx={{ flexGrow: 1 }}
                                size="small"
                                InputProps={{
                                  readOnly:
                                    store.selectedEvent !== null && eventData,
                                }}
                                inputProps={{
                                  min: startDate, // Ensuring end date is after start date
                                }}
                              />
                            )}
                          />
                          {errors.endDate && (
                            <FormHelperText sx={{ color: "error.main" }}>
                              This field is required
                            </FormHelperText>
                          )}
                        </FormControl>
                      )}
                    </Grid>

                    {/* End Time Field */}
                    <Grid
                      item
                      xs={12}
                      sm={4.9}
                      md={6}
                      sx={{
                        paddingLeft: {
                          lg: "25px",
                          md: "25px !important",
                          xs: "0.5rem !important",
                        },
                      }}
                    >
                      {store.selectedEvent !== null && eventData ? (
                        <Typography className="data-field">
                          End Time:&nbsp;
                          <b>{eventData?.endTime || "-"}</b>
                        </Typography>
                      ) : (
                        !allDay && (
                          <FormControl fullWidth sx={{ mb: { xs: 2, md: 0 } }}>
                            <Controller
                              name="endTime"
                              control={control}
                              defaultValue=""
                              rules={{
                                required: "End Time is required",
                                validate: (value) => {
                                  const startTimeValue = getValues("startTime");
                                  if (startTimeValue && value) {
                                    const startTime = new Date(
                                      `1970-01-01T${startTimeValue}:00`
                                    );
                                    const endTime = new Date(
                                      `1970-01-01T${value}:00`
                                    );
                                    if (endTime < startTime) {
                                      return "End Time cannot be before Start Time";
                                    }
                                  }
                                  return true;
                                },
                              }}
                              render={({ field }) => (
                                <TextField
                                  {...field}
                                  size="small"
                                  label="End Time"
                                  type="time"
                                  InputLabelProps={{ shrink: true }}
                                  error={Boolean(errors.endTime)}
                                  helperText={errors.endTime?.message}
                                  aria-describedby="endTime"
                                  InputProps={{
                                    readOnly:
                                      store.selectedEvent !== null && eventData,
                                  }}
                                />
                              )}
                            />
                          </FormControl>
                        )
                      )}
                    </Grid>
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2}>
                  <Grid
                    item
                    xs={1}
                    sx={{ paddingLeft: { md: "1.5rem !important" } }}
                  >
                    <IconButton
                      sx={{
                        display: {
                          xs: "none !important",
                          md: "block !important",
                        },
                      }}
                    >
                      <PlaceIcon />
                    </IconButton>
                  </Grid>
                  {/* In-Person Meet Switch */}
                  <Grid item xs={11}>
                    {store.selectedEvent !== null && eventData ? (
                      <Typography className="data-field">
                        In-person Meet:&nbsp;
                        <b>{inPersonEvent ? "Yes" : "No"}</b>
                      </Typography>
                    ) : (
                      <FormControl sx={{ mb: { xs: 2, md: 0 } }}>
                        <FormControlLabel
                          label="In-person Meet"
                          sx={{ marginLeft: { sm: "1rem !important" } }}
                          control={
                            <Switch
                              checked={inPersonEvent}
                              onChange={(e) =>
                                setInPersonEvent(e.target.checked)
                              }
                              disabled={
                                store.selectedEvent !== null && eventData
                              } // Ensuring it's disabled when eventData is true
                            />
                          }
                        />
                      </FormControl>
                    )}
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  {/* Empty Grid for Alignment (Icon Space) */}
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>

                  {/* Search for Room Field */}
                  <Grid
                    item
                    xs={11}
                    sx={{
                      paddingLeft: { xs: "25px", md: "0rem !important" },
                      paddingTop: { xs: "0rem !important" },
                    }}
                  >
                    {store.selectedEvent !== null && eventData ? (
                      <Typography className="data-field">
                        Room Members:&nbsp;
                        <b>
                          {control._formValues.searchForRoom || "Not Specified"}
                        </b>
                      </Typography>
                    ) : (
                      <FormControl fullWidth>
                        <Controller
                          name="searchForRoom"
                          control={control}
                          rules={{ required: "This field is required" }}
                          render={({ field: { value, onChange } }) => (
                            <TextField
                              label="Search for Room"
                              value={value}
                              onChange={onChange}
                              error={Boolean(errors.searchForRoom)}
                              sx={{ flexGrow: 1 }}
                              size="small"
                              InputProps={{
                                readOnly:
                                  store.selectedEvent !== null && eventData,
                              }}
                            />
                          )}
                        />
                        {errors.searchForRoom && (
                          <FormHelperText sx={{ color: "error.main" }}>
                            {errors.searchForRoom.message}
                          </FormHelperText>
                        )}
                      </FormControl>
                    )}
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  {/* Empty Grid for Alignment (Icon Space) */}
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>

                  {/* Meeting Link Input Field */}
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                    mt={1.5}
                    mb={1.5}
                  >
                    {store?.selectedEvent !== null &&
                    store.selectedEvent?.title?.length ? (
                      <Typography className="data-field">
                        Meeting Link:&nbsp;
                        {eventData ? (
                          <a
                            href={eventData?.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{ color: "#1a73e8", textDecoration: "none" }}
                          >
                            {eventData?.url || "-"}
                          </a>
                        ) : (
                          <b>-</b>
                        )}
                      </Typography>
                    ) : (
                      <FormControl fullWidth>
                        <Controller
                          name="url"
                          control={control}
                          rules={{
                            required: "Meeting link is required",
                            pattern: {
                              value:
                                /^(https:\/\/)?meet.google.com\/[a-zA-Z0-9-]+$/,
                              message: "Invalid Google Meet link",
                            },
                            validate: (value) =>
                              !usedLinks.includes(value) ||
                              "This Google Meet link has already been used. Please use a different link.",
                          }}
                          render={({ field: { value, onChange } }) => (
                            <TextField
                              label="Meeting Link"
                              value={value}
                              onChange={onChange}
                              error={Boolean(errors.url)}
                              sx={{ flexGrow: 1 }}
                              size="small"
                              placeholder="Click on the Google Meet icon to navigate & paste the URL here"
                              InputProps={{
                                readOnly:
                                  store?.selectedEvent !== null &&
                                  store.selectedEvent?.title?.length,
                                endAdornment: (
                                  <InputAdornment position="end">
                                    <Box
                                      sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: "0.5rem",
                                      }}
                                    >
                                      <Tooltip title="Copy" arrow>
                                        <Icon
                                          onClick={() => handleCopyClick(value)}
                                          cursor="pointer"
                                          icon="ph:copy"
                                          width="30"
                                          height="30"
                                          mr={1}
                                        />
                                      </Tooltip>
                                      <Tooltip title="Open Google Meet" arrow>
                                        <Icon
                                          onClick={handleGoogleMeet}
                                          onMouseOver={handleIconHover}
                                          cursor="pointer"
                                          icon="logos:google-meet"
                                        />
                                      </Tooltip>
                                    </Box>
                                    <Toaster position="top-right" />
                                  </InputAdornment>
                                ),
                              }}
                            />
                          )}
                        />
                        {errors.url && (
                          <FormHelperText sx={{ color: "error.main" }}>
                            {errors.url.message}
                          </FormHelperText>
                        )}
                      </FormControl>
                    )}
                  </Grid>
                </Grid>

                <Grid container alignItems="center" spacing={2} mt={1}>
                  {/* Empty Grid for Alignment (Icon Space) */}
                  <Grid
                    item
                    xs={1}
                    sx={{
                      paddingLeft: {
                        xs: "0 !important",
                        sm: "1.25rem !important",
                      },
                    }}
                  ></Grid>

                  {/* Description Field */}
                  <Grid
                    item
                    xs={11}
                    sx={{ paddingLeft: { xs: "25px", md: "0 !important" } }}
                  >
                    {store?.selectedEvent !== null && eventData ? (
                      <Typography className="data-field">
                        Description:&nbsp;
                        <b>
                          {removeHtmlTags(
                            eventData?.extendedProps?.description
                          ) || "No Description"}
                        </b>
                      </Typography>
                    ) : (
                      <FormControl fullWidth sx={{ mb: 4 }}>
                        <Controller
                          name="description"
                          control={control}
                          render={({ field }) => (
                            <ReactQuill
                              {...field}
                              theme="snow"
                              modules={{
                                toolbar: [
                                  [
                                    { header: "1" },
                                    { header: "2" },
                                    { font: [] },
                                  ],
                                  [{ size: [] }],
                                  [
                                    "bold",
                                    "italic",
                                    "underline",
                                    "strike",
                                    "blockquote",
                                  ],
                                  [
                                    { list: "ordered" },
                                    { list: "bullet" },
                                    { indent: "-1" },
                                    { indent: "+1" },
                                  ],
                                  ["clean"],
                                ],
                              }}
                              formats={[
                                "header",
                                "font",
                                "size",
                                "bold",
                                "italic",
                                "underline",
                                "strike",
                                "blockquote",
                                "list",
                                "bullet",
                                "indent",
                                "link",
                                "image",
                              ]}
                              placeholder="Enter description"
                              style={{ height: "200px" }}
                              readOnly={
                                store.selectedEvent !== null && eventData
                              }
                              onChange={(content) => field.onChange(content)}
                            />
                          )}
                        />
                      </FormControl>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </DatePickerWrapper>
          )}
        </DialogContent>
        {dialogTitle !== "View Event" && (
          <DialogActions
            sx={{
              justifyContent: "end",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
            }}
          >
            {user?.id === eventData?.createdBy ? (
              <>
                <Button
                  type="submit"
                  variant="contained"
                  onClick={handleSubmit(onSubmit)}
                  sx={{
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 5px !important",
                    },
                  }}
                >
                  {store?.selectedEvent?.title?.length ? "Update" : "Add"}
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={() => {
                    store?.selectedEvent?.title?.length
                      ? handleDeleteEvent()
                      : resetToEmptyValues();
                  }}
                  sx={{
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 5px !important",
                    },
                  }}
                >
                  {store?.selectedEvent?.title?.length ? "Delete" : "Reset"}
                </Button>
              </>
            ) : store?.selectedEvent === null ||
              typeof eventData === "undefined" ? (
              <>
                <Button
                  type="submit"
                  variant="contained"
                  onClick={handleSubmit(onSubmit)}
                  sx={{
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 5px !important",
                    },
                  }}
                >
                  {onLoad ? <CircularProgress color="inherit" size={24} /> : "Add"}                 
                </Button>
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={resetToEmptyValues}
                  sx={{
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 5px !important",
                    },
                    marginRight: {
                      xl: 7.5,
                      lg: 3.7,
                      md: 3.7,
                      sm: 3.5,
                      xs: 3.5,
                    },
                  }}
                >
                  Reset
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="contained"
                  color="primary"
                  sx={{
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 7px !important",
                    },
                  }}
                  onClick={handleJoinClick}
                >
                  Join
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleDialogClose}
                  sx={{
                    color: "black !important",
                    fontSize: {
                      xs: "0.7rem !important",
                      lg: "0.873rem !important",
                    },
                    padding: {
                      xs: "5px 11px !important",
                      sm: "6px 13px !important",
                    },
                    "@media (max-width:320px)": {
                      fontSize: "0.6rem !important",
                      padding: "4px 5px !important",
                    },
                    marginRight: { xl: 4, lg: 0, md: 0, sm: 0, xs: 0 },
                  }}
                >
                  Close
                </Button>
              </>
            )}
          </DialogActions>
        )}
      </Dialog>
    </>
  );
};

export default EventDialog;
