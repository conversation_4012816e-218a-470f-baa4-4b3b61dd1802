// ** Demo Components Imports
import { useTheme } from "@emotion/react";

import Icon from "src/@core/components/icon";

// ** Styled Component
import {
  Box,
  Button,
  Card,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  IconButton,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  Typography,
} from "@mui/material";
import MUITableCell from "../SP/MUITableCell";
import { useContext } from "react";
import { AuthContext } from "src/context/AuthContext";

const fieldLabelStyle = {
  fontSize: "12.75px",
};

const fieldValueStyle = {
  fontWeight: "bold",
  wordWrap: "break-word",
  whiteSpace: "pre-wrap",
  // color: "#66BB6A", // Sets color for the TableCell
  color: "#108A00",
  fontSize: "14px",
};

const tablecellLabelStyle = {
  width: { xs: "50%", sm: "50%", md: "40%" }, // Responsive width adjustment
  padding: "4px", // Ensure padding for better alignment
  textAlign: "right", // Align the text to the left
  verticalAlign: "middle", // Vertically align the text to the middle
  textIndent: {
    lg: "80px",
    md: "80px",
    sm: "100px",
    xs: "0",
  },
};

const tablecellValueStyle = {
  justifyContent: "flex-start",
  alignItems: "center",
  width: { xs: "50%", sm: "50%", md: "60%" }, // Responsive width for small and medium screens
  height: "10px",
};

const ViewDialog = ({
  open,
  onClose,
  data,
  paymentModeData,
  donationTypesData,
}) => {
  // ** Hook
  const theme = useTheme();

  const { listValues } = useContext(AuthContext);

  return (
    <>
      <Dialog open={open} onClose={onClose} maxWidth="lg">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
            fontSize: { xs: 19, md: 20 },
            height: "50px", // height
          }}
          textAlign={"center"}
        >
          View Donation Receipt
          <Box sx={{ position: "absolute", top: "9px", right: "10px" }}>
            <IconButton
              size="small"
              onClick={onClose}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <>
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#d6d6f5",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Donor Information
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Organisation Name:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.orgName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Donor name:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.tenantDonorsDTO?.name || data?.selfRegisteredDonorDTO?.donorName}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography style={fieldLabelStyle}>
                          Donor Mobile Number:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.tenantDonorsDTO?.mobileNumber || data?.selfRegisteredDonorDTO?.donorMobileNumber}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography style={fieldLabelStyle}>Email:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.tenantDonorsDTO?.email || data?.selfRegisteredDonorDTO?.donorEmail}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Pan Number:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.tenantDonorsDTO?.panNo  || data?.selfRegisteredDonorDTO?.donorPanNo}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>Address:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.tenantDonorsDTO?.address  || data?.selfRegisteredDonorDTO?.address}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#d6d6f5",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Donor Details
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Donation Type:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {
                            donationTypesData?.find(
                              (item) => item.value === data?.donationTypeId
                            )?.key
                          }
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography style={fieldLabelStyle}>
                          Donation Head:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.donationHead}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography style={fieldLabelStyle}>Amount:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.metaData?.amount}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Donation Date:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.receiptDate}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Payment Type:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {
                            listValues?.find(
                              (item) => item.id === data?.metaData?.paymentType
                            )?.name
                          }
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    {listValues?.find(
                      (item) => item.id === data?.metaData?.paymentType
                    )?.name === "Offline" && (
                      <TableRow>
                        <MUITableCell sx={tablecellLabelStyle}>
                          <Typography sx={fieldLabelStyle}>
                            Payment Mode:
                          </Typography>
                        </MUITableCell>
                        <MUITableCell sx={tablecellValueStyle}>
                          <Typography
                            className="data-field"
                            style={fieldValueStyle}
                          >
                            {
                              paymentModeData?.find(
                                (item) =>
                                  item.value === data?.metaData?.paymentMode
                              )?.key
                            }
                          </Typography>
                        </MUITableCell>
                      </TableRow>
                    )}

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>
                          Payment Details:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.metaData?.paymentDetails}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
            <Card>
              <Grid
                sx={{
                  backgroundColor: "#d6d6f5",
                  mt: 4,
                  paddingTop: 0,
                  height: "36px",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="body1"
                  fontWeight={"bold"}
                  sx={{ mt: 0, ml: 2 }}
                >
                  Additional Information
                </Typography>
                <Divider />
              </Grid>
              <Divider />
              <TableContainer sx={{ padding: "4px 6px" }} className="tableBody">
                <Table>
                  <TableBody
                    sx={{
                      "& .MuiTableCell-root": {
                        p: `${theme.spacing(1.35, 1.125)} !important`,
                      },
                    }}
                  >
                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography sx={fieldLabelStyle}>Reference:</Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.metaData?.reference}
                        </Typography>
                      </MUITableCell>
                    </TableRow>

                    <TableRow>
                      <MUITableCell sx={tablecellLabelStyle}>
                        <Typography style={fieldLabelStyle}>
                          Additional Notes:
                        </Typography>
                      </MUITableCell>
                      <MUITableCell sx={tablecellValueStyle}>
                        <Typography
                          className="data-field"
                          style={fieldValueStyle}
                        >
                          {data?.metaData?.additionalNotes}
                        </Typography>
                      </MUITableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Card>
          </>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px", // height
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={onClose}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
export default ViewDialog;
