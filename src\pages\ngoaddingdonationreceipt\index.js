import React, { useEffect, useState } from 'react'
import BlankLayout from 'src/@core/layouts/BlankLayout'
import { useForm, Controller } from 'react-hook-form'
import {
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  FormControl,
  InputAdornment,
  MenuItem,
  Select,
  Snackbar,
  Stack,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  InputLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton
} from '@mui/material'
import { ArrowBack, VolunteerActivism } from "@mui/icons-material";


const steps = [
  "Select/Add Donor",
  "Choose Payment Method",
  "Enter Payment Details",
  "Review & Submit",
  "Receipt"
]

const mockDonors = [
  { value: "1", label: "John Doe", email: "<EMAIL>" },
  { value: "2", label: "<PERSON>", email: "<EMAIL>" },
]

const paymentModes = [
  { value: "CASH", label: "Cash" },
  { value: "CHEQUE", label: "Cheque" },
  { value: "BANK_TRANSFER", label: "Bank Transfer" },
  { value: "ONLINE", label: "Online (Razorpay)" }
]

const buttonStyle = {
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: 'none',
  height: 38,
  minWidth: 80,
}

const pageBackground = "linear-gradient(135deg, #e0e7ff 0%, #fff 100%)"
const RazorpayKey = "rzp_test_zKgAOeC4Kqzl31"

const loadRazorpayScript = () =>
  new Promise((resolve) => {
    if (window.Razorpay) {
      resolve(true)
      return
    }
    const script = document.createElement('script')
    script.src = 'https://checkout.razorpay.com/v1/checkout.js'
    script.onload = () => resolve(true)
    script.onerror = () => resolve(false)
    document.body.appendChild(script)
  })

const NGOAddingDonationReceipt = () => {
  const [activeStep, setActiveStep] = useState(0)
  const [donors, setDonors] = useState(mockDonors)
  const [loading, setLoading] = useState(false)
  const [receipt, setReceipt] = useState(null)
  const [openSnackbar, setOpenSnackbar] = useState(false)
  const [openDialog, setOpenDialog] = useState(false)
  const [isCustomDonor, setIsCustomDonor] = useState(false);

  const { control, handleSubmit, watch, setValue, reset, formState: { errors }, trigger } = useForm({
    defaultValues: {
      donor: "",
      donorEmail: "",
      paymentMode: "",
      amount: "",
      remarks: ""
    }
  })

  useEffect(() => {
    const selectedDonor = donors.find(d => d.value === watch("donor"))
    if (selectedDonor) {
      setValue("donorEmail", selectedDonor.email)
    } else {
      setValue("donorEmail", "")
    }
  }, [watch("donor"), setValue, donors])

  // Razorpay Payment
  const openRazorpay = async (data) => {
    const res = await loadRazorpayScript()
    if (!res) {
      alert('Razorpay SDK failed to load. Are you online?')
      setLoading(false)
      return
    }
    const amountInPaise = Number(data.amount) * 100
    const options = {
      key: RazorpayKey,
      amount: amountInPaise,
      currency: 'INR',
      name: 'Pure Heart Donation',
      description: `Donation from ${donors.find(d => d.value === data.donor)?.label || 'Donor'}`,
      prefill: {
        name: donors.find(d => d.value === data.donor)?.label,
        email: data.donorEmail
      },
      handler: function (response) {
        const generatedReceipt = {
          ...data,
          donorName: donors.find(d => d.value === data.donor)?.label,
          paymentModeLabel: paymentModes.find(m => m.value === data.paymentMode)?.label,
          receiptId: Math.floor(100000 + Math.random() * 900000),
          date: new Date().toLocaleString(),
          paymentId: response.razorpay_payment_id
        }
        setReceipt(generatedReceipt)
        setActiveStep(4)
        setLoading(false)
        setOpenSnackbar(true)
      },
      modal: {
        ondismiss: function () {
          setLoading(false)
        }
      }
    }
    const paymentObject = new window.Razorpay(options)
    paymentObject.open()
  }

  // Submission for both offline and online
  const onSubmit = async (data) => {
    setLoading(true)
    if (data.paymentMode === "ONLINE") {
      openRazorpay(data)
    } else {
      setTimeout(() => {
        const generatedReceipt = {
          ...data,
          donorName: donors.find(d => d.value === data.donor)?.label,
          paymentModeLabel: paymentModes.find(m => m.value === data.paymentMode)?.label,
          receiptId: Math.floor(100000 + Math.random() * 900000),
          date: new Date().toLocaleString(),
        }
        setReceipt(generatedReceipt)
        setActiveStep(4)
        setLoading(false)
        setOpenSnackbar(true)
      }, 1200)
    }
  }

  // Step navigation
  const handleNext = async () => {
    let valid = false
    if (activeStep === 0) {
      valid = await trigger(['donor', 'donorEmail'])
    } else if (activeStep === 1) {
      valid = await trigger('paymentMode')
    } else if (activeStep === 2) {
      valid = await trigger('amount')
    } else {
      valid = true
    }
    if (valid) setActiveStep((prev) => prev + 1)
  }

  const handleBack = () => {
  if (activeStep > 0) {
    setActiveStep((prev) => prev - 1); // Navigate to the previous step
  }
};

const handleLogout = () => {
  setActiveStep(0); // Reset to the first step
  setReceipt(null); // Clear the receipt
  reset(); // Reset the form
};
  const handleSnackbarClose = () => setOpenSnackbar(false)
  const restartHandler = () => {
    reset()
    setActiveStep(0)
    setReceipt(null)
  }

  return (
    <Box
              sx={{
                minHeight: '100vh',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: 'linear-gradient(135deg, #e0e7ff 0%, #fff 100%)'
              }}>
              <Card sx={{
                minWidth: 340,
                maxWidth: 420,
                width: '100%',
                p: { xs: 1, sm: 3 },
                boxShadow: 6,
                borderRadius: 4,
                bgcolor: '#fff',
                position: 'relative'
              }}>
                <CardContent>
                  <Stack spacing={2} alignItems="center" sx={{ mb: 2 }}>
                    <VolunteerActivism sx={{ fontSize: 40, color: 'primary.main' }} />
                    <Typography variant="h4" fontWeight="bold" color="primary" align="center" sx={{ letterSpacing: 1 }}>
                      Donate
                    </Typography>
                    <Typography variant="subtitle2" color="text.secondary" align="center">
                      Your generosity can change lives!
                    </Typography>
                  </Stack>

          {loading && (
            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 120 }}>
              <CircularProgress />
            </Box>
          )}

          {/* Step 0: Select/Add Donor */}
          {!loading && activeStep === 0 && (
            <form onSubmit={e => { e.preventDefault(); handleNext() }}>
              <Stack spacing={2}>

<FormControl fullWidth error={!!errors.donor} size="small">
  <InputLabel>Select Donor</InputLabel>
  <Controller
    name="donor"
    control={control}
    rules={{ required: "Please select or add a donor" }}
    render={({ field }) => (
      <Select
        {...field}
        label="Select Donor"
        displayEmpty
        size="small"
        onChange={(e) => {
          field.onChange(e); // Update the form value
          setIsCustomDonor(e.target.value === "custom"); // Check if "Add Custom Donor" is selected
        }}
      >
        <MenuItem value="" disabled>Select Donor</MenuItem>
        {donors.map((d) => (
          <MenuItem key={d.value} value={d.value}>{d.label}</MenuItem>
        ))}
        <MenuItem value="custom">Add Custom Donor</MenuItem>
      </Select>
    )}
  />
  {errors.donor && <Typography color="error" variant="caption">{errors.donor.message}</Typography>}
</FormControl>

{isCustomDonor && (
  <>
    <Controller
      name="customDonorName"
      control={control}
      rules={{ required: "Please enter the donor name" }}
      render={({ field }) => (
        <TextField
          {...field}
          label="Custom Donor Name"
          fullWidth
          size="small"
          error={!!errors.customDonorName}
          helperText={errors.customDonorName?.message}
        />
      )}
    />
    <Controller
      name="customDonorEmail"
      control={control}
      rules={{
        required: "Please enter the donor email",
        pattern: {
          value: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
          message: "Invalid email address",
        },
      }}
      render={({ field }) => (
        <TextField
          {...field}
          label="Custom Donor Email"
          fullWidth
          size="small"
          error={!!errors.customDonorEmail}
          helperText={errors.customDonorEmail?.message}
        />
      )}
    />
  </>
)}
            
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Button variant="outlined" sx={buttonStyle} onClick={() => window.location.href = '/'}>
                    Return to Home
                  </Button>
                  <Button variant="contained" sx={buttonStyle} type="submit">Next</Button>
                </Box>
              </Stack>
            </form>
          )}

          {/* Step 1: Choose Payment Method */}
          {!loading && activeStep === 1 && (
            <form onSubmit={e => { e.preventDefault(); handleNext() }}>
              <Stack spacing={2}>
                <FormControl fullWidth error={!!errors.paymentMode} size="small">
                  <InputLabel>Select Payment Method</InputLabel>
                  <Controller
                    name="paymentMode"
                    control={control}
                    rules={{ required: "Please select a payment method" }}
                    render={({ field }) => (
                      <Select {...field} label="Select Payment Method" displayEmpty size="small">
                        <MenuItem value="" disabled>Select Payment Method</MenuItem>
                        {paymentModes.map(m => (
                          <MenuItem key={m.value} value={m.value}>{m.label}</MenuItem>
                        ))}
                      </Select>
                    )}
                  />
                  {errors.paymentMode && <Typography color="error" variant="caption">{errors.paymentMode.message}</Typography>}
                </FormControl>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Button variant="outlined" sx={buttonStyle} onClick={handleBack}>Back</Button>
                  <Button variant="contained" sx={buttonStyle} type="submit">Next</Button>
                </Box>
              </Stack>
            </form>
          )}

          {/* Step 2: Enter Payment Details */}
          {!loading && activeStep === 2 && (
            <form onSubmit={e => { e.preventDefault(); handleNext() }}>
              <Stack spacing={2}>
                <Controller
                  name="amount"
                  control={control}
                  rules={{ required: "Amount is required", min: { value: 1, message: "Amount must be positive" } }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Amount"
                      type="number"
                      fullWidth
                      size="small"
                      InputProps={{ startAdornment: <InputAdornment position="start">₹</InputAdornment> }}
                      error={!!errors.amount}
                      helperText={errors.amount?.message}
                    />
                  )}
                />
                <Controller
                  name="remarks"
                  control={control}
                  render={({ field }) => (
                    <TextField {...field} label="Remarks (optional)" fullWidth size="small" />
                  )}
                />
                <Box sx={{ display: "flex", gap: 32, mt: 2 }}>
  <Button
    variant="outlined"
    onClick={handleBack}
    sx={{
      flex: 1, // Keep the "Back" button smaller
      height: 36, // Adjust height to make it smaller
      fontSize: "0.875rem", // Adjust font size
    }}
  >
    Back
  </Button>
  <Button
    variant="contained"
    type="submit"
    sx={{
      ...buttonStyle,
      flex: 5, // Increase the length of the "Complete Payment" button
      height: 36, // Adjust height to make it smaller
      fontSize: "0.875rem", // Adjust font size
    }}
  >
    Complete Payment
  </Button>
</Box>
              </Stack>
            </form>
          )}

         

          {/* Step 3: Review & Submit */}
          {!loading && activeStep === 3 && (
            <Box>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold', color: 'primary.main', mb: 2 }}>Review & Submit</Typography>
              <Stack spacing={1} sx={{ mb: 2 }}>
                <Typography><b>Donor:</b> {donors.find(d => d.value === watch("donor"))?.label}</Typography>
                <Typography><b>Email:</b> {watch("donorEmail")}</Typography>
                <Typography><b>Payment Mode:</b> {paymentModes.find(m => m.value === watch("paymentMode"))?.label}</Typography>
                <Typography><b>Amount:</b> ₹{watch("amount")}</Typography>
                <Typography><b>Remarks:</b> {watch("remarks") || '-'}</Typography>
              </Stack>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
                <Button variant="outlined" sx={buttonStyle} onClick={handleBack}>Back</Button>
                <Button variant="contained" sx={buttonStyle} onClick={handleSubmit(onSubmit)}>
                  {watch("paymentMode") === "ONLINE" ? "Pay & Generate Receipt" : "Generate & Send Receipt"}
                </Button>
              </Box>
            </Box>
          )}

          {/* Step 4: Receipt */}
          {!loading && activeStep === 4 && receipt && (
            <Stack spacing={2} textAlign="center">
              <Typography variant="h5" color="success.main" sx={{ mb: 2 }}>
                Receipt Generated{receipt.paymentId ? " & Payment Successful!" : " & Sent!"}
              </Typography>
              <Typography variant="subtitle1" sx={{ color: "text.secondary" }}>
                Receipt ID: {receipt.receiptId}
              </Typography>
              <Stack spacing={0.5} sx={{ mb: 2 }}>
                <Typography>Date: <b>{receipt.date}</b></Typography>
                <Typography>Donor: <b>{receipt.donorName}</b></Typography>
                <Typography>Email: <b>{receipt.donorEmail}</b></Typography>
                <Typography>Payment Mode: <b>{receipt.paymentModeLabel}</b></Typography>
                <Typography>Amount: <b>₹{receipt.amount}</b></Typography>
                <Typography>Remarks: <b>{receipt.remarks || '-'}</b></Typography>
                {receipt.paymentId && (
                  <Typography>Payment ID: <b>{receipt.paymentId}</b></Typography>
                )}
              </Stack>
              <Button variant="outlined" sx={buttonStyle} onClick={restartHandler}>Add Another Receipt</Button>
            </Stack>
          )}
        </CardContent>
       <Snackbar open={openSnackbar} autoHideDuration={4000} onClose={handleSnackbarClose}>
  <Alert 
    onClose={handleSnackbarClose} 
    severity="success" 
    sx={{ width: '100%', color: '#fff', backgroundColor: 'green' }} // White text and green background
  >
    Receipt generated and sent successfully!
  </Alert>
</Snackbar>
      </Card>
    </Box>
  )
}

NGOAddingDonationReceipt.getLayout = page => <BlankLayout>{page}</BlankLayout>
NGOAddingDonationReceipt.guestGuard = true

export default NGOAddingDonationReceipt
