import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { Tab<PERSON>ontext, <PERSON>bList, TabPanel } from "@mui/lab";
import { Box, Checkbox, Divider, Grid, Tab, Typography } from "@mui/material";
import { useEffect, useState } from "react";

const getPermissionState = (permissionValue) => ({
  read: (permissionValue & 1) !== 0,
  create: (permissionValue & 2) !== 0,
  update: (permissionValue & 4) !== 0,
  delete: (permissionValue & 8) !== 0,
});

const calculatePermissionsValue = (permissions) =>
  (permissions.read ? 1 : 0) |
  (permissions.create ? 2 : 0) |
  (permissions.update ? 4 : 0) |
  (permissions.delete ? 8 : 0);

const PermissionItem = ({ item, level = 0, updatePermissions }) => {
  const [expanded, setExpanded] = useState(false);
  const [permissions, setPermissions] = useState(
    getPermissionState(item.permissions)
  );

  useEffect(() => {
    setPermissions(getPermissionState(item.permissions));
  }, [item.permissions]);

  const isSelectAllChecked =
    permissions.read &&
    permissions.create &&
    permissions.update &&
    permissions.delete;

  const handlePermissionChange = (permission) => {
    const newValue = !permissions[permission];
    const updatedPermissions = {
      ...permissions,
      [permission]: newValue,
    };
    updatePermissions(item.name, updatedPermissions);
  };

  const handleSelectAllChange = () => {
    const newValue = !isSelectAllChecked;
    const updatedPermissions = {
      read: newValue,
      create: newValue,
      update: newValue,
      delete: newValue,
    };
    updatePermissions(item.name, updatedPermissions);
  };

  const renderPermissions = () => (
    <div style={{ display: "flex", gap: "3rem", alignItems: "center" }}>
      {["read", "create", "update", "delete"]?.map((perm) => (
        <div key={perm} style={{ width: "80px", textAlign: "center" }}>
          <Checkbox
            checked={permissions[perm]}
            onChange={() => handlePermissionChange(perm)}
          />
        </div>
      ))}
      <div style={{ width: "80px", textAlign: "center" }}>
        <Checkbox
          checked={isSelectAllChecked}
          onChange={handleSelectAllChange}
        />
      </div>
    </div>
  );

  const formatName = (name) => name.replace(/_/g, " ");
  const fontSize = level <= 1 ? "1rem" : `${1 - (level - 1) * 0.06}rem`;
  const fontWeight = level === 0 ? "bold" : "normal";

  return (
    <div style={{ marginLeft: level * 10, marginTop: 2 }}>
      <div
        style={{
          display: "flex",
          alignItems: "center",
          padding: "2px 10px",
          borderRadius: "6px",
          transition: "background-color 0.2s",
        }}
        onMouseEnter={(e) =>
          (e.currentTarget.style.backgroundColor = "#f2f2f2")
        }
        onMouseLeave={(e) =>
          (e.currentTarget.style.backgroundColor = "transparent")
        }
      >
        <span
          style={{ cursor: "pointer", fontSize, fontWeight }}
          onClick={() => item.children?.length && setExpanded(!expanded)}
        >
          {item.children?.length > 0 &&
            (expanded ? (
              <ExpandLessIcon style={{ fontSize: "16px" }} />
            ) : (
              <ExpandMoreIcon style={{ fontSize: "16px" }} />
            ))}{" "}
          {formatName(item.name)}
        </span>
        <div style={{ marginLeft: "auto" }}>{renderPermissions()}</div>
      </div>
      {expanded &&
        item.children &&
        item.children
          .slice()
          .sort((a, b) => a.displayNumber - b.displayNumber)
          ?.map((child) => (
            <PermissionItem
              key={child.name}
              item={child}
              level={level + 1}
              updatePermissions={updatePermissions}
            />
          ))}
    </div>
  );
};

const PermissionTree = ({ data, updatePermissions }) => {
  const [selectedMainTab, setSelectedMainTab] = useState("0");
  const [selectedSubTab, setSelectedSubTab] = useState({});

  const handleMainTabChange = (event, newValue) => {
    setSelectedMainTab(newValue);
    setSelectedSubTab((prev) => ({ ...prev, [newValue]: "0" }));
  };

  const handleSubTabChange = (mainIndex) => (event, newValue) => {
    setSelectedSubTab((prev) => ({ ...prev, [mainIndex]: newValue }));
  };

  const formatName = (name) => name.replace(/_/g, " ");

  const renderStickyHeader = () => (
    <div
      style={{
        position: "sticky",
        top: 0,
        backgroundColor: "white",
        zIndex: 1,
        padding: "8px 16px",
        display: "flex",
        gap: "3rem",
        alignItems: "center",
        borderBottom: "1px solid #ddd",
      }}
    >
      <div style={{ flex: 1, fontWeight: "bold" }}></div>
      {["Read", "Create", "Update", "Delete", "Select All"]?.map((label) => (
        <div key={label} style={{ width: "80px", textAlign: "center" }}>
          {label}
        </div>
      ))}
    </div>
  );

  return (
    <div>
      {data?.length > 0 && (
        <>
          <Grid
            sx={{
              backgroundColor: "#f2f7f2",
              mt: 4,
              height: "36px",
              display: "flex",
              alignItems: "center",
            }}
          >
            <Typography variant="body1" fontWeight={"bold"} sx={{ ml: 2 }}>
              Site Map Data
            </Typography>
          </Grid>
          <TabContext value={selectedMainTab}>
            <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
              <TabList
                variant="scrollable"
                scrollButtons="auto"
                onChange={handleMainTabChange}
              >
                {data?.map((item, index) => (
                  <Tab
                    sx={{ textTransform: "none" }}
                    label={formatName(item.name)}
                    value={index.toString()}
                    key={index}
                  />
                ))}
              </TabList>
            </Box>
            {data?.map((item, mainIndex) => (
              <TabPanel value={mainIndex.toString()} key={mainIndex}>
                <TabContext value={selectedSubTab[mainIndex] || "0"}>
                  <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                    <TabList
                      variant="scrollable"
                      scrollButtons="auto"
                      onChange={handleSubTabChange(mainIndex)}
                    >
                      {item.children?.map((child, index) => (
                        <Tab
                          sx={{ textTransform: "none" }}
                          label={formatName(child.name)}
                          value={index.toString()}
                          key={index}
                        />
                      ))}
                    </TabList>
                  </Box>
                  <Box
                    sx={{ height: `calc(100vh - 280px)`, overflowY: "auto" }}
                  >
                    {renderStickyHeader()}
                    {item.children?.map((child, index) => (
                      <TabPanel value={index.toString()} key={index}>
                        <PermissionItem
                          item={child}
                          updatePermissions={updatePermissions}
                        />
                      </TabPanel>
                    ))}
                  </Box>
                </TabContext>
              </TabPanel>
            ))}
          </TabContext>
        </>
      )}
    </div>
  );
};

export default function DialogData({
  initialSiteMapData,
  setInitialSiteMapData,
}) {
  const updatePermissions = (targetName, updatedPermissionObject) => {
    const updateTree = (items) => {
      return items?.map((item) => {
        let newItem = { ...item };

        if (item.name === targetName) {
          const newPermissionValue = calculatePermissionsValue(
            updatedPermissionObject
          );
          newItem.permissions = newPermissionValue;

          if (item.children && item.children.length > 0) {
            newItem.children = cascadeToChildren(
              item.children,
              newPermissionValue
            );
          }
        } else if (item.children && item.children.length > 0) {
          newItem.children = updateTree(item.children);
        }

        return newItem;
      });
    };

    const cascadeToChildren = (children, permissionValue) => {
      return children?.map((child) => {
        let newChild = { ...child, permissions: permissionValue };
        if (child.children && child.children.length > 0) {
          newChild.children = cascadeToChildren(
            child.children,
            permissionValue
          );
        }
        return newChild;
      });
    };

    const updatedTree = updateTree(initialSiteMapData);
    setInitialSiteMapData(updatedTree);
  };

  return (
    
      <PermissionTree
        data={initialSiteMapData}
        updatePermissions={updatePermissions}
      />
    
  );
}
