import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Grid,
  IconButton,
  InputAdornment,
  Snackbar,
  TextField,
  Typography,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import BlankLayout from "src/@core/layouts/BlankLayout";
import { useEffect, useState } from "react";
import Icon from "src/@core/components/icon";
import { useRouter } from "next/router";
import axios from "axios";
import { getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useAuth } from "src/hooks/useAuth";
import { fetchIpAddress } from "src/@core/components/custom-components/FetchIpAddress";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
const ResetPasswordPage = () => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const router = useRouter();
  const { roleId, process, resetCode, emailId } = router.query;
  const auth = useAuth();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmedPassword, setShowConfirmedPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showToast, setShowToast] = useState(false); // State for toast visibility
  const [toastMessage, setToastMessage] = useState("");
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const [resetSuccess, setResetSuccess] = useState(false);
  const [isFailed, setIsFailed] = useState(false);
  const [activation, setActivation] = useState(false);

  const getPasswordStrength = () => {
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isValidLength = password.length >= 8;

    const conditionsMet = [
      hasUpperCase,
      hasLowerCase,
      hasNumber,
      hasSpecialChar,
    ].filter(Boolean).length;

    if (!isValidLength || conditionsMet < 2) return "Poor";
    if (conditionsMet === 3) return "Moderate";
    if (conditionsMet === 4 && password.length >= 8) return "Strong";
    return "Moderate";
  };

  const passwordsMatch = password === confirmPassword && password.length > 0;

  const handleFailure = (error) => {
    setToastMessage(error.response.data.message);
    setShowToast(true);
  };

  const handleLoginFailure = () => {
    setIsFailed(true);
    const message = ` 
    <div>
      <h3>Email or password credentials are invalid.</h3>
    </div>
    `;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handleLoginSuccess = () => {
    setIsFailed(false);
    const message = `
    <div>
      <h5>Redirecting to your dashboard.</h5>
    </div>
`;
    setDialogMessage(message);
    setOpenDialog(true);
  };

  const handlePopup = () => {};

  useEffect(() => {
    if (process === "account_activation") {
      const data = {
        email: emailId,
        roleId: roleId,
        processEnum: process,
      };
      axios({
        method: "post",
        url: getUrl(authConfig.activationEndpoint),
        data: data,
      })
        .then((res) => {
          setActivation(true); // Hide loading spinner
        })
        .catch((err) =>{
          if(err.response.data.message === "Account is already activated. Please log in."){
            setActivation(true); 
          }
          console.log("Sign Up error", err)
        });
    }
  }, [process]);

  async function login(data) {
    const ipAddress = await fetchIpAddress();
    const fields = {
      email: emailId,
      password: confirmPassword,
      ipAddress: ipAddress,
      overrideExistingLogins: true,
    };
    try {
      await auth.loginNew(
        fields,
        handleLoginFailure,
        handleLoginSuccess,
        handlePopup
      );
    } catch (error) {
      // Handle any errors from validation, IP fetch, or login process
      console.error("An error occurred:", error);
      handleLoginFailure();
    }
  }
  async function handleReset() {
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isValidLength = password.length >= 8;

    if (
      !hasUpperCase ||
      !hasLowerCase ||
      !hasNumber ||
      !hasSpecialChar ||
      !isValidLength
    ) {
      setToastMessage(
        "Password must be greater than 7 characters with uppercase, lowercase, number, and special character."
      );
      setShowToast(true);
      return; // Exit early if password is weak
    }

    const data = {
      emailId: emailId,
      resetCode: resetCode,
      password: confirmPassword,
    };

    try {
      const response = await auth.postResetPassword(data, handleFailure);
      if (response === true) {
        setResetSuccess(true);
        setToastMessage("Password Reset Successful. Please login");
        setShowToast(true);
        login();
      }
    } catch (error) {
      console.error("forgot password failed:", error);
      handleFailure();
    }
  }

  async function handleCreate() {
    const data = 	{
      emailId: emailId,
      password:confirmPassword
    }
   
    axios({
      method: "post",
      url:
        getUrl(authConfig.createPasswordEndpoint),
      data: data,
    })
      .then((res) => {
        setResetSuccess(true);
        setToastMessage("Password Created Successfully.");
        setShowToast(true);
        login();
      })
      .catch((err) => console.log("Sign Up error", err));
  }

  const handleToastClose = () => {
    setShowToast(false);
    setToastMessage("");
  };

  const handleLogin = () => {
    router.push("/login");
  };

  const handleClose = () => {
    setOpenDialog(false);
  };

  return (
    <>
      <Container
        maxWidth="xs"
        sx={{
          height: "90vh",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "start",
          boxShadow: 3,
          p: 8,
          mt: 8,
          mb: 8,
          borderRadius: 6,
          bgcolor: "white",
          position: "relative",
          overflowY: "auto",
          flexGrow: 1,
          transform: {
            xs: "scale(0.9)", // 90% of original size on extra-small screens (mobile)
            sm: "none", // Normal size for tablets and above
          },
        }}
      >
        <Box>
          <Grid
            container
            spacing={3}
            sx={{ alignItems: "center", justifyContent: "center" }}
          >
            {activation && (
              <Grid item xs={12}>
                 <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography
                  variant="h6"
                  fontWeight={200}
                  color="primary"
                  gutterBottom
                >
                  Your account has been activated successfully.
                </Typography>
                <CheckCircleIcon
                  sx={{
                    color: "green",
                    marginBottom: "-7px",
                    paddingBottom: 2,
                    marginLeft: "5px",
                  }} 
                />
                </Box>
              </Grid>
            )}
          </Grid>
        </Box>

        <Box sx={{ mt: 4 }}>
          <Grid
            container
            spacing={3}
            sx={{ alignItems: "center", justifyContent: "center" }}
          >
            <Grid item xs={9.2}>
              <Typography
                variant="h5"
                fontWeight={500}
                color="primary"
                gutterBottom
              >
                {process === "account_activation"
                  ? "Create Your Password"
                  : "Reset Your Password"}
              </Typography>
            </Grid>

            <Grid item xs={9.2} sx={{ marginBottom: 2 }}>
              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="New password*"
                    placeholder="Enter new password"
                    type={showPassword ? "text" : "password"}
                    fullWidth
                    size="small"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    error={Boolean(errors.password)}
                    helperText={errors.password?.message}
                    InputLabelProps={{ shrink: true, sx: { fontSize: "1rem" } }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            onMouseDown={(e) => e.preventDefault()}
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            <Icon
                              icon={
                                showPassword ? "tabler:eye" : "tabler:eye-off"
                              }
                              fontSize={{ xs: 5, lg: 20 }}
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
              {password?.length > 0 && (
                <Typography
                  color={
                    getPasswordStrength() === "Poor"
                      ? "error"
                      : getPasswordStrength() === "Moderate"
                      ? "orange"
                      : "green"
                  }
                >
                  {getPasswordStrength()}
                </Typography>
              )}
            </Grid>

            <Grid item xs={9.2} sx={{ marginBottom: 2 }}>
              <Controller
                name="confirmedPassword"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    label="Confirm New Password*"
                    placeholder="Confirm Password"
                    type={showConfirmedPassword ? "text" : "password"}
                    fullWidth
                    size="small"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    error={Boolean(errors.confirmedPassword)}
                    helperText={
                      <Typography color={passwordsMatch ? "green" : "red"}>
                        {confirmPassword.length > 0 && !passwordsMatch
                          ? "Both passwords should be the same"
                          : passwordsMatch
                          ? "Both matches"
                          : ""}
                      </Typography>
                    }
                    InputLabelProps={{ shrink: true, sx: { fontSize: "1rem" } }}
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            edge="end"
                            onMouseDown={(e) => e.preventDefault()}
                            onClick={() =>
                              setShowConfirmedPassword(!showConfirmedPassword)
                            }
                          >
                            <Icon
                              icon={
                                showConfirmedPassword
                                  ? "tabler:eye"
                                  : "tabler:eye-off"
                              }
                              fontSize={{ xs: 5, lg: 20 }}
                            />
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                  />
                )}
              />
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ width: "80%", py: 2, textAlign: "center", mt: "auto" }}>
          {process === "account_activation" ? (
            ""
          ) : (
            <Typography variant="body2" sx={{ mt: 2, mb: 2 }}>
              Resetting your password will log you out on all devices{" "}
            </Typography>
          )}

          {process === "account_activation" ? (
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 4, width: "100%" }}
              onClick={handleCreate}
              disabled={!passwordsMatch}
            >
              Create Password
            </Button>
          ) : (
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 4, width: "100%" }}
              onClick={handleReset}
              disabled={!passwordsMatch}
            >
              Reset Password
            </Button>
          )}
        </Box>
      </Container>
      <Snackbar
        open={showToast}
        autoHideDuration={5000} // Toast will be visible for 5 seconds
        onClose={handleToastClose}
        anchorOrigin={{ vertical: "top", horizontal: "right" }} // Position of the toast
      >
        <Alert
          onClose={handleToastClose}
          severity={resetSuccess ? "success" : "error"}
          sx={{
            color: "black",
            padding: "4px 8px", // Reduce padding to make it smaller
            fontSize: "0.875rem", // Adjust font size for a more compact look
            borderRadius: "12px", // Optional: you can adjust the border radius
            border: "0.5px solid #ccc", // Optional: set a border or remove it completely
          }}
        >
          {toastMessage}
        </Alert>
      </Snackbar>
      <Dialog
        open={openDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          {isFailed ? (
            <>
              <DialogActions>
                <Button
                  onClick={handleClose}
                  style={{ margin: "10px auto", width: 100 }}
                >
                  Okay
                </Button>
              </DialogActions>
            </>
          ) : (
            <>
              <Button
                style={{
                  margin: "10px auto",
                  display: "block",
                  width: 100,
                }}
              >
                <CircularProgress color="inherit" size={24} />
              </Button>
            </>
          )}
        </Box>
      </Dialog>
    </>
  );
};

ResetPasswordPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
ResetPasswordPage.guestGuard = true;

export default ResetPasswordPage;
