# Docker Setup for Donation Receipt Frontend Application

This document explains how to use Docker with this Next.js application for both local development and CI/CD pipelines.

## Files Created

- `Dockerfile` - Production-optimized multi-stage build
- `Dockerfile.dev` - Development dockerfile with hot reloading
- `docker-compose.yml` - Local development and testing orchestration
- `.dockerignore` - Optimizes build context by excluding unnecessary files

## Production Build (CI/CD Pipeline)

### Building the Docker Image

```bash
# Build the production image (uses .env.production)
docker build --build-arg BUILD_ENV=production -t donation-receipt-app:latest .

# Build staging image (uses .env.development)
docker build --build-arg BUILD_ENV=development -t donation-receipt-app:staging .

# Build with a specific tag for production
docker build --build-arg BUILD_ENV=production -t donation-receipt-app:v1.0.0 .

# Build for specific platform (for CI/CD on different architectures)
docker build --platform linux/amd64 --build-arg BUILD_ENV=production -t donation-receipt-app:latest .
```

### Running the Container

```bash
# Run the container
docker run -p 3000:3000 donation-receipt-app:latest

# Run with environment variables
docker run -p 3000:3000 -e NODE_ENV=production donation-receipt-app:latest

# Run in detached mode
docker run -d -p 3000:3000 --name donation-app donation-receipt-app:latest
```

## Local Development

### Using Docker Compose

```bash
# Build and run production version (uses .env.production)
docker-compose up donation-receipt-app --build

# Build and run staging version (uses .env.development)  
docker-compose --profile staging up donation-receipt-staging --build

# Run development version with hot reloading (uses .env.local)
docker-compose --profile dev up donation-receipt-dev --build

# Run in background
docker-compose up -d

# Stop services
docker-compose down

# View logs for specific service
docker-compose logs -f donation-receipt-app
docker-compose logs -f donation-receipt-staging
docker-compose logs -f donation-receipt-dev
```

### Development Workflow

1. **Local development with hot reloading (.env.local):**
   ```bash
   docker-compose --profile dev up donation-receipt-dev --build
   ```
   This runs the app on `http://localhost:3002` with file watching and .env.local.

2. **Staging testing (.env.development):**
   ```bash
   docker-compose --profile staging up donation-receipt-staging --build
   ```
   This runs the staging build on `http://localhost:3001` using .env.development.

3. **Production testing (.env.production):**
   ```bash
   docker-compose up donation-receipt-app --build
   ```
   This runs the production build on `http://localhost:3000` using .env.production.

## CI/CD Pipeline Integration

### GitLab CI Example

```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - deploy

variables:
  DOCKER_IMAGE_NAME: $CI_REGISTRY_IMAGE/donation-receipt-app
  DOCKER_IMAGE_TAG: $CI_COMMIT_SHA

# Build staging image (development environment)
build_staging:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build --build-arg BUILD_ENV=development -t $DOCKER_IMAGE_NAME:staging-$DOCKER_IMAGE_TAG .
    - docker push $DOCKER_IMAGE_NAME:staging-$DOCKER_IMAGE_TAG
    - docker tag $DOCKER_IMAGE_NAME:staging-$DOCKER_IMAGE_TAG $DOCKER_IMAGE_NAME:staging-latest
    - docker push $DOCKER_IMAGE_NAME:staging-latest
  only:
    - develop
    - staging

# Build production image  
build_production:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build --build-arg BUILD_ENV=production -t $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG .
    - docker push $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG
    - docker tag $DOCKER_IMAGE_NAME:$DOCKER_IMAGE_TAG $DOCKER_IMAGE_NAME:latest
    - docker push $DOCKER_IMAGE_NAME:latest
  only:
    - main
    - master
```

### GitHub Actions Example

```yaml
# .github/workflows/docker-build.yml
name: Build and Push Docker Image

on:
  push:
    branches: [ main, develop, staging ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
      
    - name: Login to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_HUB_USERNAME }}
        password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

    # Build staging image for develop/staging branches
    - name: Build and push staging
      if: github.ref == 'refs/heads/develop' || github.ref == 'refs/heads/staging'
      uses: docker/build-push-action@v4
      with:
        context: .
        push: ${{ github.event_name != 'pull_request' }}
        build-args: |
          BUILD_ENV=development
        tags: |
          your-dockerhub-username/donation-receipt-app:staging-latest
          your-dockerhub-username/donation-receipt-app:staging-${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    # Build production image for main branch
    - name: Build and push production
      if: github.ref == 'refs/heads/main'
      uses: docker/build-push-action@v4
      with:
        context: .
        push: ${{ github.event_name != 'pull_request' }}
        build-args: |
          BUILD_ENV=production
        tags: |
          your-dockerhub-username/donation-receipt-app:latest
          your-dockerhub-username/donation-receipt-app:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
```

### Azure DevOps Example

```yaml
# azure-pipelines.yml
trigger:
- main
- develop
- staging

pool:
  vmImage: 'ubuntu-latest'

variables:
  dockerRegistryServiceConnection: 'docker-hub-connection'
  imageRepository: 'donation-receipt-app'
  containerRegistry: 'your-registry.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.BuildId)'

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  # Staging build for develop/staging branches
  - job: BuildStaging
    displayName: Build Staging
    condition: or(eq(variables['Build.SourceBranch'], 'refs/heads/develop'), eq(variables['Build.SourceBranch'], 'refs/heads/staging'))
    steps:
    - task: Docker@2
      displayName: Build and push staging image
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        arguments: '--build-arg BUILD_ENV=development'
        tags: |
          staging-$(tag)
          staging-latest

  # Production build for main branch
  - job: BuildProduction
    displayName: Build Production
    condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')
    steps:
    - task: Docker@2
      displayName: Build and push production image
      inputs:
        command: buildAndPush
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        arguments: '--build-arg BUILD_ENV=production'
        tags: |
          $(tag)
          latest
```

## Docker Image Optimization

The production Dockerfile uses several optimization techniques:

1. **Multi-stage build** - Separates build dependencies from runtime
2. **Alpine Linux** - Smaller base image size
3. **Layer caching** - Optimized layer ordering for better cache hits
4. **Standalone output** - Uses Next.js standalone mode for smaller image
5. **Non-root user** - Runs as unprivileged user for security
6. **Health checks** - Container health monitoring

## Environment Files Configuration

This setup uses different environment files for different environments:

### Environment File Structure
```
.env.local        # Local development (used by docker-compose dev profile)
.env.development  # Staging environment (used when BUILD_ENV=development)
.env.production   # Production environment (used when BUILD_ENV=production)
```

### Creating Environment Files

Create these files in your project root with your Spring backend configuration:

**.env.local** (for local development):
```env
# Local Development Environment Configuration
# Spring Backend API URL (local development)
NEXT_PUBLIC_API_URL=http://localhost:8080

# Environment identifier
NEXT_PUBLIC_ENVIRONMENT=local

# Feature flags for local development
NEXT_PUBLIC_ENABLE_DEBUG=true
NEXT_PUBLIC_ENABLE_MOCK_DATA=true

# API timeout settings
NEXT_PUBLIC_API_TIMEOUT=10000

# Logging level for local development
NEXT_PUBLIC_LOG_LEVEL=debug

# Authentication settings (if applicable)
# NEXT_PUBLIC_AUTH_DOMAIN=localhost
# NEXT_PUBLIC_AUTH_CLIENT_ID=your-local-client-id
```

**.env.development** (for staging):
```env
# Staging/Development Environment Configuration  
# Spring Backend API URL (staging environment)
NEXT_PUBLIC_API_URL=https://staging-api.yourdomain.com

# Environment identifier
NEXT_PUBLIC_ENVIRONMENT=staging

# Feature flags for staging
NEXT_PUBLIC_ENABLE_DEBUG=false
NEXT_PUBLIC_ENABLE_MOCK_DATA=false

# API timeout settings
NEXT_PUBLIC_API_TIMEOUT=15000

# Logging level for staging
NEXT_PUBLIC_LOG_LEVEL=info

# Analytics/tracking (staging IDs)
# NEXT_PUBLIC_GOOGLE_ANALYTICS=GA-STAGING-ID

# Authentication settings (if applicable)
# NEXT_PUBLIC_AUTH_DOMAIN=staging.yourdomain.com
# NEXT_PUBLIC_AUTH_CLIENT_ID=your-staging-client-id
```

**.env.production** (for production):
```env
# Production Environment Configuration
# Spring Backend API URL (production environment)
NEXT_PUBLIC_API_URL=https://api.yourdomain.com

# Environment identifier
NEXT_PUBLIC_ENVIRONMENT=production

# Feature flags for production
NEXT_PUBLIC_ENABLE_DEBUG=false
NEXT_PUBLIC_ENABLE_MOCK_DATA=false

# API timeout settings
NEXT_PUBLIC_API_TIMEOUT=30000

# Logging level for production
NEXT_PUBLIC_LOG_LEVEL=error

# Analytics/tracking (production IDs)
# NEXT_PUBLIC_GOOGLE_ANALYTICS=GA-PRODUCTION-ID

# Security settings
NEXT_PUBLIC_SECURE_COOKIES=true

# Authentication settings (if applicable)
# NEXT_PUBLIC_AUTH_DOMAIN=yourdomain.com
# NEXT_PUBLIC_AUTH_CLIENT_ID=your-production-client-id
```

### How It Works

1. **Local Development**: Uses `.env.local` through docker-compose volume mount
2. **Staging Builds**: `BUILD_ENV=development` copies `.env.development` to `.env.production` during build
3. **Production Builds**: `BUILD_ENV=production` copies `.env.production` to `.env.production` during build

### Build Arguments

The Dockerfile accepts a `BUILD_ENV` argument to determine which environment file to use:

```bash
# For staging
docker build --build-arg BUILD_ENV=development -t app:staging .

# For production  
docker build --build-arg BUILD_ENV=production -t app:production .
```

### Runtime Environment Variables

You can still override variables at runtime if needed:

```bash
# Using docker run
docker run -p 3000:3000 -e NEXT_PUBLIC_API_URL=https://custom-api.com donation-receipt-app

# Using docker-compose
# Add to docker-compose.yml environment section
```

## Troubleshooting

### Common Issues

1. **Build failures due to missing dependencies:**
   - Ensure all dependencies are listed in package.json
   - Check if any dependencies require additional system packages

2. **Large image size:**
   - Review .dockerignore to exclude unnecessary files
   - Consider using npm ci --only=production in dependencies stage

3. **Permission issues:**
   - The container runs as user 'nextjs' (uid 1001)
   - Ensure mounted volumes have appropriate permissions

4. **Memory issues during build:**
   - Increase Docker memory limit
   - Consider using --max-old-space-size for Node.js

### Debugging

```bash
# Build with verbose output
docker build --progress=plain --no-cache -t donation-receipt-app .

# Inspect the built image
docker run -it --entrypoint sh donation-receipt-app

# Check logs
docker logs <container-id>

# Monitor container resources
docker stats <container-id>
```

## Best Practices

1. **Use specific tags** instead of 'latest' in production
2. **Scan images** for vulnerabilities regularly
3. **Keep images updated** with security patches
4. **Monitor resource usage** in production
5. **Use health checks** for container orchestration
6. **Implement proper logging** strategy
7. **Set resource limits** in production deployments

## Next Steps

1. **Create environment files**: Copy the examples above and create `.env.local`, `.env.development`, and `.env.production` files with your Spring backend URLs and configuration
2. **Set up your container registry** (Docker Hub, AWS ECR, etc.)
3. **Configure your CI/CD pipeline** using the examples above
4. **Test locally**: Run `docker-compose --profile dev up` to test with `.env.local`
5. **Test staging build**: Run `docker build --build-arg BUILD_ENV=development -t app:staging .`
6. **Set up monitoring and logging** for your containers
7. **Implement proper secret management** for sensitive environment variables
8. **Configure container orchestration** (Kubernetes, Docker Swarm) if needed

## Quick Start Commands

```bash
# 1. Create your environment files first
cp DOCKER_SETUP.md .env.local   # Copy examples from documentation
cp DOCKER_SETUP.md .env.development
cp DOCKER_SETUP.md .env.production

# 2. Test local development
docker-compose --profile dev up --build

# 3. Test staging build
docker build --build-arg BUILD_ENV=development -t donation-receipt-app:staging .

# 4. Test production build  
docker build --build-arg BUILD_ENV=production -t donation-receipt-app:latest .
``` 