import SearchIcon from "@mui/icons-material/Search";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  Link,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { useContext, useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import CustomAvatar from "src/@core/components/mui/avatar";
import { AuthContext } from "src/context/AuthContext";
import { useAuth } from "src/hooks/useAuth";

import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import "react-datepicker/dist/react-datepicker.css";
import toast, { Toaster } from "react-hot-toast";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import MultiSelectAutoComplete from "src/@core/components/custom-components/MultiSelectAutoComplete";

const field = {
  fontWeight: 500,
  fontSize: "12.75px",
};

const ContactGroupDetails = ({
  open,
  onClose,
  fetchContactGroups,
  tenantsList,
  formData,
}) => {
  const auth = useAuth();
  const {
    handleSubmit,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useForm();
  const { user, getAllListValuesByListNameId } = useContext(AuthContext);
  const currentToast = useRef(null); // To keep track of the current toast

  const [dialogMessage, setDialogMessage] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [selectedRows, setSelectedRows] = useState([]);
  const [tenantId, setTenantId] = useState("");

  const [selectedUsers, setSelectedUsers] = useState([]);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];

  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [rowCount, setRowCount] = useState(0);
  const [keyword, setKeyword] = useState("");
  const [finalList, setFinalList] = useState(false);
  const [selectedTags, setSelectedTags] = useState("");
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [tagsList, setTagsList] = useState([]);
  const [usersData, setUserData] = useState([]);

  const handleError = (error) => {
    console.error("Donors page error:", error);
  };
  useEffect(() => {
    getAllListValuesByListNameId(
      authConfig.tagsListNameId,
      (data) =>
        setTagsList(
          data?.listValues?.map((item) => ({
            value: item.id,
            key: item.listValue,
          }))
        ),
      handleError
    );
  }, [authConfig]);

  useEffect(() => {
    if (selectedTags) {
      const tagIds = selectedTags?.map((tag) => tag.value);
      handleFilterChange("tagsFilter", tagIds, "Tags");
    } else {
      handleFilterChange("tagsFilter", [], "Tags");
    }
  }, [selectedTags]);

  const handleServiceChange = (event) => {
    const value = event.target?.value;
    setSelectedTags(value);
  };
  const onDelete = (id) => {
    setSelectedUsers((prevUsers) => prevUsers.filter((user) => user.id !== id));
  };

  const columns = [
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.4,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "contactNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        // Ensure params.value is neither null nor undefined
        const contactNumber = params && params.value ? params.value : "";

        // If contactNumber is empty or invalid, return nothing
        if (
          contactNumber === "" ||
          contactNumber === null ||
          contactNumber === undefined
        ) {
          return null; // Handle cases with no valid mobile number
        }

        return contactNumber.length > 9 ? (
          <Tooltip title={contactNumber}>
            <Link
              href={`tel:${contactNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{contactNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${contactNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {contactNumber}
          </Link>
        );
      },
    },
    {
      field: "panNo",
      minWidth: 100,
      headerName: "Pan No",
      flex: 0.2,
    },
    {
      field: "state",
      minWidth: 90,
      headerName: "State",
      flex: 0.16,
      valueGetter: (params) => params.row?.donorMetaData?.state || "",
    },
    {
      field: "address",
      minWidth: 100,
      headerName: "Address",
      flex: 0.2,
      valueGetter: (params) => params.row?.donorMetaData?.address || "",
    },
    {
      field: "pinCode",
      minWidth: 60,
      headerName: "Pin Code",
      flex: 0.12,
      valueGetter: (params) => params.row?.donorMetaData?.pinCode || "",
    },
  ];

  const cols = [
    {
      field: "name",
      minWidth: 100,
      headerName: "Name",
      flex: 0.2,
    },
    {
      field: "email",
      minWidth: 100,
      headerName: "Email",
      flex: 0.2,
      renderCell: (params) => {
        const email = params?.value;

        return (
          <Link
            href={`mailto:${email}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {email}
          </Link>
        );
      },
    },
    {
      field: "contactNumber",
      headerName: "Mobile Number",
      flex: 0.13,
      minWidth: 100,
      renderCell: (params) => {
        const contactNumber = params?.value;

        return contactNumber?.length > 9 ? (
          <Tooltip title={contactNumber}>
            <Link
              href={`tel:${contactNumber}`}
              target="_blank"
              rel="noopener noreferrer"
              sx={{ color: "#6666ff" }}
            >
              <span>{contactNumber}</span>
            </Link>
          </Tooltip>
        ) : (
          <Link
            href={`tel:${contactNumber}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ color: "#6666ff" }}
          >
            {contactNumber}
          </Link>
        );
      },
    },
    {
      field: "panNo",
      minWidth: 100,
      headerName: "Pan No",
      flex: 0.2,
    },
    {
      field: "state",
      minWidth: 90,
      headerName: "State",
      flex: 0.16,
      valueGetter: (params) =>
        params.row?.donorMetaData?.state || params.row?.state,
    },
    {
      field: "address",
      minWidth: 100,
      headerName: "Address",
      flex: 0.2,
      valueGetter: (params) =>
        params.row?.donorMetaData?.address || params.row?.address,
    },
    {
      field: "pinCode",
      minWidth: 60,
      headerName: "Pin Code",
      flex: 0.12,
      valueGetter: (params) =>
        params.row?.donorMetaData?.pinCode || params.row?.pinCode,
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.09,
      minWidth: 100,
      renderCell: (params) => {
        const onClickDelete = () => {
          const id = params?.row?.id;
          onDelete(id);
        };
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Tooltip title="Delete">
              <CustomAvatar
                skin="light"
                variant="rounded"
                sx={{ mr: 5, width: 34, height: 34, cursor: "pointer" }}
                onClick={onClickDelete}
              >
                <Icon icon="iconamoon:trash" />
              </CustomAvatar>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  const handleRemoveFilter = (key, id) => {
    if (key === "tagsFilter" && id) {
      // Remove the specific ID from the servicesFilter value
      const updatedFilters = selectedFilters
        ?.map((filter) => {
          if (filter.key === key) {
            return {
              ...filter,
              value: filter.value.filter((tagId) => tagId !== id),
            };
          }
          return filter;
        })
        .filter((filter) => filter.value?.length > 0); // Remove the filter if no values remain
      setSelectedFilters(updatedFilters);
      if (selectedTags) {
        const updatedTags = selectedTags.filter((tag) => tag.value !== id);
        setSelectedTags(updatedTags);
      }
    } else {
      // Remove the entire filter
      setSelectedFilters(
        selectedFilters.filter((filter) => filter.key !== key)
      );

      switch (key) {
        case "nameFilter":
          setValue("nameFilter", "");
          break;
        case "emailFilter":
          setValue("emailFilter", "");
          break;
        case "mobileFilter":
          setValue("mobileFilter", "");
          break;
        case "panNoFilter":
          setValue("panNoFilter", "");
          break;
        case "stateFilter":
          setValue("stateFilter", "");
          break;
        case "addressFilter":
          setValue("addressFilter", "");
          break;
        case "pinCodeFilter":
          setValue("pinCodeFilter", "");
          break;
        case "orgId":
          setValue("tenantName", "");
          setTenantId("");
          break;
        default:
          break;
      }
    }
  };

  useEffect(() => {
    if (formData) {
      setValue("contactGroupName", formData?.name);
      setValue("description", formData?.description);
      setTenantId(formData?.orgId);
      setSelectedUsers(formData?.donorResponseList);
      setSelectedFilters(
        Object.entries(formData?.filters || {})?.map(([key, value]) => ({
          key,
          value,
        }))
      );
      setValue("emailFilter", formData?.filters?.emailFilter);
      setValue("mobileFilter", formData?.filters?.mobileFilter);
      setValue("nameFilter", formData?.filters?.nameFilter);
      setValue("pinCodeFilter", formData?.filters?.pinCodeFilter);
      setValue("addressFilter", formData?.filters?.addressFilter);
      setValue("stateFilter", formData?.filters?.stateFilter);
      setValue("panNoFilter", formData?.filters?.panNoFilter);
      setSelectedTags(
        tagsList?.filter((tag) =>
          formData?.filters?.tagsFilter?.includes(tag.value)
        )
      );
    }
  }, [formData]);

  const handleCancel = () => {
    onClose();
    setFinalList(false);
    setKeyword("");
    setTenantId("");
    setSelectedUsers([]);
    setSelectedTags("");
    setSelectedFilters([]);
    reset({
      contactGroupName: "",
      description: "",
      nameFilter: "",
      emailFilter: "",
      mobileFilter: "",
      panNoFilter: "",
      stateFilter: "",
      addressFilter: "",
      pinCodeFilter: "",
    });
  };

  const fetchDonors = async (currentPage, currentPageSize, selectedFilters) => {
    const url = getUrl(authConfig.donorsEndpoint) + "/all/donor";
    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setUserData(response.data?.donorsResponseList);
        setRowCount(
          response.data?.rowCount || response.data.donorsResponseList.length
        );
      }
    } catch (err) {
      console.error("Failed to fetch donors:", err);
    }
  };

  useEffect(() => {
    fetchDonors(page, pageSize, selectedFilters);
  }, [page, pageSize, selectedFilters]);

  const handleSuccess = () => {
    const message = `
    <div> 
      <h3>Donor Group Created Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Create Group. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleUpdateSuccess = () => {
    const message = `
    <div> 
      <h3>Donor Group Updated Successfully.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleUpdateFailure = () => {
    const message = `
    <div> 
      <h3> Failed to Update Group. Please try again later.</h3>
    </div>
  `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const [currentToastId, setCurrentToastId] = useState(null);

  const onFormInvalid = (toastError) => {
    if (currentToastId !== null) {
      toast.dismiss(currentToastId);
    }

    Object.keys(toastError).forEach((error) => {
      if (toastError[error].message.length > 0) {
        const toastId = toast.error(toastError[error].message, {
          duration: 4000, // Set the duration you prefer
          onClose: () => {
            setCurrentToastId(null); // Reset currentToastId when the toast closes
          },
        });
        setCurrentToastId(toastId);
      }
    });
  };

  const isApiCalling = useRef(false);

  async function submit(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    if (selectedUsers.length < 1) {
      toast.error(
        "At least one contact should be in final list to create a group",
        {
          style: { zIndex: 9999 }, // Adjust the zIndex to ensure it appears above the dialog
        }
      );
      return;
    }
    isApiCalling.current = true;

    const filters = {};

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      filters[key] = filter.value;
    });
    const fields = {
      name: data?.contactGroupName,
      description: data?.description,
      filters: filters,
      orgId:
        user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
      donorIds: selectedUsers?.map((user) => user.id),
    };

    try {
      const response = await auth.postDonorGroup(
        fields,
        handleFailure,
        handleSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleFailure();
    } finally {
      isApiCalling.current = false;
    }
    fetchContactGroups();
    handleCancel();
  }

  async function update(data) {
    if (isApiCalling.current) {
      // API call is already in progress, return early
      return;
    }

    if (selectedUsers.length < 1) {
      toast.error(
        "At least one contact should be in final list to create a group",
        {
          style: { zIndex: 9999 }, // Adjust the zIndex to ensure it appears above the dialog
        }
      );
      return;
    }

    isApiCalling.current = true;

    const filters = {};

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      filters[key] = filter.value;
    });
    const fields = {
      id: formData?.id,
      name: data?.contactGroupName,
      description: data?.description,
      filters: filters,
      orgId:
        user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
      donorIds: selectedUsers?.map((user) => user.id),
    };

    try {
      const response = await auth.patchDonorGroup(
        fields,
        handleUpdateFailure,
        handleUpdateSuccess
      );
    } catch (error) {
      console.error("Employee Creation failed:", error);
      handleUpdateFailure();
    } finally {
      isApiCalling.current = false;
    }
    fetchContactGroups();
    handleCancel();
  }

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  useEffect(() => {
    if (selectedRows.length > 0) {
      setFinalList(true);
    } else {
      setFinalList(false);
    }
  }, [selectedRows]);

  const handleFinalListClick = () => {
    const selectedData = usersData.filter((row) =>
      selectedRows.includes(row.id)
    );

    setSelectedUsers((prevSelectedUsers) => {
      const newSelectedData = selectedData.filter(
        (row) => !prevSelectedUsers.some((selected) => selected.id === row.id)
      );
      return [...prevSelectedUsers, ...newSelectedData];
    });
  };

  const handleFilterChange = (filterKey, value, label) => {
    setSelectedFilters((prevFilters) => {
      // Remove any existing filter with this key
      const filteredFilters = prevFilters.filter(
        (filter) => filter.key !== filterKey
      );

      // Only add the filter if there's a value
      if (value) {
        return [
          ...filteredFilters,
          {
            key: filterKey,
            value: value,
            label: label,
          },
        ];
      }

      return filteredFilters;
    });
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleCancel}>
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px", // Set fixed height for header
            marginLeft: { xl: 4, lg: 4, md: 4, sm: 4, xs: 4 },
          }}
          textAlign={"center"}
        >
          {!formData || Object.keys(formData).length === 0
            ? "Create a Donor Group"
            : "Update Donor Group"}
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              marginRight: { xl: 10, lg: 6, md: 6, sm: 5.5, xs: 5.7 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCancel}
              sx={{
                // p: "0.438rem",
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main", // Set background color to green
                "&:hover": {
                  backgroundColor: (theme) =>
                    `rgba(${theme.palette.customColors.main}, 0.16)`,
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            pt: (theme) => `${theme.spacing(8)} !important`,
            pb: (theme) => `${theme.spacing(5)} !important`,
            px: (theme) => [`${theme.spacing(8)} !important`],
          }}
        >
          <Card>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Donor Group name
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="contactGroupName"
                    control={control}
                    rules={{ required: "Donor Group Name is required" }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Donor Group Name"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Enter your Donor Group Name "
                        error={Boolean(errors.contactGroupName)}
                        helperText={errors.contactGroupName?.message}
                        aria-describedby="validation-contactGroupName"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <Controller
                    name="description"
                    control={control}
                    rules={{ required: false }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Description"
                        InputLabelProps={{ shrink: true }}
                        size="small"
                        placeholder="Add description "
                        error={Boolean(errors.description)}
                        helperText={errors.description?.message}
                        aria-describedby="validation-description"
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Card>
          <Card>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Donors filter criteria
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <Grid container spacing={5} style={{ padding: "16px" }}>
              <Grid item xs={12} sm={12}>
                {selectedFilters?.map((filter) => {
                  if (
                    filter.key === "tagsFilter" &&
                    Array.isArray(filter.value)
                  ) {
                    // If the filter is for services and the value is an array of IDs
                    return filter.value?.map((id) => {
                      // Find the corresponding object in servicesList for each ID
                      const matchedItem = tagsList?.find(
                        (item) => item.value === id
                      );

                      // Use the key of matchedItem if found, otherwise display the ID itself
                      const displayValue = matchedItem ? matchedItem.key : id;
                      return (
                        <Chip
                          key={`${filter.key}-${id}`} // Ensure unique key for each chip
                          label={`${filter.label}: ${displayValue}`}
                          onDelete={() => handleRemoveFilter(filter.key, id)} // Pass both filter key and ID
                          sx={{ mr: 1, mb: 1 }}
                        />
                      );
                    });
                  }

                  // For other filters, render a single chip
                  return (
                    filter.label && ( // Only render the Chip if label is not null or undefined
                      <Chip
                        key={filter.key}
                        label={`${filter.label}: ${filter.value}`}
                        onDelete={() => handleRemoveFilter(filter.key)}
                        sx={{ mr: 1, mb: 1 }}
                      />
                    )
                  );
                })}
              </Grid>
              {user?.organisationCategory === "SUPER_ADMIN" && (
                <Grid item xs={12} sm={3}>
                  <FormControl fullWidth error={Boolean(errors.tenantName)}>
                    <Controller
                      name="tenantName"
                      control={control}
                      rules={{
                        required: false,
                      }}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="tenantName"
                          label="NGO Name"
                          nameArray={tenantsList}
                          value={tenantId}
                          onChange={(event) => {
                            field.onChange(event.target?.value);
                            setTenantId(event.target?.value);
                            handleFilterChange(
                              "orgId",
                              event.target.value,
                              "NGO Name"
                            );
                          }}
                        />
                      )}
                    />
                    {errors.tenantName && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.tenantName.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="nameFilter"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        id="nameFilter"
                        placeholder="Search by name"
                        value={field.value || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleFilterChange(
                            "nameFilter",
                            e.target.value,
                            "Name"
                          );
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            fontSize: "0.9rem",
                            borderRadius: "5px",
                            backgroundColor: "white",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                }}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="emailFilter"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        id="emailFilter"
                        placeholder="Search by Email"
                        value={field.value || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleFilterChange(
                            "emailFilter",
                            e.target.value,
                            "Email"
                          );
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            fontSize: "0.9rem",
                            borderRadius: "5px",
                            backgroundColor: "white",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                }}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="mobileFilter"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        id="mobileFilter"
                        placeholder="Search by Mobile"
                        value={field.value || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleFilterChange(
                            "mobileFilter",
                            e.target.value,
                            "Mobile"
                          );
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            fontSize: "0.9rem",
                            borderRadius: "5px",
                            backgroundColor: "white",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                }}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="panNoFilter"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        id="panNoFilter"
                        placeholder="Search by pan number"
                        value={field.value || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleFilterChange(
                            "panNoFilter",
                            e.target.value,
                            "Pan Number"
                          );
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            fontSize: "0.9rem",
                            borderRadius: "5px",
                            backgroundColor: "white",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                }}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="stateFilter"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        id="stateFilter"
                        placeholder="Search by state"
                        value={field.value || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleFilterChange(
                            "stateFilter",
                            e.target.value,
                            "State"
                          );
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            fontSize: "0.9rem",
                            borderRadius: "5px",
                            backgroundColor: "white",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                }}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="addressFilter"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        id="addressFilter"
                        placeholder="Search by address"
                        value={field.value || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleFilterChange(
                            "addressFilter",
                            e.target.value,
                            "Address"
                          );
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            fontSize: "0.9rem",
                            borderRadius: "5px",
                            backgroundColor: "white",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                }}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth>
                  <Controller
                    name="pinCodeFilter"
                    control={control}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        id="pinCodeFilter"
                        placeholder="Search by pin code"
                        value={field.value || ""}
                        onChange={(e) => {
                          field.onChange(e.target.value);
                          handleFilterChange(
                            "pinCodeFilter",
                            e.target.value,
                            "Pin Code"
                          );
                        }}
                        sx={{
                          "& .MuiInputBase-root": {
                            fontSize: "0.9rem",
                            borderRadius: "5px",
                            backgroundColor: "white",
                          },
                        }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="start">
                              <SearchIcon
                                sx={{
                                  cursor: "pointer",
                                }}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={3}>
                <FormControl fullWidth error={Boolean(errors.tags)}>
                  <Controller
                    name="tags"
                    control={control}
                    render={({ field }) => (
                      <MultiSelectAutoComplete
                        id="tags"
                        label="Select Tags"
                        nameArray={tagsList}
                        value={selectedTags || []}
                        onChange={(e) => {
                          handleServiceChange(e);
                          field.onChange(e.target.value);
                        }}
                        error={Boolean(errors.tags)}
                      />
                    )}
                  />
                </FormControl>
              </Grid>
            </Grid>
          </Card>
          <Card>
            <Grid
              sx={{
                backgroundColor: "#d6d6f5",
                mt: 4,
                paddingTop: 0,
                height: "36px",
                display: "flex",
                alignItems: "center",
              }}
            >
              <Typography
                variant="body1"
                fontWeight={"bold"}
                sx={{ mt: 0, ml: 2 }}
              >
                Donors
              </Typography>
              <Divider />
            </Grid>
            <Divider />

            <div style={{ height: 380, width: "100%" }}>
              <DataGrid
                rows={usersData}
                columns={columns}
                checkboxSelection
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                //onSelectionModelChange={handleSelection}
                rowHeight={38}
                headerHeight={38}
                onSelectionModelChange={(newSelection) => {
                  setSelectedRows(newSelection);
                }}
              />
            </div>
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                marginTop: "20px",
                marginBottom: "20px",
              }}
            >
              <Tooltip
                title={!finalList ? "Select at least one row to enable" : ""}
              >
                <span>
                  <Button
                    disabled={!finalList}
                    variant="contained"
                    onClick={handleFinalListClick}
                  >
                    Add to Final List
                  </Button>
                </span>
              </Tooltip>
            </div>
          </Card>
          <div style={{ height: 380, width: "100%" }}>
            <DataGrid
              rows={selectedUsers}
              columns={cols}
              rowsPerPageOptions={rowsPerPageOptions}
              rowCount={selectedUsers?.length}
              rowHeight={38}
              headerHeight={38}
            />
          </div>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5)} !important`,
            height: "50px", // Set fixed height for footer
          }}
        >
          <Box
            sx={{
              marginRight: { xl: 10, lg: 6, md: 6, sm: 5.5, xs: 6 }, // Correct marginRight syntax
              display: "flex", // Ensure the Box is a flex container
              gap: 2,
            }}
          >
            <Button
              display="flex"
              variant="outlined"
              color="primary"
              onClick={handleCancel}
            >
              Cancel
            </Button>
            {!formData || Object.keys(formData).length === 0 ? (
              <Button
                display="flex"
                justifyContent="center"
                variant="contained"
                color="primary"
                onClick={handleSubmit(submit, onFormInvalid)}
              >
                Create
              </Button>
            ) : (
              <Button
                display="flex"
                justifyContent="center"
                variant="contained"
                color="primary"
                onClick={handleSubmit(update, onFormInvalid)}
              >
                Update
              </Button>
            )}
          </Box>
        </DialogActions>
        <Toaster position="top-right" />
      </Dialog>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: "white",
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default ContactGroupDetails;
