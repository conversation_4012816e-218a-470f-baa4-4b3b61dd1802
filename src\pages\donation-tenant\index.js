import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
    Box,
    Typography,
    Paper,
    useTheme,
    Grid,
    CircularProgress,
    Card, 
    CardContent,
    Avatar 
} from '@mui/material';
import MonetizationOnOutlinedIcon from '@mui/icons-material/MonetizationOnOutlined';
import CategoryOutlinedIcon from '@mui/icons-material/CategoryOutlined'; // For donation heads
import EventNoteOutlinedIcon from '@mui/icons-material/EventNoteOutlined'; // For current period/recent
import TrendingUpOutlinedIcon from '@mui/icons-material/TrendingUpOutlined'; // Example: Maybe for top head value

// Dynamically import ApexCharts
const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

// --- Mock Data Fetching (Replace with API calls) ---
const fetchMonthlyDonationsByYear = async () => {
    console.log('(Mock) Fetching monthly totals');
    await new Promise(resolve => setTimeout(resolve, 300)); // Simulate network delay
    return {
        series: [{ name: 'Monthly Donations', data: [300, 250, 350, 100, 400, 500, 0, 0, 0, 0, 0, 0] }],
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    };
};

// Mock fetching for donation head trends
const fetchDonationHeadTrends = async () => {
    console.log('(Mock) Fetching donation head trends');
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate network delay

    return {
        series: [
            { name: 'Education', data: [100, 80, 120, 30, 150, 180, 0, 0, 0, 0, 0, 0] },
            { name: 'Healthcare', data: [150, 100, 150, 50, 180, 220, 0, 0, 0, 0, 0, 0] },
            { name: 'Food Aid', data: [50, 70, 80, 20, 70, 100, 0, 0, 0, 0, 0, 0] },
        ],
        categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    };
};

// NEW: Mock fetching for tenant stats cards
const fetchTenantStatsData = async () => {
    await new Promise(resolve => setTimeout(resolve, 400)); // Simulate delay
    // Calculate total from monthly mock data for consistency
    const monthlyData = [300, 250, 350, 100, 400, 500, 0, 0, 0, 0, 0, 0];
    const totalDonations = monthlyData.reduce((sum, val) => sum + val, 0);
    // Example stats relevant to tenant
    return {
        totalDonations: totalDonations,
        donationHeadsCount: 3, // From fetchDonationHeadTrends mock
        currentPeriod: 'This Year', // Or 'This Month' if data scope changes
        topHead: 'Healthcare' // Example: Could be dynamically determined
    };
};

// --- Reusable Stats Card Component (Copied from admin) ---
const StatsCard = ({ title, value, icon, formatValue, loading }) => {
    const theme = useTheme();
    const cardColor = theme.palette.primary.main;

    // Default formatting
    const displayValue = formatValue ? formatValue(value) : (value ?? '-'); // Show '-' if value is null/undefined

    return (
        <Card sx={{ 
            position: 'relative', 
            overflow: 'visible', 
            borderLeft: `5px solid ${cardColor}`,
            borderRadius: '8px'
            }} 
            elevation={2}
        >
            <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
                <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                        {title}
                    </Typography>
                    <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
                        {loading ? <CircularProgress size={20} /> : displayValue}
                    </Typography>
                </Box>
                <Avatar sx={{ 
                    bgcolor: `${cardColor}20`,
                    color: cardColor, 
                    width: 40, 
                    height: 40 
                    }}>
                    {icon}
                </Avatar>
            </CardContent>
        </Card>
    );
};

// --- Donation NGO Page Component ---
const DonationTenantPage = () => {
    const theme = useTheme();
    // State for charts
    const [monthlyChartData, setMonthlyChartData] = useState(null);
    const [loadingMonthly, setLoadingMonthly] = useState(true);
    const [headTrendsChartData, setHeadTrendsChartData] = useState(null);
    const [loadingHeadTrends, setLoadingHeadTrends] = useState(true);
    // NEW: State for stats cards
    const [stats, setStats] = useState(null);
    const [loadingStats, setLoadingStats] = useState(true);

    const currencySymbol = '₹';

    // Fetch data on component mount
    useEffect(() => {
        const loadAllData = async () => {
            setLoadingMonthly(true);
            setLoadingHeadTrends(true);
            setLoadingStats(true); // Start loading stats
            try {
                // Fetch all datasets in parallel
                const [monthlyData, headTrendsData, statsData] = await Promise.all([
                    fetchMonthlyDonationsByYear(),
                    fetchDonationHeadTrends(),
                    fetchTenantStatsData() // Fetch stats
                ]);
                setMonthlyChartData(monthlyData);
                setHeadTrendsChartData(headTrendsData);
                setStats(statsData); // Set stats data
            } catch (error) {
                console.error("Error fetching donation data:", error);
                setMonthlyChartData(null);
                setHeadTrendsChartData(null);
                setStats(null); // Handle stats error
            } finally {
                setLoadingMonthly(false);
                setLoadingHeadTrends(false);
                setLoadingStats(false); // Finish loading stats
            }
        };
        loadAllData();
    }, []);

    // --- Chart Options --- 
    // Options for Monthly Totals Bar Chart
    const monthlyChartOptions = {
        chart: { type: 'bar', toolbar: { show: false }, zoom: { enabled: false }, foreColor: theme.palette.text.secondary },
        colors: [theme.palette.primary.main],
        plotOptions: { bar: { borderRadius: 4, columnWidth: '45%', distributed: false } },
        dataLabels: { enabled: false },
        xaxis: { categories: monthlyChartData?.categories || [], axisBorder: { show: false }, axisTicks: { show: false }, labels: { style: { fontSize: '12px' } } },
        yaxis: { title: { text: `Total Donations (${currencySymbol})`, style: { fontSize: '12px', fontWeight: 500 } }, labels: { style: { fontSize: '12px' }, formatter: (value) => `${Math.floor(value)}` } },
        grid: { borderColor: theme.palette.divider, strokeDashArray: 3, xaxis: { lines: { show: false } }, yaxis: { lines: { show: true } } },
        tooltip: { theme: theme.palette.mode, y: { formatter: (value) => `${currencySymbol}${value.toLocaleString()}`, title: { formatter: (seriesName) => `${seriesName}:` } } }
    };

    // Options for Donation Head Trends Line Chart
    const headTrendsChartOptions = {
        chart: { type: 'line', toolbar: { show: false }, zoom: { enabled: false }, foreColor: theme.palette.text.secondary },
        colors: [theme.palette.primary.main, theme.palette.success.main, theme.palette.warning.main],
        dataLabels: { enabled: false },
        stroke: { curve: 'smooth', width: 2 },
        markers: { size: 4, strokeColors: '#fff', strokeWidth: 2, hover: { size: 6 } },
        xaxis: { categories: headTrendsChartData?.categories || [], axisBorder: { show: true, color: theme.palette.divider }, axisTicks: { show: false }, labels: { style: { fontSize: '12px' } } },
        yaxis: { title: { text: `Donations (${currencySymbol})`, style: { fontSize: '12px', fontWeight: 500 } }, labels: { style: { fontSize: '12px' }, formatter: (value) => `${Math.floor(value)}` } },
        grid: { borderColor: theme.palette.divider, strokeDashArray: 3 },
        legend: { position: 'top', horizontalAlign: 'right', floating: true, offsetY: -5, offsetX: -5 },
        tooltip: { theme: theme.palette.mode, y: { formatter: (value) => `${currencySymbol}${value?.toLocaleString()}`, title: { formatter: (seriesName) => `${seriesName}:` } } }
    };

    return (
        <Grid container spacing={3}>
             {/* NEW: Stats Cards Row */}
             <Grid item xs={12} sm={6} md={3}>
                 <StatsCard
                    title="Total Donations"
                    value={stats?.totalDonations}
                    loading={loadingStats}
                    icon={<MonetizationOnOutlinedIcon />}
                    formatValue={(val) => `${currencySymbol}${val?.toLocaleString() || '0'}`}
                />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                    title="Donation Heads"
                    value={stats?.donationHeadsCount}
                    loading={loadingStats}
                    icon={<CategoryOutlinedIcon />}
                />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                 <StatsCard
                    title="Current Period"
                    value={stats?.currentPeriod}
                    loading={loadingStats}
                    icon={<EventNoteOutlinedIcon />}
                />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
                <StatsCard
                    title="Top Head (Example)"
                    value={stats?.topHead}
                    loading={loadingStats}
                    icon={<TrendingUpOutlinedIcon />}
                />
            </Grid>

            {/* Title Row (adjusted margin) */}
            <Grid item xs={12} sx={{ mt: 1 }}> {/* Added slight top margin to separate from cards */}
                <Box sx={{ mb: 2 }}>
                    <Typography variant="h4">
                        Donation Overview
                    </Typography>
                </Box>
            </Grid>

            {/* Charts Row */}
            {/* Monthly Totals Bar Chart */}
            <Grid item xs={12} md={6}>
                <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
                     <Typography variant="h6" sx={{ mb: 2 }}>
                        Total Donations per Month
                    </Typography>
                    {loadingMonthly ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                            <CircularProgress />
                        </Box>
                    ) : monthlyChartData ? (
                        <Chart
                            options={monthlyChartOptions}
                            series={monthlyChartData.series}
                            type="bar"
                            height={300}
                        />
                    ) : (
                        <Typography color="error">
                            Could not load monthly donation data.
                        </Typography>
                    )}
                </Paper>
            </Grid>

            {/* Donation Head Trends Line Chart */}
             <Grid item xs={12} md={6}>
                <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
                     <Typography variant="h6" sx={{ mb: 2 }}>
                        Donations by Head (Monthly Trend)
                    </Typography>
                    {loadingHeadTrends ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                            <CircularProgress />
                        </Box>
                    ) : headTrendsChartData ? (
                        <Chart
                            options={headTrendsChartOptions}
                            series={headTrendsChartData.series}
                            type="line"
                            height={300}
                        />
                    ) : (
                        <Typography color="error">
                            Could not load donation head trend data.
                        </Typography>
                    )}
                </Paper>
            </Grid>
        </Grid>
    );
};

// Configure page access
DonationTenantPage.authGuard = false;

export default DonationTenantPage;
