import dynamic from 'next/dynamic';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const DumbBellChart = () => {
  const state = {
    series: [
      {
        data: [
          { x: 'Maintenance', y: [3000, 5000] },    // Societies: [3000], Service Providers: [5000]
          { x: 'Repair Services', y: [3200, 4700] }, // Societies: [3200], Service Providers: [4700]
          { x: 'Consultancy', y: [2800, 7500] },    // Societies: [2800], Service Providers: [7500]
          { x: 'Security', y: [3400, 4600] },       // Societies: [3400], Service Providers: [4600]
          { x: 'Landscaping', y: [3500, 4000] },    // Societies: [3500], Service Providers: [4000]
          { x: 'Utilities', y: [4000, 5700] }       // Societies: [4000], Service Providers: [5700]
        ]
      }
    ],
    options: {
      chart: {
        height: 390,
        type: 'rangeBar',
        zoom: {
          enabled: false
        }
      },
      colors: ['#EC7D31', '#36BDCB'],
      plotOptions: {
        bar: {
          horizontal: true,
          isDumbbell: true,
          dumbbellColors: [['#EC7D31', '#36BDCB']]
        }
      },
      title: {
        text: 'Societies vs Service Providers Disparity'
      },
      legend: {
        show: true,
        showForSingleSeries: true,
        position: 'top',
        horizontalAlign: 'left',
        customLegendItems: ['Societies', 'Service Providers']
      },
      fill: {
        type: 'gradient',
        gradient: {
          gradientToColors: ['#36BDCB'],
          inverseColors: false,
          stops: [0, 100]
        }
      },
      grid: {
        xaxis: {
          lines: {
            show: true
          }
        },
        yaxis: {
          lines: {
            show: false
          }
        }
      }
    },
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="rangeBar" height={390} />
      </div>
    </div>
  );
};

export default DumbBellChart;
