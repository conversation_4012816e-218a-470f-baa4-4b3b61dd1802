import {
  Box,
  CardContent,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  Divider,
  FormControl,
  InputAdornment,
  TextField,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import FallbackSpinner from "src/@core/components/spinner";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import AdvancedSearch from "./AdvancedSearch";
import useColumns from "./Columns";
import DeleteDialog from "./DeleteDialog";
import DonationDialog from "./DonationDialog";
import ViewDialog from "./ViewDialog";
import { useRBAC } from "../permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";
import { useRouter } from "next/router";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const LandingPage = () => {
  const [userList, setUserList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [sendPDF, setSendPDF] = useState(false);
  const [sendPaymentLink, setSendPaymentLink] = useState(false);
  const [sendWhatsAppDialog, setSendWhatsAppDialog] = useState(false);

  const [selectedFilters, setSelectedFilters] = useState([]);
  const handleClosePDFDialog = () => {
    setSendPDF(false);
    setSendPaymentLink(false);
  };

  const handleCloseWhatsAppDialog = () => {
    setSendWhatsAppDialog(false);
  };

  const auth = useAuth();

  const {
    user,
    donationReceiptDetails,
    setDonationReceiptDetails,
    getAllListValuesByListNameId,
    sendWatiMessage,
  } = useContext(AuthContext);

  const [searchKeyword, setSearchKeyword] = useState("");
  const [keyword, setKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  const handleClose = () => setOpenDialogContent(false);
  const [pdfLoad, setPdfLoad] = useState(false);
  const [whatsappLoad, setWhatsappLoad] = useState(false);

  const [loading, setLoading] = useState(true);

  const [selectedZoneId, setSelectedZoneId] = useState("");

  const {
    handleSubmit,
    control,
    setValue,
    reset,
    formState: { errors },
  } = useForm();

  const [tenantId, setTenantId] = useState("");
  const [donorId, setDonorId] = useState("");
  const [donorData, setDonorData] = useState({});
  const [donationTypesData, setDonationTypesData] = useState([]);
  const [paymentModeData, setPaymentModeData] = useState([]);
  const [paymentTypeData, setPaymentTypeData] = useState([]);
  const [tenantsList, setTenantsList] = useState([]);
  const [donorsList, setDonorsList] = useState([]);
  const [donationHeadsList, setDonationHeadsList] = useState([]);
  const [allDonationHeads, setAllDonationHeads] = useState([]);
  useEffect(() => {
    // Fetch all tenants
    axios({
      method: "get",
      url: getUrl(authConfig.organisationsEndpoint) + "/TENANT",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setTenantsList(
          res.data.map((item) => ({
            value: item.id,
            key: item.name,
          }))
        );
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  useEffect(() => {
    let id =
      user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId;
    if (id) {
      axios({
        method: "get",
        url: getUrl(authConfig.donationHeadEndpoint) + "/by-org/" + id,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setDonationHeadsList(
            res.data.donationHeads?.map((item) => ({
              value: item.id,
              key: item.name,
            }))
          );
        })
        .catch((err) => console.log("Donation error", err));
    }

    const tenId =
      user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId;
    if (tenId) {
      axios({
        method: "get",
        url:
          getUrl(authConfig.donorsEndpoint) +
          "/donor-dropdown-by-tenant-id/" +
          tenId,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setDonorsList(
            res.data.map((item) => ({
              value: item.id,
              key: item.name,
            }))
          );
        })
        .catch((err) => console.log("Employees error", err));
    }
  }, [tenantId]);

  useEffect(() => {
    if (donorId) {
      axios({
        method: "get",
        url: getUrl(authConfig.donorsEndpoint) + "/" + donorId,
        headers: getAuthorizationHeaders(),
      })
        .then((res) => {
          setDonorData(res.data);
        })
        .catch((err) => console.log("Donation error", err));
    }
  }, [donorId]);

  useEffect(() => {
    axios({
      method: "get",
      url: getUrl(authConfig.donationHeadEndpoint) + "/get-donation-heads",
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setAllDonationHeads(
          res.data?.map((item) => ({
            value: item.id,
            key: item.donationHeadName,
          }))
        );
      })
      .catch((err) => console.log("Donation error", err));
  }, []);

  const handleError = (error) => {
    console.error("Employees page:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.donationListNameId,
        (data) =>
          setDonationTypesData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.paymentModeListNameId,
        (data) =>
          setPaymentModeData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
      getAllListValuesByListNameId(
        authConfig.paymentTypeListNameId,
        (data) =>
          setPaymentTypeData(
            data?.listValues.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  const fetchReceipts = async (
    currentPage,
    currentPageSize,
    searchKeyword,
    selectedFilters
  ) => {
    const url = getUrl(authConfig.donationReceiptEndpoint) + "/all";

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyWord: searchKeyword,
    };

    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      data[key] = filter.value;
    });

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        const receipts = response.data.donationReceipts || [];

        setUserList(receipts);

        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseCreateDialog = () => {
    reset();
    setOpenDialog(false);
    setSelectedZoneId("");
    setDonationReceiptDetails(null);
    setDonorData({});
    setTenantId("");
    setDonorId("");
  };

  const handleViewClose = () => {
    setOpenViewDialog(false);
    setDonationReceiptDetails(null);
    setDonorData({});
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchReceipts(page, pageSize, searchKeyword, selectedFilters);
  };

  useEffect(() => {
    fetchReceipts(page, pageSize, searchKeyword, selectedFilters);
  }, [page, pageSize, searchKeyword, selectedFilters]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const handleOpenDialog = () => {
    setOpenDialog(true);
  };

  const [menu, setMenu] = useState(null);

  const handleCloseMenuItems = () => {
    setMenu(null);
  };

  const [openAdvancedSearch, setOpenAdvancedSearch] = useState(false);
  const [searchingState, setSearchingState] = useState(false);
  const handleAdvancedSearch = () => {
    setOpenAdvancedSearch(!openAdvancedSearch);
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setSearchingState(false); // Reset search state if needed
  };

  const handleApplyFilters = (filters) => {
    setSelectedFilters(filters); // Set filters for chips
    setSearchingState(true); // Trigger search
  };
  // Function to remove a single filter
  const handleRemoveFilter = (filterKey) => {
    setSelectedFilters((prevFilters) =>
      prevFilters.filter((filter) => filter.key !== filterKey)
    );
  };
  const handleSuccess = () => {
    handleClosePDFDialog();
    const message = `
        <div> 
          <h3> Receipt Sent Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    handleClosePDFDialog();
    const message = `
        <div> 
          <h3> Failed to Send Receipt. Please try again later.</h3>
        </div>
      `;

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handlePDFSend = () => {
    setPdfLoad(true);
    const url =
      getUrl(authConfig.donationReceiptEndpoint) +
      "/generate-pdf/" +
      currentRow?.id;

    axios({
      method: "post",
      url: url,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        handleSuccess();
        setPdfLoad(false);
      })
      .catch((err) => {
        handleFailure();
        setPdfLoad(false);
      });
  };

  const handleWhatsAppSuccess = () => {
    handleCloseWhatsAppDialog();
    const message = `
        <div>
          <h3> Receipt Sent via WhatsApp Successfully.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleWhatsAppFailure = (err) => {
    handleCloseWhatsAppDialog();
    const message = `
        <div>
          <h3> Failed to Send Receipt via WhatsApp. Please try again later.</h3>
        </div>
      `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleSendReceipt = async () => {
    setWhatsappLoad(true);

    // Get donor mobile number from current row
    const mobileNumber =
      currentRow?.tenantDonorsDTO?.mobileNumber ||
      currentRow?.selfRegisteredDonorDTO?.donorMobileNumber;

    if (!mobileNumber) {
      handleWhatsAppFailure("No mobile number found for this donor");
      setWhatsappLoad(false);
      return;
    }

    const url =
      getUrl(authConfig.donationReceiptEndpoint) +
      "/api/wati/shareReceiptViaWA?mobileNumber=" +
      mobileNumber +
      "&receiptId=" +
      currentRow?.id;

    const headers = getAuthorizationHeaders();

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
      });

      if (response.data === "Message sent successfully") {
        handleWhatsAppSuccess();
      } else {
        handleWhatsAppFailure("WhatsApp number not registered");
      }
      setWhatsappLoad(false);
    } catch (error) {
      handleWhatsAppFailure(error);
      setWhatsappLoad(false);
    } finally {
      setLoading(false);
    }
  };

  const columns = useColumns({
    menu,
    setMenu,
    setOpenDialog,
    setOpenViewDialog,
    setOpenDeleteDialog,
    setCurrentRow,
    currentRow,
    donationTypesData,
    setSendPDF,
    setSendPaymentLink,
    setSendWhatsAppDialog,
  });

  const { canMenuPage, canMenuPageSection, rbacRoles } = useRBAC();
  const router = useRouter();

  const canAccessDonationReceipts = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.DONATION_RECEIPT, requiredPermission);

  const canAccessActions = (requiredPermission, section) =>
    canMenuPageSection(
      MENUS.LEFT,
      PAGES.DONATION_RECEIPT,
      section,
      requiredPermission
    );

  useEffect(() => {
    if (rbacRoles != null && rbacRoles.length > 0) {
      if (!canAccessDonationReceipts(PERMISSIONS.READ)) {
        router.push("/401");
      }
    }
  }, [rbacRoles]);
  if (canAccessDonationReceipts(PERMISSIONS.READ)) {
    return (
      <>
        <Grid>
          <Box
            sx={{
              py: 3,
              px: 6,
              rowGap: 2,
              columnGap: 4,
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              justifyContent: "space-between",
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
                <Typography variant="h6">{"Donation Receipts"}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <Grid
                  container
                  spacing={2}
                  // alignItems="center"
                  justifyContent="flex-end"
                >
                  {/* <Grid item xs={12} sm={7} md={4} lg={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="mainSearch"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            id="mainSearch"
                            placeholder="Search by Donor name and email"
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                              setSearchKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                                fetchReceipts(
                                  page,
                                  pageSize,
                                  searchKeyword,
                                  selectedFilters
                                );
                              }
                            }}
                            sx={{
                              "& .MuiInputBase-root": {
                                height: "40px",
                              },
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                      marginRight: "-15px",
                                    }}
                                    onClick={() => {
                                      setSearchKeyword(keyword);
                                      fetchReceipts(
                                        page,
                                        pageSize,
                                        searchKeyword,
                                        selectedFilters
                                      );
                                    }}
                                  />{" "}
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid> */}
                  {canAccessActions(
                    PERMISSIONS.FULL_ACCESS,
                    "Advanced_Search"
                  ) && (
                    <Grid item xs="auto" sm="auto" md="auto" lg="auto">
                      <AdvancedSearch
                        open={openAdvancedSearch}
                        toggle={handleAdvancedSearch}
                        searchingState={searchingState}
                        setSearchingState={setSearchingState}
                        selectedFilters={selectedFilters}
                        clearAllFilters={clearAllFilters}
                        onApplyFilters={handleApplyFilters}
                        tenantsList={tenantsList}
                        allDonationHeads={allDonationHeads}
                        paymentModeData={paymentModeData}
                      />
                    </Grid>
                  )}
                  {canAccessActions(PERMISSIONS.FULL_ACCESS, "Add") && (
                    <Grid item xs="auto" sm="auto" md="auto" lg="auto">
                      <Button variant="contained" onClick={handleOpenDialog}>
                        Add New Receipt
                      </Button>
                    </Grid>
                  )}
                </Grid>
              </Grid>
            </Grid>
          </Box>
          <Divider />

          <CardContent>
            <>
              <Box sx={{ display: "flex", flexWrap: "wrap", mb: 2 }}>
                {selectedFilters.map((filter) => {
                  if (filter.label === "NGO Name") {
                    const matchedItem = tenantsList.find(
                      (item) => item.value === filter.value
                    );

                    // Use the key of matchedItem if found, otherwise display the ID itself
                    const displayValue = matchedItem ? matchedItem.key : id;

                    return (
                      <Chip
                        key={filter.key} // Ensure unique key for each chip
                        label={`${filter.label}: ${displayValue}`}
                        onDelete={() => handleRemoveFilter(filter.key)} // Pass both filter key and ID
                        sx={{ mr: 1, mb: 1 }}
                      />
                    );
                  }

                  if (filter.label === "Donation Head") {
                    const matchedItem = allDonationHeads.find(
                      (item) => item.value === filter.value
                    );

                    // Use the key of matchedItem if found, otherwise display the ID itself
                    const displayValue = matchedItem ? matchedItem.key : id;

                    return (
                      <Chip
                        key={filter.key} // Ensure unique key for each chip
                        label={`${filter.label}: ${displayValue}`}
                        onDelete={() => handleRemoveFilter(filter.key)} // Pass both filter key and ID
                        sx={{ mr: 1, mb: 1 }}
                      />
                    );
                  }

                  if (filter.label === "Payment Mode") {
                    const matchedItem = paymentModeData.find(
                      (item) => item.value === filter.value
                    );

                    // Use the key of matchedItem if found, otherwise display the ID itself
                    const displayValue = matchedItem ? matchedItem.key : id;

                    return (
                      <Chip
                        key={filter.key} // Ensure unique key for each chip
                        label={`${filter.label}: ${displayValue}`}
                        onDelete={() => handleRemoveFilter(filter.key)} // Pass both filter key and ID
                        sx={{ mr: 1, mb: 1 }}
                      />
                    );
                  }

                  const displayValue =
                    filter.label === "Role" && matchedItem
                      ? matchedItem.key
                      : filter.value;

                  return (
                    filter.label && ( // Only render the Chip if label is not null or undefined
                      <Chip
                        key={filter.key}
                        label={`${filter.label}: ${displayValue}`}
                        onDelete={() => handleRemoveFilter(filter.key)}
                        sx={{ mr: 1, mb: 1 }}
                      />
                    )
                  );
                })}
              </Box>
            </>
            <div style={{ height: 430, width: "100%" }}>
              {loading ? (
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  height="60vh"
                >
                  <FallbackSpinner />
                </Box>
              ) : (
                <DataGrid
                  rows={userList || []}
                  columns={columns}
                  pagination
                  pageSize={pageSize}
                  page={page - 1}
                  rowsPerPageOptions={rowsPerPageOptions}
                  rowCount={rowCount}
                  paginationMode="server"
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  rowHeight={38}
                  headerHeight={38}
                />
              )}
            </div>
          </CardContent>
          <DeleteDialog
            open={openDeleteDialog}
            onClose={handleCloseDeleteDialog}
            data={currentRow}
          />

          <DonationDialog
            open={openDialog}
            onClose={handleCloseCreateDialog}
            formData={donationReceiptDetails}
            control={control}
            errors={errors}
            setValue={setValue}
            handleSubmit={handleSubmit}
            donationTypesData={donationTypesData}
            paymentModeData={paymentModeData}
            paymentTypeData={paymentTypeData}
            tenantsList={tenantsList}
            donorsList={donorsList}
            fetchReceipts={fetchReceipts}
            page={page}
            pageSize={pageSize}
            searchKeyword={searchKeyword}
            donationHeadsList={donationHeadsList}
            tenantId={tenantId}
            setTenantId={setTenantId}
            donorId={donorId}
            setDonorId={setDonorId}
            donorData={donorData}
          />
          <ViewDialog
            open={openViewDialog}
            onClose={handleViewClose}
            data={donationReceiptDetails}
            paymentModeData={paymentModeData}
            donationTypesData={donationTypesData}
          />
        </Grid>
        <Dialog
          open={sendPDF}
          onClose={handleClosePDFDialog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                {sendPaymentLink
                  ? `Are you sure you want to send payment link to donor ? Please confirm`
                  : `Are you sure you want to send receipt to donor ? Please confirm`}
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={sendPaymentLink ? handleClosePDFDialog : handlePDFSend}
                sx={{ margin: "auto", width: 100 }}
              >
                {pdfLoad ? (
                  <CircularProgress color="inherit" size={22} />
                ) : (
                  "Proceed"
                )}
              </Button>
              <Button
                variant="outlined"
                onClick={handleClosePDFDialog}
                sx={{ margin: "auto", width: 100 }}
              >
                Cancel
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        <Dialog
          open={openDialogContent}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="alert-dialog-description"
                color="primary.main"
              >
                <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleClose}
                sx={{ margin: "auto", width: 100 }}
              >
                Okay
              </Button>
            </DialogActions>
          </Box>
        </Dialog>

        {/* WhatsApp Confirmation Dialog */}
        <Dialog
          open={sendWhatsAppDialog}
          onClose={handleCloseWhatsAppDialog}
          aria-labelledby="whatsapp-dialog-title"
          aria-describedby="whatsapp-dialog-description"
          PaperProps={{
            sx: {
              p: (theme) => `${theme.spacing(2.5)} !important`,
              backgroundColor: (theme) => theme.palette.primary.background,
            },
          }}
        >
          <Box
            sx={{
              width: "100%",
              borderRadius: 1,
              textAlign: "center",
              border: (theme) => `1px solid ${theme.palette.divider}`,
              borderColor: "primary.main",
            }}
          >
            <DialogContent>
              <DialogContentText
                id="whatsapp-dialog-description"
                color="primary.main"
              >
                Are you sure you want to send receipt via WhatsApp?
              </DialogContentText>
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                onClick={handleSendReceipt}
                sx={{ margin: "auto", width: 100 }}
              >
                {whatsappLoad ? (
                  <CircularProgress color="inherit" size={22} />
                ) : (
                  "Proceed"
                )}
              </Button>
              <Button
                variant="outlined"
                onClick={handleCloseWhatsAppDialog}
                sx={{ margin: "auto", width: 100 }}
              >
                Cancel
              </Button>
            </DialogActions>
          </Box>
        </Dialog>
      </>
    );
  } else {
    return null;
  }
};

export default LandingPage;
