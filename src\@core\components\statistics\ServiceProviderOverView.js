import React, { useState } from "react";
import dynamic from "next/dynamic";
import { Menu, MenuItem, IconButton, Box, Typography } from "@mui/material";
import { Icon } from "@iconify/react";
import dayjs from "dayjs";

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const ServiceProviderOverView = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("Daily");
  const [anchorEl, setAnchorEl] = useState(null);

  const timeFilters = ["Daily", "Weekly", "Monthly", "Quarterly", "Annually"];
  const allCategories = ["Onboarded", "Meetings Happened", "Empaneled", "Total Conversations"];

  const onboardingData = [
    { date: "2025-01-01", category: "Onboarded", count: 10 },
    { date: "2025-01-05", category: "Meetings Happened", count: 12 },
    { date: "2025-01-10", category: "Empaneled", count: 15 },
    { date: "2025-01-15", category: "Total Conversations", count: 18 },
    { date: "2025-01-20", category: "Onboarded", count: 22 },
    { date: "2025-01-25", category: "Meetings Happened", count: 25 },
    { date: "2025-02-01", category: "Empaneled", count: 30 },
    { date: "2025-02-05", category: "Total Conversations", count: 20 },
    { date: "2025-02-10", category: "Onboarded", count: 35 },
    { date: "2025-02-15", category: "Meetings Happened", count: 30 },
    { date: "2025-02-20", category: "Empaneled", count: 40 },
    { date: "2025-02-25", category: "Total Conversations", count: 45 },
    { date: "2025-02-25", category: "Empaneled", count: 45 },
    { date: "2025-03-01", category: "Onboarded", count: 50 },
    { date: "2025-03-05", category: "Meetings Happened", count: 55 },
    { date: "2025-03-10", category: "Empaneled", count: 60 },
    { date: "2025-03-15", category: "Total Conversations", count: 65 },
    { date: "2025-03-20", category: "Onboarded", count: 80 },
    { date: "2025-03-25", category: "Meetings Happened", count: 85 },
    { date: "2025-03-30", category: "Empaneled", count: 90 },
  ];

  // Function to filter data based on selected time range
  const filterDataByTimeRange = (data, range) => {
    const now = dayjs();
    switch (range) {
      case "Daily":
        return data.filter((entry) => dayjs(entry.date).isSame(now, "day"));
      case "Weekly":
        return data.filter((entry) => dayjs(entry.date).isSame(now, "week"));
      case "Monthly":
        return data.filter((entry) => dayjs(entry.date).isSame(now, "month"));
      case "Quarterly":
        return data.filter((entry) => {
          const quarterStart = now.startOf("quarter");
          const quarterEnd = now.endOf("quarter");
          return dayjs(entry.date).isBetween(quarterStart, quarterEnd, null, "[]");
        });
      case "Annually":
        return data.filter((entry) => dayjs(entry.date).isSame(now, "year"));
      default:
        return data;
    }
  };

  // Get the filtered data
  const filteredData = filterDataByTimeRange(onboardingData, selectedTimeRange);

  // Aggregate data per category
  const categoryCounts = allCategories.reduce((acc, category) => {
    acc[category] = 0;
    return acc;
  }, {});

  filteredData.forEach((entry) => {
    categoryCounts[entry.category] += entry.count;
  });

  // Prepare data for chart
  const categories = allCategories; // X-axis categories
  const seriesData = categories.map((category) => categoryCounts[category]);

  const options = {
    chart: {
      type: "bar",
      height: 350,
      toolbar: { show: false },
    },
    xaxis: {
      categories,
      title: { text: "SP's Count" },
    },
    yaxis: {
      title: { text: "Category" },
    },
    plotOptions: { bar: { horizontal: true } },
    colors: ["rgb(20, 184, 20)"],
  };

  return (
    <Box width="100%" p={2}>
      {/* Header with space between */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        {/* Title */}
        <Typography variant="h6" color="black">Service Provider</Typography>

        {/* Time Range Filter Button */}
        <IconButton onClick={(e) => setAnchorEl(e.currentTarget)}>
          <Icon
            icon="tabler:baseline-density-medium"
            width="30"
            height="30"
            style={{ cursor: "pointer", color: "black" }}
          />
        </IconButton>
      </Box>

      {/* Dropdown Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={() => setAnchorEl(null)}
      >
        {timeFilters.map((range) => (
          <MenuItem
            key={range}
            onClick={() => {
              setSelectedTimeRange(range);
              setAnchorEl(null);
            }}
          >
            {range}
          </MenuItem>
        ))}
      </Menu>

      {/* Chart */}
      <ApexChart options={options} series={[{ name: "Count", data: seriesData }]} type="bar" height={350} />
    </Box>
  );
};

export default ServiceProviderOverView;
