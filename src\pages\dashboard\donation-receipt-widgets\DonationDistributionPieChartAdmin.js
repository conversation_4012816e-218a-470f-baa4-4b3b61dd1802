import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  useTheme,
} from "@mui/material";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";

// Dynamically import ApexCharts
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

const DonationDistributionPieChartAdmin = () => {
  // Use theme from Material-UI context
  const theme = useTheme();

  // Define initial state for dynamic data
  const [title] = useState("Donation Distribution");
  const [subtitle] = useState("Breakdown by Donation Head");
  const [series, setSeries] = useState([]);
  const [labels, setLabels] = useState([]); // Initial labels for 5 donation heads
  const [currencySymbol] = useState("₹");
  const [loading, setLoading] = useState(false);

  // Placeholder for future GET endpoint integration
  useEffect(() => {
    // This will be replaced with actual API call
    const fetchData = async () => {
      setLoading(true);
      try {
        const url = `${getUrl(
          authConfig.dashboardStatisticsEndpoint + "/donation-distribution"
        )}`;

        const response = await axios({
          method: "get",
          url: url,
          headers: getAuthorizationHeaders(),
        });
        const data = response.data;

        // Extract series and labels
        const extractedSeries = data.map((item) => parseFloat(item.percentage));
        const extractedLabels = data.map((item) => item.donationHeadName);

        setSeries(extractedSeries);
        setLabels(extractedLabels);
      } catch (error) {
        console.error("Error fetching donation distribution data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Chart options
  const chartOptions = {
    chart: {
      type: "pie",
      toolbar: { show: true },
    },
    colors: [
      theme.palette.primary.main,
      theme.palette.success.main,
      theme.palette.warning.main,
      theme.palette.info.main,
      theme.palette.error.main,
    ],
    labels: labels,
    dataLabels: {
      enabled: true,
      formatter: function (val, opts) {
        return `${opts.w.globals.seriesNames[opts.seriesIndex]}: ${val.toFixed(
          1
        )}%`;
      },
      style: {
        fontSize: "10px",
        fontWeight: "bold",
      },
      dropShadow: {
        enabled: true,
        top: 1,
        left: 1,
        blur: 1,
        color: "#000",
        opacity: 0.45,
      },
    },
    legend: {
      position: "bottom",
      horizontalAlign: "center",
      fontSize: "12px",
      markers: {
        width: 12,
        height: 12,
      },
      itemMargin: {
        horizontal: 10,
        vertical: 5,
      },
    },
    tooltip: {
      theme: theme.palette.mode,
      y: {
        formatter: (value) => `${currencySymbol}${value.toLocaleString()}`,
        title: {
          formatter: (seriesName) => `${seriesName}:`,
        },
      },
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: {
            width: 200,
          },
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  return (
    <Paper elevation={3} sx={{ p: 3, height: "100%" }}>
      <Typography variant="h5" gutterBottom>
        {title}
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {subtitle}
      </Typography>
      <Box
        sx={{
          mt: 2,
          minHeight: 350,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {loading ? (
          <CircularProgress />
        ) : series.length > 0 && labels.length > 0 ? (
          <Chart
            options={chartOptions}
            series={series}
            type="pie"
            height={350}
          />
        ) : (
          <Typography color="error">No data available</Typography>
        )}
      </Box>
    </Paper>
  );
};

export default DonationDistributionPieChartAdmin;
