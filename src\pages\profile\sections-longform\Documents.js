import { useContext, useEffect, useRef, useState } from "react";

// ** MUI Imports
import CheckCircleOutlineIcon from "@mui/icons-material/CheckCircleOutline";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import TextField from "@mui/material/TextField";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
// ** Third Party Imports
import {
  Autocomplete,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from "@mui/material";
import axios from "axios";
import { Controller, useForm } from "react-hook-form";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import authConfig from "src/configs/auth";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import SocietyMembers from "./SocietyMembers";
import { AuthContext } from "src/context/AuthContext";
import UploadFile2 from "./UploadFile2";
import UploadFile from "./UploadFile";
import UploadFile3 from "./UploadFile3";
import ViewDocumentByLocation from "src/@core/components/custom-components/ViewDocumentByLocation";

const Documents = ({
  formData,
  onUpdate,
  selectedFiles,
  setSelectedFiles,
  selectedFiles2,
  setSelectedFiles2,
  selectedFiles3,
  setSelectedFiles3,
}) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.documents || {}, // Initialize form fields with existing data
  });

  // Watch all fields for changes
  const watchedFields = watch();

  // Update formData on any change
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    const hasWatchedFieldsChanged =
      JSON.stringify(previousWatchedFields.current) !==
      JSON.stringify(watchedFields);
    if (hasWatchedFieldsChanged) {
      onUpdate({
        ...watchedFields,
      });
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, onUpdate]);
  const [document, setDocument] = useState("");
  const [document2, setDocument2] = useState("");
  const [document3, setDocument3] = useState("");

  useEffect(() => {
    setDocument(formData?.documents?.logoFileLocation);
    setDocument2(formData?.documents?.g80CertificationFileLocationPageOne);
    setDocument3(formData?.documents?.g80CertificationFileLocationPageTwo);
  }, [formData]);

  useEffect(() => {
    setDocument3(selectedFiles3[0]?.location);
  }, [selectedFiles3]);

  useEffect(() => {
    setDocument2(selectedFiles2[0]?.location);
  }, [selectedFiles2]);

  useEffect(() => {
    setDocument(selectedFiles[0]?.location);
  }, [selectedFiles]);

  const [updatedFile, setUpdatedFile] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const handleDialogClose = () => {
    setSelectedDocument(null);
  };

  return (
    <Box>
      <>
        <Grid
          sx={{
            backgroundColor: "#d6d6f5",
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
            mt: 2,
          }}
        >
          <Typography variant="h6" fontWeight={"bold"} sx={{ mt: 0, ml: 2 }}>
            Upload Logo (jpg, png, Max 5MB)
          </Typography>
        </Grid>
        <Grid
          container
          spacing={3}
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            marginTop: "2rem",
          }}
        >
          <Grid container spacing={2} mt={0.3}>
            <Grid item xs={12}>
              <UploadFile
                selectedDocument={document}
                selectedFiles={selectedFiles}
                setSelectedFiles={setSelectedFiles}
                setUpdatedFile={setUpdatedFile}
              />
            </Grid>
            <Grid item xs={12} sx={{ mt: 2 }}>
              {document && (
                <TableContainer
                  style={{ overflowX: "auto", maxHeight: "90px" }}
                >
                  <Table sx={{ width: "100%" }}>
                    <TableHead>
                      <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "12px",
                            height: "10px",
                            paddingTop: "1px",
                            paddingBottom: "1px",
                          }}
                        >
                          File Name
                        </TableCell>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "12px",
                            height: "10px",
                            paddingTop: "1px",
                            paddingBottom: "1px",
                          }}
                        >
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <Typography className="data-field">
                            {document && document?.split("/").pop()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => setSelectedDocument(document)}
                            color="error"
                          >
                            <Icon icon="iconamoon:eye" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
          </Grid>
        </Grid>
        <Grid
          sx={{
            backgroundColor: "#d6d6f5",
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" fontWeight={"bold"} sx={{ mt: 0, ml: 2 }}>
            Upload 80G Certification page 1 (jpg, png, Max 5MB)
          </Typography>
        </Grid>
        <Grid
          container
          spacing={3}
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            marginTop: "2rem",
          }}
        >
          <Grid container spacing={2} mt={0.3}>
            <Grid item xs={12}>
              <UploadFile2
                selectedDocument={document2}
                selectedFiles={selectedFiles2}
                setSelectedFiles={setSelectedFiles2}
                setUpdatedFile={setUpdatedFile}
              />
            </Grid>
            <Grid item xs={12}>
              {document2 && (
                <TableContainer
                  style={{ overflowX: "auto", maxHeight: "90px" }}
                >
                  <Table sx={{ width: "100%" }}>
                    <TableHead>
                      <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "12px",
                            height: "10px",
                            paddingTop: "1px",
                            paddingBottom: "1px",
                          }}
                        >
                          File Name
                        </TableCell>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "12px",
                            height: "10px",
                            paddingTop: "1px",
                            paddingBottom: "1px",
                          }}
                        >
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <Typography className="data-field">
                            {document2 && document2?.split("/").pop()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => setSelectedDocument(document2)}
                            color="error"
                          >
                            <Icon icon="iconamoon:eye" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
          </Grid>
        </Grid>
        <Grid
          sx={{
            backgroundColor: "#d6d6f5",
            paddingTop: 0,
            height: "36px",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Typography variant="h6" fontWeight={"bold"} sx={{ mt: 0, ml: 2 }}>
            Upload 80G Certification page 2 (jpg, png, Max 5MB)
          </Typography>
        </Grid>
        <Grid
          container
          spacing={3}
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            marginTop: "2rem",
          }}
        >
          <Grid
            container
            spacing={2}
            mt={0.3}
          >
            <Grid item xs={12}>
              <UploadFile3
                selectedDocument={document3}
                selectedFiles={selectedFiles3}
                setSelectedFiles={setSelectedFiles3}
                setUpdatedFile={setUpdatedFile}
              />
            </Grid>
            <Grid item xs={12}>
              {document3 && (
                <TableContainer
                  style={{ overflowX: "auto", maxHeight: "90px" }}
                >
                  <Table sx={{ width: "100%" }}>
                    <TableHead>
                      <TableRow style={{ backgroundColor: "#f2f7f2" }}>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "12px",
                            paddingTop: "1px",
                            paddingBottom: "1px",
                          }}
                        >
                          File Name
                        </TableCell>
                        <TableCell
                          style={{
                            fontWeight: "bold",
                            fontSize: "12px",
                            paddingTop: "1px",
                            paddingBottom: "1px",
                          }}
                        >
                          Actions
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      <TableRow>
                        <TableCell>
                          <Typography className="data-field">
                            {document3?.split("/").pop()}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => setSelectedDocument(document3)}
                            color="error"
                          >
                            <Icon icon="iconamoon:eye" />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Grid>
          </Grid>
        </Grid>
      </>

      <ViewDocumentByLocation
        location={selectedDocument}
        setSelectedLocation={setSelectedDocument}
        onClose={handleDialogClose}
      />
    </Box>
  );
};

export default Documents;
