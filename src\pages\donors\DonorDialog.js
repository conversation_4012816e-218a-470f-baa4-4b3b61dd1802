import { useContext, useEffect, useState } from "react";

// ** Custom Components Imports

// ** Demo Components Imports
import { useTheme } from "@emotion/react";

// ** Styled Component

import { AuthContext } from "src/context/AuthContext";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  TextField,
  MenuItem,
} from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { useAuth } from "src/hooks/useAuth";
import MultiSelectAutoCompleteCustom from "src/@core/components/custom-components/MultiSelectAutoCompleteCustom";
import authConfig from "src/configs/auth";
const DonorDialog = ({
  open,
  onClose,
  formData,
  fetchDonors,
  page,
  pageSize,
  searchKeyword,
  tenantsList,
  tagsList,
  setValuesUpdate,
}) => {
  const theme = useTheme();
  const {
    handleSubmit,
    setValue,
    control,
    reset,
    formState: { errors },
  } = useForm();
  const { user } = useContext(AuthContext);
  const [saveLoading, setSaveLoading] = useState(false);
  const auth = useAuth();
  const [tenantId, setTenantId] = useState("");
  const [openDialogContent, setOpenDialogContent] = useState(false);
  const [dialogMessage, setDialogMessage] = useState("");
  // Add this state for selected tags
  const [selectedTags, setSelectedTags] = useState([]);



  const panRegex = /^[A-Z]{3}[PCHFATBLJG][A-Z][0-9]{4}[A-Z]$/;
  const validFourthChars = ["P", "C", "H", "F", "A", "T", "B", "L", "J", "G"];
  // Success/Failure handlers
  const handleClose = () => setOpenDialogContent(false);

  const handleSuccess = () => {
    setDialogMessage(`<div><h3> Donor Added Successfully</h3></div>`);
    setOpenDialogContent(true);
  };

  const handleFailure = (err, action = "Add") => {
    let message;
    if (err?.response?.status === 400) {
      message = `<div><h3>Donor already exists!</h3></div>`;
    } else {
      message = `<div><h3>Failed to ${action} Donor. Please try again later.</h3></div>`;
    }
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleSuccessUpdate = () => {
    setDialogMessage(`<div><h3> Donor Updated Successfully</h3></div>`);
    setOpenDialogContent(true);
  };

  const handleFailureUpdate = (err) => {
    let message;
    if (err?.response?.status === 400) {
      message = `<div><h3>Donor already exists!</h3></div>`;
    } else {
      message = `<div><h3>Failed to update Donor. Please try again later.</h3></div>`;
    }
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  async function submit(data) {
    setSaveLoading(true);

    let fields;
    if (user?.organisationCategory === "SUPER_ADMIN") {
      fields = {
        name: data?.name,
        email: data?.email,
        contactNumber: data?.contactNumber,
        panNo: data?.panNo,
        tenantOrgId: tenantId,
      };
    } else {
      fields = {
        name: data?.name,
        email: data?.email,
        contactNumber: data?.contactNumber,
        panNo: data?.panNo,
        tenantOrgId: user?.orgId,
      };
    }

    try {
      await auth.postDonor(fields, handleFailure, handleSuccess);
    } catch (error) {
      handleFailure(error, "Add");
    }
    setSaveLoading(false);
    fetchDonors(page, pageSize, searchKeyword);
    handleCloseDialog();
  }

  async function update(data) {
    setSaveLoading(true);

    const fields = {
        name: data.name,
        email: data.email,
        contactNumber: data.contactNumber,
        panNo: data.panNo,
        tenantOrgId:user?.organisationCategory === "SUPER_ADMIN" ? tenantId : user?.orgId,
        donorMetaData: {
          state: data?.state,
          pinCode: data?.pinCode,
          address: data.address,
          tags: selectedTags?.map(tag => tag.value),
        }, 
      };
    

    try {
      await auth.patchDonor(
        fields,
        formData?.id,
        handleFailureUpdate,
        handleSuccessUpdate
      );
    } catch (error) {
      handleFailureUpdate(error, "Update");
    }
    setSaveLoading(false);
    fetchDonors(page, pageSize, searchKeyword);
    handleCloseDialog();
  }

  const handleCloseDialog = () => {
    onClose();
    reset();
  };

  // Populate form on edit
  useEffect(() => {
    setValue("name", formData?.name || "");
    setValue("email", formData?.email || "");
    setValue("contactNumber", formData?.contactNumber || "");
    setValue("panNo", formData?.panNo || "");
    setTenantId(formData?.tenantOrgId || "");
    setValue("state", formData?.donorMetaData?.state || "");
    setValue("address", formData?.donorMetaData?.address || "");
    setValue("pinCode", formData?.donorMetaData?.pinCode || "");
    setSelectedTags(tagsList?.filter(tag =>
      formData?.donorMetaData?.tags?.includes(tag.value)
    ));

  }, [formData, setValue]);

  return (
    <>
      <Dialog open={open} onClose={handleCloseDialog} maxWidth="xs">
        <DialogTitle
          sx={{
            position: "relative",
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: "flex",
            alignItems: "center",
            justifyContent: { xs: "start" },
            fontSize: { xs: 19, md: 20 },
            height: "50px",
          }}
          textAlign={"center"}
        >
          <Box
            sx={{
              fontSize: { xs: 14, sm: 15, md: 17, lg: 16 },
              fontWeight: 600,
              ml: { xs: 3, xl: 3 },
            }}
          >
            {!formData || Object.keys(formData).length === 0
              ? "Add Donor"
              : "Update Donor"}
          </Box>
          <Box
            sx={{
              position: "absolute",
              top: "9px",
              right: "10px",
              mr: { xs: 5.5, sm: 5.5, md: 5.5, lg: 5.5, xl: 5.5 },
            }}
          >
            <IconButton
              size="small"
              onClick={handleCloseDialog}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#9a9ae5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            position: "relative",
            p: (theme) => `${theme.spacing(6, 8)} !important`,
          }}
        >
          <Grid container spacing={2}>
            {user?.organisationCategory === "SUPER_ADMIN" && (
              <Grid item xs={12}>
                <FormControl fullWidth error={Boolean(errors.tenantName)}>
                  <Controller
                    name="tenantName"
                    control={control}
                    rules={{
                      required: tenantId ? false : "NGO Name is required",
                    }}
                    render={({ field }) => (
                      <SelectAutoComplete
                        id="tenantName"
                        label="NGO Name"
                        nameArray={tenantsList}
                        value={tenantId}
                        onChange={(event) => {
                          field.onChange(event.target?.value);
                          setTenantId(event.target?.value);
                        }}
                      />
                    )}
                  />
                  {errors.tenantName && (
                    <FormHelperText sx={{ color: "error.main" }}>
                      {errors.tenantName.message}
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="name"
                  control={control}
                  rules={{ required: "Name is required" }}
                  render={({ field }) => (
                    <NameTextField
                      {...field}
                      size="small"
                      label="Name"
                      InputLabelProps={{ shrink: true }}
                      placeholder="Enter Name"
                      error={Boolean(errors.name)}
                      helperText={errors.name?.message}
                      aria-describedby="name"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="email"
                  control={control}
                  rules={{
                    required: "Email is required",
                    pattern: {
                      value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                      message: "Invalid email address",
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      size="small"
                      label="Email"
                      InputLabelProps={{ shrink: true }}
                      placeholder="Enter Email"
                      error={Boolean(errors.email)}
                      helperText={errors.email?.message}
                      aria-describedby="email"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="contactNumber"
                  control={control}
                  rules={{
                    required: "Mobile Number is required",
                    minLength: {
                      value: 10,
                      message: "Enter valid mobile number",
                    },
                  }}
                  render={({ field }) => (
                    <MobileNumberValidation
                      {...field}
                      size="small"
                      label="Mobile Number"
                      InputLabelProps={{ shrink: true }}
                      placeholder="Enter Mobile Number"
                      error={Boolean(errors.contactNumber)}
                      helperText={errors.contactNumber?.message}
                      aria-describedby="contactNumber"
                    />
                  )}
                />
              </FormControl>
            </Grid>
            {/* <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="donorType"
                  control={control}
                  rules={{ required: "Donor Type is required" }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      select
                      size="small"
                      label="Donor Type"
                      InputLabelProps={{ shrink: true }}
                      error={Boolean(errors.donorType)}
                      helperText={errors.donorType?.message}
                      aria-describedby="donorType"
                    >
                      <MenuItem value="Individual">Individual</MenuItem>
                      <MenuItem value="Entity">Entity</MenuItem>
                    </TextField>
                  )}
                />
              </FormControl>
            </Grid> */}
            <Grid item xs={12}>
              <FormControl fullWidth>
                <Controller
                  name="panNo"
                  control={control}
                  rules={{
                    required: false,
                    validate: (value) => {
                      if (!value) return true;
                      if (!panRegex.test(value)) {
                        return "Invalid PAN format. Must be 5 letters, 4 digits, 1 letter (e.g., **********)";
                      }
                      if (!validFourthChars.includes(value.charAt(3))) {
                        return "Invalid 4th character. Must be one of: P, C, H, F, A, T, B, L, J, G";
                      }
                      return true;
                    },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      onChange={(e) =>
                        field.onChange(e.target.value.toUpperCase())
                      }
                      size="small"
                      label="PAN Number"
                      placeholder="Enter your PAN Number"
                      error={Boolean(errors.panNo)}
                      helperText={errors.panNo?.message}
                      inputProps={{ maxLength: 10 }}
                    />
                  )}
                />
              </FormControl>
            </Grid>

            {!formData || Object.keys(formData).length === 0 ? null : (
              <>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="state"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="State"
                          fullWidth
                          size="small"
                          error={Boolean(errors.state)}
                          helperText={errors.state?.message}
                          placeholder="Enter your state"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="pinCode"
                      control={control}
                      rules={{
                        required: false,
                        pattern: {
                          value: /^[1-9][0-9]{5}$/,
                          message: "Please enter a valid 6-digit PIN code",
                        },
                        validate: {
                          validLength: (value) =>
                            !value ||
                            value.length === 6 ||
                            "PIN code must be 6 digits",
                          onlyNumbers: (value) =>
                            !value ||
                            /^\d+$/.test(value) ||
                            "PIN code should contain only numbers",
                          validFirstDigit: (value) =>
                            !value ||
                            value[0] !== "0" ||
                            "PIN code cannot start with 0",
                        },
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="PIN Code"
                          fullWidth
                          size="small"
                          error={Boolean(errors.pinCode)}
                          helperText={errors.pinCode?.message}
                          placeholder="Enter your PIN code"
                          inputProps={{
                            maxLength: 6,
                            inputMode: "numeric",
                            pattern: "[0-9]*",
                          }}
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="address"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Address"
                          fullWidth
                          size="small"
                          multiline
                          rows={3}
                          error={Boolean(errors.address)}
                          helperText={errors.address?.message}
                          placeholder="Enter your complete address"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth error={Boolean(errors.tags)}>
                    <Controller
                      name="tags"
                      control={control}
                      rules={{ required: false }}
                      render={({ field }) => (
                        <MultiSelectAutoCompleteCustom
                          id="tags"
                          label="Tags"
                          nameArray={tagsList}
                          value={selectedTags}
                          listNameId={authConfig.tagsListNameId}
                          setValuesUpdate={setValuesUpdate}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                            setSelectedTags(e.target.value);
                          }}
                        />
                      )}
                    />
                    {errors.tags && (
                      <FormHelperText sx={{ color: "error.main" }}>
                        {errors.tags.message}
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "end",
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(2.5, 4)} !important`,
            height: "50px",
          }}
        >
          <Button
            display="flex"
            justifyContent="center"
            variant="outlined"
            color="primary"
            onClick={handleCloseDialog}
          >
            Cancel
          </Button>
          {!formData || Object.keys(formData).length === 0 ? (
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(submit)}
              sx={{ mr: { xs: 4, sm: 4, md: 4, lg: 4, xl: 4 } }}
            >
              {saveLoading ? (
                <CircularProgress color="inherit" size={22} />
              ) : (
                "Save"
              )}
            </Button>
          ) : (
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(update)}
              sx={{ mr: { xs: 4, sm: 4, md: 4, lg: 4, xl: 4 } }}
            >
              {saveLoading ? (
                <CircularProgress color="inherit" size={22} />
              ) : (
                "Update"
              )}
            </Button>
          )}
        </DialogActions>
      </Dialog>
      <Dialog
        open={openDialogContent}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleClose}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default DonorDialog;
