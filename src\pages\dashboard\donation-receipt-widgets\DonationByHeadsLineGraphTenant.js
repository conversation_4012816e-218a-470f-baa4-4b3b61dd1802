import React, { useState, useEffect, useContext } from 'react';
import dynamic from 'next/dynamic';
import { Box, Typography, Paper, CircularProgress, useTheme } from '@mui/material';
import axios from 'axios';
import { AuthContext } from 'src/context/AuthContext';
import { getAuthorizationHeaders, getUrl } from 'src/helpers/utils';
import authConfig from 'src/configs/auth';

const Chart = dynamic(() => import('react-apexcharts'), { ssr: false });

const DonationByHeadsLineGraphTenant = () => {
  const { user } = useContext(AuthContext);
  const theme = useTheme();
  const [title] = useState("Donations by Head (Monthly Trend)");
  const [series, setSeries] = useState([]);
  const [categories, setCategories] = useState([]);
  const [currencySymbol] = useState('₹');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const orgId = user?.orgId;
        const url = `${getUrl(authConfig.dashboardStatisticsEndpoint + "/donations-by-head")}`;
        const params = {};
        if (orgId) params.orgId = orgId;

        const response = await axios({
          method: "get",
          url: url,
          params: params,
          headers: getAuthorizationHeaders(),
        });

        const headData = response.data.data || [];
        setCategories(headData.map(item => item.month));

        const seriesData = headData.reduce((acc, item) => {
          Object.entries(item.amountsByHead).forEach(([head, amount]) => {
            const existingSeries = acc.find(s => s.name === head);
            if (existingSeries) {
              existingSeries.data.push(amount || 0);
            } else {
              acc.push({ name: head, data: [amount || 0] });
            }
          });
          return acc;
        }, []);

        setSeries(seriesData);
      } catch (error) {
        console.error('Error fetching donation data:', error);
        // fallback to empty categories (example: months) so X-axis shows
        setCategories(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']);
        setSeries([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const chartOptions = {
    chart: {
      type: 'line',
      toolbar: { show: false },
      zoom: { enabled: false },
      foreColor: theme.palette.text.secondary,
    },
    colors: [
      theme.palette.primary.main,
      theme.palette.success.main,
      theme.palette.warning.main,
      theme.palette.error.main,
      theme.palette.info.main
    ],
    dataLabels: { enabled: false },
    stroke: { curve: 'smooth', width: 2 },
    markers: { size: 4, strokeColors: '#fff', strokeWidth: 2, hover: { size: 6 } },
    xaxis: {
      categories: categories.length ? categories : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      axisBorder: { show: true, color: theme.palette.divider },
      axisTicks: { show: true },
      labels: { style: { fontSize: '12px' } }
    },
    yaxis: {
      title: {
        text: `Donations (${currencySymbol})`,
        style: { fontSize: '12px', fontWeight: 500 }
      },
      labels: {
        style: { fontSize: '12px' },
        formatter: (value) => `${Math.floor(value)}`
      }
    },
    grid: { borderColor: theme.palette.divider, strokeDashArray: 3 },
    legend: { position: 'top', horizontalAlign: 'right', floating: true, offsetY: -5, offsetX: -5 },
    tooltip: {
      theme: theme.palette.mode,
      y: {
        formatter: (value) => `${currencySymbol}${value.toLocaleString()}`,
        title: { formatter: (seriesName) => `${seriesName}:` }
      }
    },
    noData: {
      text: 'No donation data available',
      align: 'center',
      verticalAlign: 'middle',
      style: {
        color: theme.palette.text.secondary,
        fontSize: '14px'
      }
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, height: '100%' }}>
      <Typography variant="h6" sx={{ mb: 2 }}>{title}</Typography>
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Chart
          options={chartOptions}
          series={series.length ? series : [{ name: "Donations", data: [] }]}
          type="line"
          height={300}
        />
      )}
    </Paper>
  );
};

export default DonationByHeadsLineGraphTenant;
