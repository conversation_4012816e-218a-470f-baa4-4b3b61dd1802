import dynamic from 'next/dynamic';
import React from 'react';

// Dynamically import 'react-apexcharts' to avoid SSR issues
const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

// Generate data for each plan
const generatePlanData = (count, yrange) => {
  const series = [];
  for (let i = 0; i < count; i++) {
    const x = i + 1; // Ensuring unique time periods
    const y = Math.floor(Math.random() * (yrange.max - yrange.min + 1)) + yrange.min;
    const z = Math.floor(Math.random() * (1000 - 200 + 1)) + 200; // Simulating revenue or another metric

    series.push([x, y, z]);
  }
  return series;
};

const BubbleChart = () => {
  const state = {
    series: [
      {
        name: 'Silver',
        data: generatePlanData(12, { min: 100, max: 500 }),
      },
      {
        name: 'Gold',
        data: generatePlanData(12, { min: 150, max: 600 }),
      },
      {
        name: 'Platinum',
        data: generatePlanData(12, { min: 200, max: 700 }),
      },
      {
        name: 'Titanium',
        data: generatePlanData(12, { min: 250, max: 800 }),
      },
      {
        name: 'Ruby',
        data: generatePlanData(12, { min: 300, max: 900 }),
      },
      {
        name: 'Diamond',
        data: generatePlanData(12, { min: 350, max: 1000 }),
      },
    ],
    options: {
      chart: {
        height: 350,
        type: 'bubble',
      },
      colors: ['#808080', '#FFD700', '#E5E4E2', '#8A2BE2', '#E0115F', '#B9F2FF'], // Grey for Silver
      dataLabels: {
        enabled: false,
      },
      fill: {
        opacity: 0.8,
      },
      title: {
        text: 'Plans Distribution',
      },
      value: {
        fontSize: '14px',
      },
      xaxis: {
        categories: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
        title: {
          text: 'Time Periods (Months)',
        },
      },
      yaxis: {
        max: 1000,
        title: {
          text: 'Number of Users',
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return `${val} users`;
          },
        },
        z: {
          title: 'Revenue: $',
          formatter: function (val) {
            return `${val}`;
          },
        },
      },
    },
  };

  return (
    <div style={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="bubble" height={350} />
      </div>
    </div>
  );
};

export default BubbleChart;
