// ** React Imports
import { useEffect, useRef } from "react";

// ** MUI Imports
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports
import { Controller, useForm } from "react-hook-form";

const Requirements = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.requirements || {}, // Initialize form fields with existing data
  });

  // Watch all fields for changes
  const watchedFields = watch();

   // Track previous watched fields using useRef
    const previousWatchedFields = useRef();

    useEffect(() => {
      // Compare previous watched fields with current watched fields
      if (JSON.stringify(previousWatchedFields.current) !== JSON.stringify(watchedFields)) {
        onUpdate(watchedFields); // Send only the updated fields to the parent
        previousWatchedFields.current = watchedFields; // Update the ref with the latest value
      }
    }, [watchedFields, onUpdate]);

  return (
    <>
      <Grid
        container
        spacing={3}
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginTop: "2rem",
        }}
      >
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="requirements_ExtraArea"
              control={control}
              defaultValue={formData?.requirements_ExtraArea}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Extra Area"
                  type="text"
                  size="small"
                  placeholder="Additional area"
                  InputLabelProps={{
                    shrink: true,
                    style: { fontWeight: "bold" }, // Apply bold styling here
                  }}
                  error={Boolean(errors.requirements_ExtraArea)}
                  helperText={errors.requirements_ExtraArea?.message}
                  aria-describedby="validation-basic-requirements_ExtraArea"
                  onKeyPress={(event) => {
                    if (!/\d/.test(event.key)) {
                      event.preventDefault();
                    }
                  }}
                  inputProps={{
                    inputMode: "numeric", // only allow numeric input
                    maxLength: 10, // adjust the max length as needed
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="requirements_Rent"
              control={control}
              defaultValue={formData?.requirements_Rent}
              render={({ field }) => (
                <TextField
                  {...field}
                  type="text"
                  label="Rent"
                  size="small"
                  placeholder="rental amount"
                  InputLabelProps={{
                    shrink: true,
                    style: { fontWeight: "bold" }, // Apply bold styling here
                  }}
                  error={Boolean(errors.requirements_Rent)}
                  helperText={errors.requirements_Rent?.message}
                  aria-describedby="validation-basic-requirements_Rent"
                  onKeyPress={(event) => {
                    if (!/\d/.test(event.key)) {
                      event.preventDefault();
                    }
                  }}
                  inputProps={{
                    inputMode: "numeric", // only allow numeric input
                    maxLength: 10, // adjust the max length as needed
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="requirements_Corpus"
              control={control}
              defaultValue={formData?.requirements_Corpus}
              render={({ field }) => (
                <TextField
                  {...field}
                  type="text"
                  size="small"
                  label="Corpus"
                  placeholder="corpus amount"
                  InputLabelProps={{
                    shrink: true,
                    style: { fontWeight: "bold" }, // Apply bold styling here
                  }}
                  error={Boolean(errors.requirements_Corpus)}
                  helperText={errors.requirements_Corpus?.message}
                  aria-describedby="validation-basic-requirements_Corpus"
                  onKeyPress={(event) => {
                    if (!/\d/.test(event.key)) {
                      event.preventDefault();
                    }
                  }}
                  inputProps={{
                    inputMode: "numeric", // only allow numeric input
                    maxLength: 10, // adjust the max length as needed
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="notes"
              control={control}
              defaultValue={formData?.notes}
              render={({ field }) => (
                <TextField
                  rows={4}
                  multiline
                  {...field}
                  label="Notes"
                  InputLabelProps={{
                    shrink: true,
                    style: { fontWeight: "bold" }, // Apply bold styling here
                  }}
                  error={Boolean(errors.notes)}
                  aria-describedby="validation-basic-notes"
                  helperText={errors.notes?.message}
                  inputProps={{
                    title: "Enter any additional comments or notes here",
                  }}
                />
              )}
            />
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
          <FormControl fullWidth>
            <Controller
              name="leadGivenTo"
              control={control}
              defaultValue={formData?.leadGivenTo}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Lead Given To"
                  size="small"
                  InputLabelProps={{
                    shrink: true,
                    style: { fontWeight: "bold" }, // Apply bold styling here
                  }}
                  placeholder="Lead Given To"
                  error={Boolean(errors.leadGivenTo)}
                  helperText={errors.leadGivenTo?.message}
                  aria-describedby="validation-basic-leadGivenTo"
                />
              )}
            />
          </FormControl>
        </Grid>
      </Grid>
    </>
  );
};

export default Requirements;
