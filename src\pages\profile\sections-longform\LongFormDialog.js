import ArchitectureIcon from "@mui/icons-material/Architecture";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import HomeIcon from "@mui/icons-material/Home";
import MapIcon from "@mui/icons-material/Map";
import PreviewIcon from "@mui/icons-material/Preview";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
} from "@mui/material";
import axios from "axios";
import { useContext, useEffect, useState } from "react";
import Icon from "src/@core/components/icon";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import PreviewSection from "./LongFormPreview";
import ProfileDetails from "./ProfileDetails";
import MembersList from "./MembersList";
import Documents from "./Documents";
import { useTheme } from "@mui/material/styles";
import OrganisationDetails from "./OrganisationDetails";

const LongFormDialog = ({
  open,
  handleClose,
  currentRow,
  fetchTenants,
  page,
  pageSize,
}) => {
  const { user, patchCHSProfile } = useContext(AuthContext);

  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);
  const [newList, setNewList] = useState([]);
  const [existingList, setExistingList] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [selectedFiles2, setSelectedFiles2] = useState([]);
  const [selectedFiles3, setSelectedFiles3] = useState([]);

  const handleCloseDialog = () => {
    setDialogSuccess(false);
  };

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const [currentStep, setCurrentStep] = useState(0);
  const [formDataOne, setFormData] = useState({
    profileDetails: {
      name: "",
      email: "",
      mobileNumber: "",
    },
    organisationDetails: {
      trustName: "",
      orgEmail: "",
      registrationNo: "",
      panNo: "",
      g80RegistrationNo: "",
      state: "",
      address: "",
      pinCode: "",
    },
    documents: {
      logoFileLocation: "",
      g80CertificationFileLocationPageOne: "",
      g80CertificationFileLocationPageTwo: "",
    },
    members: {
      orgIndividualsList: [],
    },
  });

  // Handle updates for individual sections
  const handleFieldUpdate = (sectionId, updatedData) => {
    setFormData((prevData) => {
      return {
        ...prevData,
        [sectionId]: updatedData,
      };
    });
  };

  const sections = [
    {
      id: "profileDetails",
      title: "Basic Profile",
      component: ProfileDetails,
      icon: <HomeIcon />,
    },
    {
      id: "organisationDetails",
      title: "Trust Information",
      component: OrganisationDetails,
      icon: <MapIcon />,
    },
    {
      id: "documents",
      title: "Documents",
      component: Documents,
      icon: <ContactMailIcon />,
    },
    {
      id: "members",
      title: "Members",
      component: MembersList,
      icon: <ArchitectureIcon />,
    },
    {
      id: "preview",
      title: "Preview",
      component: PreviewSection,
      icon: <PreviewIcon />,
    },
  ];

  const handleStepChange = (index) => {
    setCurrentStep(index);
  };

  const handleNext = () => {
    if (currentStep < sections?.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleDialogClose = () => {
    setFormData({
      profileDetails: {
        name: "",
        email: "",
        mobileNumber: "",
      },
      organisationDetails: {
        trustName: "",
        orgEmail: "",
        registrationNo: "",
        panNo: "",
        g80RegistrationNo: "",
        state: "",
        address: "",
        pinCode: "",
      },
      documents: {
        logoFileLocation: "",
        g80CertificationFileLocationPageOne: "",
        g80CertificationFileLocationPageTwo: "",
      },
      members: {
        orgIndividualsList: [],
      },
    });
    setProfileData({});
    setCurrentStep(0);
    handleClose();
  };
  function extractNonNullFields(formData) {
    const result = {};

    // const member = {
    //   id: profileData?.chsProfileDTO?.userId,
    //   name: formData?.societyMemberInformation?.societyMemberName,
    //   contactNumber: formData?.societyMemberInformation?.societyMemberContactNumber,
    //   alternateNumber: formData?.societyMemberInformation?.alternateNumber,
    //   email: formData?.societyMemberInformation?.loginEmail,
    //   designation: formData?.societyMemberInformation?.societyMemberDesignation,
    //   fromDate: formData?.societyMemberInformation?.fromDate,
    //   toDate: formData?.societyMemberInformation?.toDate,
    // };

    // const mappings = {
    //   societyAddress: formData?.societyDetails?.societyAddress,
    //   societyCommitteeMemberInformationList: Array.isArray(
    //     formData?.societyMemberInformation?.societyCommitteeMemberInformationList
    //   )
    //     ? [...formData.societyMemberInformation.societyCommitteeMemberInformationList]
    //     : [],
    //   userId: profileData.chsProfileDTO.userId,
    //   name: formData?.societyDetails?.name,
    //   userId: profileData?.chsProfileDTO.userId,
    //   googleMapLocation: formData?.societyDetails?.googleMapLocation,
    //   enrolledDate: formData?.societyDetails?.enrolledDate,
    //   registeredFor: formData?.societyDetails?.registeredFor,
    //   requisition: formData?.societyDetails?.requisition?.toUpperCase(),
    //   readiness: formData?.societyDetails?.readiness?.toUpperCase(),
    //   roadWidth: formData?.societyDetails?.roadWidth,
    //   grossPlotArea: formData?.societyDetails?.grossPlotArea,
    //   authority: formData?.societyDetails?.authority,
    //   teamMember: formData?.societyDetails?.teamMember,
    //   plotCTSNo: formData?.societyDetails?.plotCTSNo,
    //   locationId: formData?.societyDetails?.locationId,
    //   zone: formData?.societyDetails?.zone,
    //   pinCode: formData?.societyDetails?.pinCode,
    //   societyMemberName: formData?.societyMemberInformation?.societyMemberName,
    //   societyMemberDesignation:
    //     formData?.societyMemberInformation?.societyMemberDesignation,
    //   societyMemberContactNumber:
    //     formData?.societyMemberInformation?.societyMemberContactNumber,
    //   loginEmail: formData?.societyMemberInformation?.loginEmail,
    //   alternateNumber: formData?.societyMemberInformation?.alternateNumber,
    //   fromDate: formData?.societyMemberInformation?.fromDate,
    //   toDate: formData?.societyMemberInformation?.toDate,
    //   bankName: formData?.businessInformation?.bankName,
    //   branch: formData?.businessInformation?.branch,
    //   accountNumber: formData?.businessInformation?.accountNumber,
    //   ifscCode: formData?.businessInformation?.ifscCode,
    //   gstNo: formData?.businessInformation?.gstNo,
    //   panNo: formData?.businessInformation?.panNo,
    //   stateName: formData?.businessInformation?.stateName,
    //   doYouHaveGstNo: formData?.businessInformation?.doYouHaveGstNo?.toUpperCase(),
    //   assignedTo: formData?.assignmentAndStatus?.assignedTo,
    //   leadPriority: formData?.assignmentAndStatus?.leadPriority,
    //   leadStatus: formData?.assignmentAndStatus?.leadStatus,
    //   remarks: formData?.assignmentAndStatus?.remarks,
    //   curatedBy:formData?.assignmentAndStatus?.curatedBy,
    //   curatedOn:formData?.assignmentAndStatus?.curatedOn,
    //   builtUpAreaResidential: formData?.landDetails?.builtUpAreaResidential,
    //   builtUpAreaCommercial: formData?.landDetails?.builtUpAreaCommercial,
    //   noOfResidence: formData?.landDetails?.noOfResidence,
    //   noOfCommercial: formData?.landDetails?.noOfCommercial,
    //   buildingAge: formData?.fsi?.buildingAge,
    //   fsiConsumedFsi: formData?.fsi?.fsiConsumedFsi,
    //   fsi_AvailableFsi: formData?.fsi?.fsi_AvailableFsi,
    //   fsi_PermissibleFsi: formData?.fsi?.fsi_PermissibleFsi,
    //   heightRestriction: formData?.fsi?.heightRestriction,
    //   scheme: formData?.fsi?.scheme,
    //   dpRestrictions: formData?.fsi?.dpRestrictions,
    //   litigationsOrEncroachment: formData?.fsi?.litigationsOrEncroachment,
    //   requirements_ExtraArea: formData?.requirements?.requirements_ExtraArea,
    //   requirements_Rent: formData?.requirements?.requirements_Rent,
    //   requirements_Corpus: formData?.requirements?.requirements_Corpus,
    //   notes: formData?.requirements?.notes,
    //   leadGivenTo: formData?.requirements?.leadGivenTo,
    //   referenceType:formData?.reference?.referenceType,
    //   referralName: formData?.reference?.referralName,
    //   referralNumber: formData?.reference?.referralNumber,
    //   referralEmail:formData?.reference?.referralEmail,
    //   referralCompanyName:formData?.reference?.referralCompanyName
    // };

    // mappings.societyCommitteeMemberInformationList.push(member);

    for (const [key, value] of Object.entries(mappings)) {
      if (value) {
        result[key] = value;
      }
    }

    return result;
  }

  async function handleSaveProgress() {
    const mappings = {
      name: formDataOne?.profileDetails?.name,
      mobileNumber: formDataOne?.profileDetails?.mobileNumber,
      email: formDataOne?.profileDetails?.email,
      trustName: formDataOne?.organisationDetails?.trustName,
      orgEmail: formDataOne?.organisationDetails?.orgEmail,
      address: formDataOne?.organisationDetails?.address,
      registrationNo: formDataOne?.organisationDetails?.registrationNo,
      panNo: formDataOne?.organisationDetails?.panNo,
      g80RegistrationNo: formDataOne?.organisationDetails?.g80RegistrationNo,
      state: formDataOne?.organisationDetails?.state,
      pinCode: formDataOne?.organisationDetails?.pinCode,
      addUnVerifiedIndividualsList:newList,
      editOrgIndividuals:existingList
    };

    const formData = new FormData();

    
    formData.append(
      "patchBasicProfileDTO",
      JSON.stringify(mappings)
    );
    formData.append("file", selectedFiles[0]);
    formData.append("file80GOne",selectedFiles2[0]);
    formData.append("file80GTwo",selectedFiles3[0]);

    let orgId = currentRow?.orgId;

    await patchCHSProfile(
      orgId,
      formData,
      () => {
        const message = `NGO Profile Progress Saved Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to save the progress of NGO Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );
    fetchTenants(page, pageSize);
    setSelectedFiles([]);
    setSelectedFiles2([]);
    setSelectedFiles3([]);
    handleDialogClose();
  }

  async function handleSubmit() {
    const mappings = {
      name: formDataOne?.profileDetails?.name,
      mobileNumber: formDataOne?.profileDetails?.mobileNumber,
      email: formDataOne?.profileDetails?.email,
      trustName: formDataOne?.organisationDetails?.trustName,
      orgEmail: formDataOne?.organisationDetails?.orgEmail,
      address: formDataOne?.organisationDetails?.address,
      registrationNo: formDataOne?.organisationDetails?.registrationNo,
      panNo: formDataOne?.organisationDetails?.panNo,
      g80RegistrationNo: formDataOne?.organisationDetails?.g80RegistrationNo,
      state: formDataOne?.organisationDetails?.state,
      pinCode: formDataOne?.organisationDetails?.pinCode,
      addUnVerifiedIndividualsList:newList,
      editOrgIndividuals:existingList
    };

    const formData = new FormData();

    
    formData.append(
      "patchBasicProfileDTO",
      JSON.stringify(mappings)
    );
    formData.append("file", selectedFiles[0]);
    formData.append("file80GOne",selectedFiles2[0]);
    formData.append("file80GTwo",selectedFiles3[0]);
  
    

    let orgId = currentRow?.orgId;

    await patchCHSProfile(
      orgId,
      formData,
      () => {
        const message = `NGO Profile Updated Successfully`;
        setDialogMessage(message);
        setDialogSuccess(true);
      },
      () => {
        const message = `Failed to update NGO Profile`;
        setDialogMessage(message);
        setDialogSuccess(true);
      }
    );
    fetchTenants(page, pageSize);
    setSelectedFiles([]);
    setSelectedFiles2([]);
    setSelectedFiles3([]);
    handleDialogClose();
  }

  const [profileData, setProfileData] = useState({});

  useEffect(() => {
    if (!currentRow?.orgId) {
      // If currentRow is null or undefined, do not make the API call
      return;
    }
    const url =
      getUrl(authConfig.organisationsEndpoint) +
      "/org-basic-profile/" +
      currentRow?.orgId;

    axios({
      method: "get",
      url: url,
      headers: getAuthorizationHeaders(),
    })
      .then((res) => {
        setProfileData(res.data);
      })
      .catch((err) => console.log("CHS Data error", err));
  }, [currentRow]);

  useEffect(() => {
    setFormData({
      profileDetails: {
        name: profileData?.name || "",
        email: profileData?.email || "",
        mobileNumber: profileData?.mobileNumber || "",
      },
      organisationDetails: {
        trustName: profileData?.trustName || "",
        orgEmail: profileData?.orgEmail || "",
        registrationNo: profileData?.registrationNo || "",
        panNo: profileData?.panNo || "",
        g80RegistrationNo: profileData?.g80RegistrationNo || "",
        state: profileData?.state || "",
        address: profileData?.address || "",
        pinCode: profileData?.pinCode || "",
      },
      documents: {
        logoFileLocation: profileData?.logoFileLocation || "",
        g80CertificationFileLocationPageOne:
          profileData?.g80CertificationFileLocationPageOne || "",
        g80CertificationFileLocationPageTwo:
          profileData?.g80CertificationFileLocationPageTwo || "",
      },
      members: {
        orgIndividualsList: profileData?.orgIndividualsList || [],
      },
    });
  }, [profileData]);

  const renderSection = () => {
    const ActiveComponent = sections[currentStep].component;
    if (!ActiveComponent) return null;
    const sectionId = sections[currentStep].id;

    return (
      <ActiveComponent
        formData={formDataOne} // Pass the specific section's data
        onUpdate={(updatedData) => handleFieldUpdate(sectionId, updatedData)} // Update the specific section's data
        setExistingList={setExistingList}
        setNewList={setNewList}
        newList={newList}
        selectedFiles={selectedFiles}
        setSelectedFiles={setSelectedFiles}
        selectedFiles2={selectedFiles2}
        setSelectedFiles2={setSelectedFiles2}
        selectedFiles3={selectedFiles3}
        setSelectedFiles3={setSelectedFiles3}
        tenantRowData={currentRow}
        
      />
    );
  };

  return (
    <>
      <Dialog fullScreen open={open} onClose={handleDialogClose}>
        <DialogTitle
          sx={{
            p: 2,
            borderBottom: 1,
            borderColor: "divider",
            display: "flex",
            justifyContent: "space-between",
          }}
        >
          <Typography variant="h6">NGO Profile</Typography>
          <IconButton
            size="small"
            onClick={handleDialogClose}
            sx={{
              borderRadius: 1,
              color: "common.white",
              backgroundColor: "primary.main",
              "&:hover": {
                backgroundColor: "#d6d6f5",
                transition: "background 0.5s ease, transform 0.5s ease",
              },
            }}
          >
            <Icon icon="tabler:x" fontSize="1rem" />
          </IconButton>
        </DialogTitle>
        <DialogContent
          sx={{
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            height: isMobile ? "auto" : "calc(100% - 100px)",
            overflow: "hidden",
          }}
        >
          {/* Sidebar */}
          <Box
            sx={{
              width: isMobile ? "100%" : 250, // Full width for mobile
              borderRight: isMobile ? "none" : 1,
              borderBottom: isMobile ? 1 : "none", // Bottom border in mobile
              borderColor: "divider",
              overflowY: "auto",
            }}
          >
            <List
              sx={{
                display: "flex",
                flexDirection: isMobile ? "row" : "column", // Horizontal list in mobile
                overflowX: isMobile ? "auto" : "hidden", // Horizontal scroll for mobile
                p: isMobile ? 1 : 0,
                gap: isMobile ? 1 : 0,
              }}
            >
              {sections?.map((section, index) => (
                <ListItem
                  button
                  key={section.id}
                  selected={currentStep === index}
                  onClick={() => handleStepChange(index)}
                  sx={{
                    backgroundColor:
                      currentStep === index ? "#d6d6f5" : "inherit",
                    color: currentStep === index ? "#ffffff" : "inherit",
                    "&:hover": {
                      backgroundColor:
                        currentStep === index
                          ? "#d6d6f5"
                          : "rgba(0, 0, 0, 0.04)",
                      color: currentStep === index ? "#ffffff" : "inherit",
                    },
                  }}
                >
                  <ListItemIcon
                    sx={{
                      color: currentStep === index ? "#FFFFFF" : "#757575",
                      backgroundColor:
                        currentStep === index ? "#1f1f7a" : "#F5F5F5",
                      borderRadius: "4px",
                      width: "30px",
                      height: "30px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginRight: 2,
                      transition:
                        "background-color 0.3s ease, color 0.3s ease, transform 0.3s ease",
                      transform:
                        currentStep === index ? "scale(1.1)" : "scale(1)",
                    }}
                  >
                    {section.icon}
                  </ListItemIcon>

                  <ListItemText
                    primary={
                      <Typography
                        variant="body1"
                        fontWeight={currentStep === index ? "bold" : "normal"}
                      >
                        {section.title}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>

          {/* Main Content Area */}
          <Box
            sx={{
              flex: 1,
              display: "flex",
              flexDirection: "column",
              p: 3,
              overflowY: "auto",
            }}
          >
            {/* Section Content */}
            <Box sx={{ flex: 1 }}>{renderSection()}</Box>
          </Box>
        </DialogContent>

        {/* Navigation Buttons */}
        <DialogActions
          sx={{
            display: "flex",
            justifyContent: currentStep === 0 ? "flex-end" : "space-between",
            p: 2,
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
          }}
        >
          {currentStep !== 0 && (
            <Button
              onClick={handlePrevious}
              variant="outlined"
              color="secondary"
            >
              Previous
            </Button>
          )}

          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {currentStep === sections?.length - 1 ? (
              <></>
            ) : (
              <>
                <Button color="primary" onClick={handleSaveProgress}>
                  Save Progress
                </Button>
              </>
            )}
            {currentStep === sections?.length - 1 ? (
              <Button
                onClick={handleSubmit}
                variant="contained"
                color="primary"
              >
                Submit
              </Button>
            ) : (
              <Button onClick={handleNext} variant="contained" color="primary">
                Next
              </Button>
            )}
          </Box>
        </DialogActions>
      </Dialog>
      <Dialog
        open={dialogSuccess}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseDialog}
              sx={{ margin: "auto", width: 100 }}
            >
              Ok
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default LongFormDialog;
