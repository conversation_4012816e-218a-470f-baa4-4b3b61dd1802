// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Box from '@mui/material/Box'
import Badge from '@mui/material/Badge'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Third Party Components
import clsx from 'clsx'
import { useKeenSlider } from 'keen-slider/react'
import TestimonialCard from '../ui/testimonials/TestimonialCard'
import TestimonialCard2 from '../ui/testimonials/TestimonialCard2'
import TestimonialCard3 from '../ui/testimonials/TestimonialCard3'
import TestimonialCard4 from '../ui/testimonials/TestimonialCard4'
import TestimonialCard5 from '../ui/testimonials/TestimonialCard5'

const SwiperAutoSwitch = ({ direction }) => {
  const [loaded, setLoaded] = useState(false)
  const [currentSlide, setCurrentSlide] = useState(0)

  // ** Hook
  const [ref, instanceRef] = useKeenSlider(
    {
      slides: {
        perView: 3,
        spacing: 25
      },
      loop: true,
      rtl: direction === 'rtl',
      slideChanged(slider) {
        setCurrentSlide(slider.track.details.rel)
      },
      created() {
        setLoaded(true)
      }
    },

    [
      slider => {
        let mouseOver = false
        let timeout

        const clearNextTimeout = () => {
          clearTimeout(timeout)
        }

        const nextTimeout = () => {
          clearTimeout(timeout)
          if (mouseOver) return
          timeout = setTimeout(() => {
            slider.next()
          }, 4000)
        }
        slider.on('created', () => {
          slider.container.addEventListener('mouseover', () => {
            mouseOver = true
            clearNextTimeout()
          })
          slider.container.addEventListener('mouseout', () => {
            mouseOver = false
            nextTimeout()
          })
          nextTimeout()
        })
        slider.on('dragStarted', clearNextTimeout)
        slider.on('animationEnded', nextTimeout)
        slider.on('updated', nextTimeout)
      }
    ]
  )

  return (
    <>
      <Box className='navigation-wrapper'>
        <Box ref={ref} className='keen-slider' sx={{ mb: 16 }}>
          <Box className='keen-slider__slide'>
            <TestimonialCard />
          </Box>
          <Box className='keen-slider__slide'>
            <TestimonialCard2 />
          </Box>
          <Box className='keen-slider__slide'>
            <TestimonialCard3 />
          </Box>
          <Box className='keen-slider__slide'>
            <TestimonialCard4 />
          </Box>
          <Box className='keen-slider__slide'>
            <TestimonialCard5 />
          </Box>
        </Box>
      </Box>
      {loaded && instanceRef.current && (
        <Box className='swiper-dots'>
          {[...Array(instanceRef.current.track.details.slides.length).keys()].map(idx => {
            return (
              <Badge
                key={idx}
                variant='dot'
                component='div'
                className={clsx({
                  active: currentSlide === idx
                })}
                onClick={() => {
                  instanceRef.current?.moveToIdx(idx)
                }}
              ></Badge>
            )
          })}
        </Box>
      )}
    </>
  )
}

export default SwiperAutoSwitch
