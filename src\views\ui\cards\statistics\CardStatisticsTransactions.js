// ** MUI Imports
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

const data = [
  {
    stats: '260+',
    title: 'Projects',
    color: 'primary',
    icon: 'tabler:chart-pie-2'
  },
  {
    color: 'info',
    stats: '30+',
    title: 'Developers',
    icon: 'tabler:users'
  },
  {
    color: 'error',
    stats: '250+',
    title: 'Professionals',
    icon: 'tabler:shopping-cart'
  },
  {
    stats: '10000+',
    color: 'success',
    title: 'Customers',
    icon: 'tabler:currency-dollar'
  }
]

const renderStats = () => {
  return data.map((sale, index) => (
    <>
      <Grid item xs={12} sm={6} md={3} key={index}>
        <Card>
          <CardContent
            key={index}
            sx={{ p: 5, gap: 3, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          >
            <CustomAvatar skin='light' color={sale.color} sx={{ mr: 10, width: 42, height: 42 }}>
              <Icon icon={sale.icon} />
            </CustomAvatar>
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
              <Typography variant='h5'>{sale.stats}</Typography>
              <Typography sx={{ color: '#888', fontSize: '16px' }}>{sale.title}</Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>
    </>
  ))
}

const CardStatisticsTransactions = () => {
  return (
    <Grid container spacing={6}>
      {renderStats()}
    </Grid>
  )
}

export default CardStatisticsTransactions
