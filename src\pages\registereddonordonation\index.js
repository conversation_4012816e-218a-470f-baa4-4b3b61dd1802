import React, { useState } from "react";
import BlankLayout from 'src/@core/layouts/BlankLayout';
import { useForm, Controller } from "react-hook-form";
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  FormControl,
  InputAdornment,
  MenuItem,
  TextField,
  Typography,
  Select,
  Snackbar,
  Alert,
  Stack,
  InputLabel,
  IconButton,
  InputLabel as MuiInputLabel
} from "@mui/material";
import { ArrowBack, VolunteerActivism } from "@mui/icons-material";

const mockUser = {
  username: "donoruser",
  password: "password123",
  name: "<PERSON><PERSON> <PERSON>",
  email: "<EMAIL>"
};

const mockNgos = [
  { value: "1", label: "Helping Hands Foundation" },
  { value: "2", label: "Green Earth Trust" }
];

const buttonStyle = {
  borderRadius: '8px',
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: 'none',
  height: 45,
  minWidth: 80
};

const RazorpayKey = 'rzp_test_zKgAOeC4Kqzl31';

const loadRazorpayScript = () =>
  new Promise((resolve) => {
    if (window.Razorpay) {
      resolve(true);
      return;
    }
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.onload = () => resolve(true);
    script.onerror = () => resolve(false);
    document.body.appendChild(script);
  });

const RegisteredDonorDonation = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [receipt, setReceipt] = useState(null);
  const [loading, setLoading] = useState(false);
  const [paymentError, setPaymentError] = useState(null);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [loginError, setLoginError] = useState("");
  const [openDialog, setOpenDialog] = useState(false); 
  // State to control the dialog
  const [isCustomNgo, setIsCustomNgo] = useState(false);


  const { control, handleSubmit, setValue, reset, formState: { errors } } = useForm({
    defaultValues: {
      username: "",
      password: "",
      ngo: "",
      amount: "",
      remarks: ""
    }
  });

  const handleSnackbarClose = () => setOpenSnackbar(false);

  // Simulate login
  const handleLogin = (data) => {
    setLoading(true);
    setTimeout(() => {
      if (data.username === mockUser.username && data.password === mockUser.password) {
        setIsLoggedIn(true);
        setValue("username", "");
        setValue("password", "");
        setActiveStep(1);
        setLoginError("");
      } else {
        setLoginError("Invalid username or password");
      }
      setLoading(false);
    }, 1000);
  };

    const handleBack = () => {
  setActiveStep((prev) => prev - 1); // Navigate to the previous step
};

  const handleCloseDialog = () => setOpenDialog(false); // Close the dialog
  const handleLogout = () => {
    setOpenDialog(false);
    setIsLoggedIn(false);
    setActiveStep(0);
  };
  const openRazorpay = async (data) => {
    const res = await loadRazorpayScript();
    if (!res) {
      alert('Razorpay SDK failed to load. Are you online?');
      setLoading(false);
      return;
    }

    const amountInPaise = Number(data.amount) * 100;

    const options = {
      key: RazorpayKey,
      amount: amountInPaise,
      currency: 'INR',
      name: 'Pure Heart Donation',
      description: `Donation to ${mockNgos.find(n => n.value === data.ngo)?.label || 'your NGO'}`,
      prefill: {
        name: mockUser.name,
        email: mockUser.email
      },
      handler: function (response) {
        const generatedReceipt = {
          ...data,
          donorName: mockUser.name,
          donorEmail: mockUser.email,
          ngo: mockNgos.find(n => n.value === data.ngo)?.label,
          receiptId: Math.floor(Math.random() * 1000000),
          date: new Date().toLocaleString(),
          paymentId: response.razorpay_payment_id
        };
        setReceipt(generatedReceipt);
        setActiveStep(2);
        setLoading(false);
        setOpenSnackbar(true);
      },
      modal: {
        ondismiss: function () {
          setLoading(false);
        }
      }
    };

    const paymentObject = new window.Razorpay(options);
    paymentObject.open();
  };

  const onSubmit = async (data) => {
    setPaymentError(null);
    setLoading(true);
    try {
      setTimeout(() => {
        openRazorpay(data);
      }, 400);
    } catch (err) {
      setLoading(false);
      setPaymentError('Something went wrong in payment initiation.');
    }
  };

  const restartDonation = () => {
    reset();
    setActiveStep(0);
    setReceipt(null);
    setPaymentError(null);
    setIsLoggedIn(false);
    setLoginError("");
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'linear-gradient(135deg, #e0e7ff 0%, #fff 100%)'
      }}>
      <Card sx={{
        minWidth: 340,
        maxWidth: 420,
        width: '100%',
        p: { xs: 1, sm: 3 },
        boxShadow: 6,
        borderRadius: 4,
        bgcolor: '#fff',
        position: 'relative'
      }}>
        <CardContent>
          <Stack spacing={2} alignItems="center" sx={{ mb: 2 }}>
            <VolunteerActivism sx={{ fontSize: 40, color: 'primary.main' }} />
            <Typography variant="h4" fontWeight="bold" color="primary" align="center" sx={{ letterSpacing: 1 }}>
              Donate
            </Typography>
            <Typography variant="subtitle2" color="text.secondary" align="center">
              Your generosity can change lives!
            </Typography>
          </Stack>

          {loading && (
            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 120 }}>
              <CircularProgress />
            </Box>
          )}

          {/* Step 1: Login */}
          {!loading && activeStep === 0 && !isLoggedIn && (
            <form onSubmit={handleSubmit(handleLogin)}>
              <Stack spacing={2}>
  <Controller
    name="username"
    control={control}
    rules={{ required: "Username is required" }}
    render={({ field }) => (
      <TextField
        {...field}
        label="Username"
        fullWidth
        size="small"
        error={!!errors.username}
        helperText={errors.username?.message}
      />
    )}
  />
  <Controller
    name="password"
    control={control}
    rules={{ required: "Password is required" }}
    render={({ field }) => (
      <TextField
        {...field}
        label="Password"
        type="password"
        fullWidth
        size="small"
        error={!!errors.password}
        helperText={errors.password?.message}
      />
    )}
  />
  {loginError && (
    <Typography color="error" align="center">
      {loginError}
    </Typography>
  )}
  <Box sx={{ display: "flex", gap: 16, mt: 2 }}>
    
    <Button
      variant="outlined"
      onClick={() => (window.location.href = "/")} // Navigate to home
      sx={{
        flex: 1, // Make both buttons the same size
        height: 36,
        fontSize: "0.875rem",
      }}
    >
      Return to Home
    </Button>
    <Button
      variant="contained"
      type="submit"
      sx={{
        flex: 1, // Make both buttons the same size
        height: 36,
        fontSize: "0.875rem",
      }}
    >
      Log In
    </Button>
  </Box>
</Stack>
            </form>
          )}

          {/* Step 2: Select NGO, Amount, and Remarks */}
          {!loading && activeStep === 1 && isLoggedIn && (
            <form onSubmit={handleSubmit(onSubmit)}>
              <Stack spacing={2}>
              

<Controller
  name="ngo"
  control={control}
  rules={{ required: "Please select an NGO" }}
  render={({ field }) => (
    <FormControl fullWidth error={!!errors.ngo} size="small">
      <InputLabel>Select NGO</InputLabel>
      <Select
        {...field}
        label="Select NGO"
        displayEmpty
        size="small"
        onChange={(e) => {
          field.onChange(e); // Update the form value
          setIsCustomNgo(e.target.value === "custom"); // Check if "Other" is selected
        }}
      >
        <MenuItem value="" disabled>Select NGO</MenuItem>
        {mockNgos.map((ngo) => (
          <MenuItem key={ngo.value} value={ngo.value}>{ngo.label}</MenuItem>
        ))}
        <MenuItem value="custom">Other</MenuItem>
      </Select>
      {errors.ngo && <Typography color="error" variant="caption">{errors.ngo.message}</Typography>}
    </FormControl>
  )}
/>

{isCustomNgo && (
  <Controller
    name="customNgo"
    control={control}
    rules={{ required: "Please specify the NGO name" }}
    render={({ field }) => (
      <TextField
        {...field}
        size="small"
        label="Custom NGO Name"
        fullWidth
        error={!!errors.customNgo}
        helperText={errors.customNgo?.message}
      />
    )}
  />
)}
                <Controller
                  name="amount"
                  control={control}
                  rules={{
                    required: "Amount is required",
                    min: { value: 1, message: "Amount must be at least ₹1" },
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Amount"
                      type="number"
                      fullWidth
                      size="small" // Make field small
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">₹</InputAdornment>
                        ),
                      }}
                      error={!!errors.amount}
                      helperText={errors.amount?.message}
                    />
                  )}
                />
                <Controller
                  name="remarks"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Remarks (optional)"
                      fullWidth
                      size="small" // Make field small
                    />
                  )}
                />
                
<Box sx={{ display: "flex", gap: 27, mt: 2 }}>
  <Button
    variant="outlined"
    onClick={() => {
      setActiveStep(0); // Navigate back to the login page
      setIsLoggedIn(false); // Ensure the user is logged out
    }}
    sx={{
      flex: 1, // Keep the "Back" button smaller
      height: 36, // Adjust height to make it smaller
      fontSize: "0.875rem", // Adjust font size
    }}
  >
    Back
  </Button>
  <Button
    variant="contained"
    type="submit"
    sx={{
      ...buttonStyle,
      flex: 3, // Increase the length of the "Complete Payment" button
      height: 36, // Adjust height to make it smaller
      fontSize: "0.875rem", // Adjust font size
    }}
  >
    Complete Payment
  </Button>
</Box>
              </Stack>
            </form>
          )}



 
        
          {/* Step 3: Receipt or Retry */}
          {!loading && activeStep === 2 && receipt && (
            <Box textAlign="center" sx={{ mt: 2 }}>
              <Typography variant="h5" color="success.main" sx={{ mb: 1, fontWeight: 700 }}>Thank you for your donation!</Typography>
              <Typography variant="subtitle1" sx={{ mb: 2, color: 'text.secondary' }}>Receipt ID: {receipt.receiptId}</Typography>
              <Stack spacing={0.5} sx={{ mb: 2 }}>
                <Typography>Date: <b>{receipt.date}</b></Typography>
                <Typography>Donor: <b>{receipt.donorName}</b></Typography>
                <Typography>Email: <b>{receipt.donorEmail}</b></Typography>
                <Typography>NGO: <b>{receipt.ngo}</b></Typography>
                <Typography>Amount: <b>₹{receipt.amount}</b></Typography>
                <Typography>Remarks: <b>{receipt.remarks || '-'}</b></Typography>
              </Stack>
              <Button sx={{ mt: 2 }} variant="outlined" onClick={restartDonation}>
                Make Another Donation
              </Button>
            </Box>
          )}
          
        </CardContent>
        <Snackbar open={openSnackbar} autoHideDuration={4000} onClose={handleSnackbarClose}>
          <Alert 
            onClose={handleSnackbarClose} 
            severity="success" 
            sx={{ width: '100%', color: '#fff', backgroundColor: 'green' }} // White text and green background
          >
            Receipt generated and sent successfully!
          </Alert>
        </Snackbar>
      </Card>
    </Box>
  );
};

RegisteredDonorDonation.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
RegisteredDonorDonation.guestGuard = true;

export default RegisteredDonorDonation;
