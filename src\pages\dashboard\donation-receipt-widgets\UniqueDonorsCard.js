import React from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Avatar,
  CircularProgress
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import GroupOutlinedIcon from '@mui/icons-material/GroupOutlined';

const UniqueDonorsCard = ({ value, loading = false }) => {
  const theme = useTheme();
  const cardColor = theme.palette.primary.main;


  return (
    <Card
      sx={{
        position: 'relative',
        overflow: 'visible',
        borderLeft: `5px solid ${cardColor}`,
        borderRadius: '8px',
      }}
      elevation={2}
    >
      <CardContent sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
        <Box>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 0.5 }}>
            Unique Donors
          </Typography>
          <Typography variant="h5" component="div" sx={{ fontWeight: 600 }}>
            {loading ? <CircularProgress size={20} /> : value !== null && value !== undefined ? value : '0'}
          </Typography>
        </Box>
        <Avatar
          sx={{
            bgcolor: `${cardColor}20`,
            color: cardColor,
            width: 40,
            height: 40,
          }}
        >
          <GroupOutlinedIcon />
        </Avatar>
      </CardContent>
    </Card>
  );
};

export default UniqueDonorsCard;
