import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  DialogContentText,
} from "@mui/material";

const ConfirmDeleteDialog = ({ open, onClose, onConfirm }) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      PaperProps={{
        sx: {
          p: (theme) => `${theme.spacing(2.5)} !important`,
          backgroundColor: (theme) => theme.palette.primary.background,
        },
      }}
    >
      <Box
        sx={{
          width: "100%",
          borderRadius: 1,
          textAlign: "center",
          border: (theme) => `1px solid ${theme.palette.divider}`,
          borderColor: "primary.main",
        }}
      >
        <DialogContent>
          <DialogContentText id="alert-dialog-description" color="primary.main">
            Are you sure you want to Delete an Event?
          </DialogContentText>
        </DialogContent>
        <DialogActions
          sx={{
            justifyContent: "center",
          }}
        >
          <Button
            onClick={onConfirm}
            style={{ margin: "0 10px auto", width: 100 }}
            variant="contained"
          >
            Yes
          </Button>
          <Button
            onClick={onClose}
            style={{ margin: "0 10px auto", width: 100 }}
          >
            No
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default ConfirmDeleteDialog;
