name: Docker Build and Push to DOCR (React Frontend)

on:
  workflow_call:
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN:
        required: true

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: [self-hosted, Linux]

    env:
      APP_NAME: donation-receipt-frontend
      REGISTRY: registry.digitalocean.com/chidhagni-doks-registry
    

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

      - name: Set Environment Configuration
        run: |
          BRANCH_NAME="${{ github.ref_name }}"
          
          echo "🚀 Building Docker image for React frontend"
          echo "📦 Branch: $BRANCH_NAME"
          
          # Set BUILD_ENV based on branch
          if [[ "$BRANCH_NAME" == "main" || "$BRANCH_NAME" == "master" ]]; then
            BUILD_ENV="production"
            echo "🔥 Production build with .env.production"
          else
            BUILD_ENV="development"
            echo "🧪 Development build with .env.development"
          fi
          
          # Always use latest tag
          IMAGE_TAG="latest"
          
          echo "BUILD_ENV=$BUILD_ENV" >> $GITHUB_ENV
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV
          echo "BRANCH_NAME=$BRANCH_NAME" >> $GITHUB_ENV
          echo "🔧 BUILD_ENV: $BUILD_ENV"
          echo "🏷️  IMAGE_TAG: $IMAGE_TAG"

      - name: Authenticate Docker with DOCR
        run: doctl registry login

      - name: Build Docker Image for React Frontend
        run: |
          echo "🔨 Building Docker image for React frontend"
          echo "🔧 BUILD_ENV: ${{ env.BUILD_ENV }}"
          echo "🌿 BRANCH: ${{ env.BRANCH_NAME }}"
          echo "🏷️  IMAGE_TAG: ${{ env.IMAGE_TAG }}"

          docker build \
            --build-arg BUILD_ENV=${{ env.BUILD_ENV }} \
            --label "branch=${{ env.BRANCH_NAME }}" \
            --label "environment=${{ env.BUILD_ENV }}" \
            --label "frontend=react" \
            --label "build-date=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" \
            --label "commit-sha=${{ github.sha }}" \
            -t ${{ env.APP_NAME }} .

      - name: Tag Docker Image
        run: |
          FULL_IMAGE_TAG="${{ env.REGISTRY }}/${{ env.APP_NAME }}:${{ env.IMAGE_TAG }}"
          
          docker tag ${{ env.APP_NAME }} $FULL_IMAGE_TAG
          
          echo "FULL_IMAGE_TAG=$FULL_IMAGE_TAG" >> $GITHUB_ENV

          echo "🏷️  Tagged image:"
          echo "   📦 Tag: $FULL_IMAGE_TAG"
          echo "🎯 Environment: ${{ env.BUILD_ENV }}"
          echo "⚛️  Donation Receipt frontend application"

      - name: Push Docker Image to DigitalOcean Registry
        run: |
          echo "📤 Pushing Docker image to DigitalOcean registry..."
          
          echo "🚀 Pushing latest tag..."
          docker push ${{ env.FULL_IMAGE_TAG }}

          echo "✅ Successfully pushed React frontend image!"
          echo "🌐 Image pushed:"
          echo "   📦 Tag: ${{ env.FULL_IMAGE_TAG }}"
          echo "🎯 Environment: ${{ env.BUILD_ENV }}"
          echo "⚛️  React frontend application"
          echo "🚀 Ready for deployment!"
