import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import SelectAutoComplete from "src/@core/components/custom-components/SelectAutoComplete";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import { AuthContext } from "src/context/AuthContext";
import MenuItem from "@mui/material/MenuItem";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    clearAllFilters,
    onApplyFilters,
    tenantsList,
  } = props;

  const {
    setValue,
    control,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const [tenantId, setTenantId] = useState("");
  const { user } = useContext(AuthContext);

  useEffect(() => {
    const filterMap = new Map(selectedFilters?.map((filter) => [filter.key, filter.value]));
    setValue("name", filterMap.get("name") || "");
    setValue("email", filterMap.get("email") || "");
    setValue("mobile", filterMap.get("mobile") || "");
    setValue("donorType", filterMap.get("donorType") || "");
    setValue("pan", filterMap.get("pan") || "");
    setTenantId(filterMap.get("orgId") || "");
  }, [selectedFilters, setValue]);

  const handleCancel = () => {
    reset();
    setTenantId("");
    setSearchingState(false);
    clearAllFilters();
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = (data) => {
    const filters = [];
    if (tenantId) {
      filters.push({ key: "orgId", label: "NGO Name", value: tenantId });
    }
    if (data?.name) {
      filters.push({ key: "name", label: "Name", value: data.name });
    }
    if (data?.email) {
      filters.push({ key: "email", label: "Email", value: data.email });
    }
    if (data?.mobile) {
      filters.push({ key: "mobile", label: "Mobile Number", value: data.mobile });
    }
    if (data?.donorType) {
      filters.push({ key: "donorType", label: "Donor Type", value: data.donorType });
    }
    if (data?.pan) {
      filters.push({ key: "pan", label: "PAN Number", value: data.pan });
    }

    onApplyFilters(filters);
    setSearchingState(true);
    toggle();
  };

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
        <Header
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            position: "relative",
          }}
        >
          <Typography variant="h5" sx={{ ml: 3 }}>
            Advanced Search
          </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px" }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#d6d6f5",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>

        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <form onSubmit={handleSubmit(handleApply)}>
              <Grid container spacing={3} alignItems="center">
                {user?.organisationCategory === "SUPER_ADMIN" && (
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <Controller
                        name="tenantName"
                        control={control}
                        render={({ field }) => (
                          <SelectAutoComplete
                            id="tenantName"
                            label="NGO Name"
                            nameArray={tenantsList}
                            value={tenantId}
                            onChange={(event) => {
                              field.onChange(event.target?.value);
                              setTenantId(event.target?.value);
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                )}
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="name"
                      control={control}
                      render={({ field }) => (
                        <NameTextField
                          {...field}
                          size="small"
                          label="Name"
                          InputLabelProps={{ shrink: true }}
                          placeholder="Enter Name"
                          error={Boolean(errors.name)}
                          helperText={errors.name?.message}
                          aria-describedby="name"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="email"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label="Email"
                          InputLabelProps={{ shrink: true }}
                          placeholder="Enter Email"
                          error={Boolean(errors.email)}
                          helperText={errors.email?.message}
                          aria-describedby="email"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="mobile"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label="Mobile Number"
                          InputLabelProps={{ shrink: true }}
                          placeholder="Enter Mobile Number"
                          error={Boolean(errors.mobile)}
                          helperText={errors.mobile?.message}
                          aria-describedby="mobile"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="donorType"
                      control={control}
                      render={({ field }) => (
                        <SelectAutoComplete
                          id="donorType"
                          label="Donor Type"
                          nameArray={[
                            { label: "Individual", value: "Individual" },
                            { label: "Entity", value: "Entity" },
                          ]}
                          value={field.value}
                          onChange={(event) => field.onChange(event.target?.value)}
                          error={Boolean(errors.donorType)}
                          helperText={errors.donorType?.message}
                          aria-describedby="donorType"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <Controller
                      name="pan"
                      control={control}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          size="small"
                          label="PAN Number"
                          InputLabelProps={{ shrink: true }}
                          placeholder="Enter PAN Number"
                          error={Boolean(errors.pan)}
                          helperText={errors.pan?.message}
                          aria-describedby="pan"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs={12} sx={{ display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={handleCancel}
                    sx={{ mr: 2 }}
                  >
                    Clear
                  </Button>
                  <Button variant="contained" color="primary" type="submit">
                    Apply
                  </Button>
                </Grid>
              </Grid>
            </form>
          </Box>
        </PerfectScrollbar>
      </Drawer>
    </>
  );
};

export default AdvancedSearch;
