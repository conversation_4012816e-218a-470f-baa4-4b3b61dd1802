// ** MUI Imports
import Card from '@mui/material/Card'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import Rating from '@mui/material/Rating'
import CardContent from '@mui/material/CardContent'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

const TestimonialCard4 = () => {
  return (
    <Card>
      <CardContent
        sx={{
          p: 5,
          gap: 3,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'start',
          justifyContent: 'center'
        }}
      >
        <Box sx={{ mb: 5, display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>
          <Rating readOnly value={5} name='read-only' sx={{ mr: 2 }} />
        </Box>
        <Typography sx={{ mb: 3, textAlign: 'left' }} variant='body2'>
          New way to promote your business that won’t cost you more money, maybe printing is one of the options you
          won’t resist. Printing is a widely use process in making printed materials.
        </Typography>
        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', alignItems: 'center' }}>
          <CustomAvatar skin='light' color='error' sx={{ mr: 5, width: 40, height: 40 }}>
            <Icon icon='tabler:chart-pie-2' />
          </CustomAvatar>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
            <Typography variant='h6'>Naren Pohadekar</Typography>
            <Typography variant='body2'>PMC</Typography>
          </Box>
        </Box>
      </CardContent>
    </Card>
  )
}

export default TestimonialCard4
