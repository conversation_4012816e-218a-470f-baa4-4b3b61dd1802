import { Card, CardContent, CardMedia, Typography } from '@mui/material'
import Link from 'next/link'
import { useRouter } from 'next/router'

const CardIconBox = props => {
  const { wordPressId,title,slug,imageId,link,resourceType } = props
  const router = useRouter()

  const handleClick = e => {
    e.preventDefault()
    const newWindow = window.open(link, '_blank', 'noopener,noreferrer')
    if (newWindow) newWindow.opener = null
  }

  return (
    <div>
      <Card
        onClick={handleClick}
        sx={{
          minWidth: {xs: '100%', sm: '13.5rem', xl: 'auto'},
          cursor: "pointer",
          border: "1px solid #ddd",
        }}
      >
        <CardMedia
          sx={{
            height: "8.35rem",
            backgroundSize: "100% 100%",
            backgroundPosition: "center",
          }}
          image={imageId}
        />
        <CardContent
          sx={{
            display: "flex",
            textAlign: "center",
            height: "100px",
            overflow: 'hidden',
            alignItems: "center",
            flexDirection: "column",
            p: (theme) => `${theme.spacing(2)} !important`,
          }}
        >
          <Typography
            variant="body2"
            sx={{
              mb: 0.8,
              mt: 0.8,
              textAlign: "center",
              cursor: "pointer",
              fontSize: "1.015rem",
              fontWeight: 500,
              "&:hover": {
                textDecoration: "none",
                color: "primary.main",
              },
            }}
          >
            {title}
          </Typography>
        </CardContent>
      </Card>
    </div>
  );
}

export default CardIconBox
