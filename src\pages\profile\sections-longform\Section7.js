// ** React Imports
import { useContext, useEffect, useState } from "react";

// ** MUI Imports
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";
import { AuthContext } from "src/context/AuthContext";

// ** Third Party Imports
import { Autocomplete } from "@mui/material";
import { Controller, useForm } from "react-hook-form";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";

const Section7 = ({ formData, onUpdate, employeesData }) => {
  const { getAllListValuesByListNameId } = useContext(AuthContext);

  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.assignmentAndStatus || {}, // Initialize form fields with existing data
  });

  const [leadStatusData, setLeadStatusData] = useState(null);
  const [leadPriorityData, setLeadPriorityData] = useState(null);

  const watchedFields = watch();

  // Update formData.assignmentStatus when any field changes
  useEffect(() => {
    onUpdate(watchedFields); // Send updated fields to the parent
  }, [watchedFields, onUpdate]);

  const handleError = (error) => {
    console.error("Basic profile: All Services:", error);
  };

  useEffect(() => {
    if (!!authConfig) {
      getAllListValuesByListNameId(
        authConfig.leadStatusListNamesId,
        (data) =>
          setLeadStatusData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );

      getAllListValuesByListNameId(
        authConfig.leadPriorityListNamesId,
        (data) =>
          setLeadPriorityData(
            data?.listValues?.map((item) => ({
              value: item.id,
              key: item.listValue,
            }))
          ),
        handleError
      );
    }
  }, [authConfig]);

  return (
    <Grid
      container
      spacing={3}
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        marginTop: "2rem",
      }}
    >
      {/* Assigned To */}
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
          <Controller
            name="assignedTo"
            control={control}
            render={({ field }) => (
              <Autocomplete
                id="assigned-to"
                options={employeesData || []}
                getOptionLabel={(option) => option.name || ""}
                isOptionEqualToValue={(option, value) => option.id === value}
                renderInput={(params) => (
                  <TextField {...params} label="Assigned To" size="small" />
                )}
                value={
                  employeesData?.find((data) => data.id === field.value) || null
                }
                onChange={(_, newValue) =>
                  field.onChange(newValue ? newValue.id : null)
                }
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* Lead Status */}
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
          <Controller
            name="leadStatus"
            control={control}
            render={({ field }) => (
              <Autocomplete
                id="leadStatus"
                options={leadStatusData || []}
                getOptionLabel={(option) => option.key || ""}
                isOptionEqualToValue={(option, value) => option.value === value}
                renderInput={(params) => (
                  <TextField {...params} label="Lead Status" size="small" />
                )}
                value={
                  leadStatusData?.find((data) => data.value === field.value) ||
                  null
                }
                onChange={(_, newValue) =>
                  field.onChange(newValue ? newValue.value : null)
                }
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* Lead Priority */}
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
          <Controller
            name="leadPriority"
            control={control}
            render={({ field }) => (
              <Autocomplete
                id="lead-priority"
                options={leadPriorityData || []}
                getOptionLabel={(option) => option.key || ""} // Assuming 'key' is the label
                isOptionEqualToValue={(option, value) => option.value === value} // Comparing 'value' to 'id'
                renderInput={(params) => (
                  <TextField {...params} label="Lead Priority" size="small" />
                )}
                value={
                  leadPriorityData?.find(
                    (data) => data.value === field.value
                  ) || null
                } // Matching 'value' with 'field.value'
                onChange={(_, newValue) =>
                  field.onChange(newValue ? newValue.value : null)
                } // Handling 'value' change
              />
            )}
          />
        </FormControl>
      </Grid>

      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
          <Controller
            name="curatedBy"
            control={control}
            render={({ field }) => (
              <Autocomplete
                id="curated-by"
                options={employeesData || []}
                getOptionLabel={(option) => option.name || ""}
                isOptionEqualToValue={(option, value) => option.id === value}
                renderInput={(params) => (
                  <TextField {...params} label="Curated By" size="small" />
                )}
                value={
                  employeesData?.find((data) => data.id === field.value) || null
                }
                onChange={(_, newValue) =>
                  field.onChange(newValue ? newValue.id : null)
                }
              />
            )}
          />
        </FormControl>
      </Grid>

      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
          <Controller
            name="curatedOn"
            control={control}
            rules={{ required: false }}
            render={({ field }) => (
              <TextField
                {...field}
                value={field.value || ""}
                size="small"
                label="Curated On"
                type="date"
                InputLabelProps={{ shrink: true }}
                error={Boolean(errors.curatedOn)}
                helperText={errors.curatedOn?.message}
                aria-describedby="curatedOn"
              />
            )}
          />
        </FormControl>
      </Grid>

      {/* Remarks */}
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
          <Controller
            name="remarks"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                rows={4}
                multiline
                label="Remarks"
                InputLabelProps={{
                  shrink: true,
                  style: { fontWeight: "bold" },
                }}
                error={Boolean(errors.remarks)}
                helperText={errors.remarks?.message}
                aria-describedby="statusAssignmentDetails_remarks"
                inputProps={{
                  title: "Enter any remarks",
                }}
              />
            )}
          />
        </FormControl>
      </Grid>
    </Grid>
  );
};

export default Section7;
