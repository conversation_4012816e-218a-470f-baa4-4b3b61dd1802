// ** MUI Imports
import Card from '@mui/material/Card'
import Button from '@mui/material/Button'
import Typography from '@mui/material/Typography'
import CardContent from '@mui/material/CardContent'
import { styled } from '@mui/material/styles'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

// ** Styled Components
const Illustration = styled('img')(({ theme }) => ({
  maxHeight: 120
}))

const CardReadiness2 = () => {
  return (
    <Card>
      <CardContent
        sx={{
          display: 'flex',
          textAlign: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          p: theme => `${theme.spacing(9.75, 5, 9.25)} !important`
        }}
      >
        <Illustration width={100} alt='congratulations john' src='/images/cards/congratulations-john.png' />

        <Typography variant='body2' sx={{ mb: 6 }}>
          According to us blisters are a very common thing and we come across them very often
        </Typography>
        <Button variant='contained' sx={{ p: theme => theme.spacing(1.75, 5.5) }}>
          Calculator
        </Button>
      </CardContent>
    </Card>
  )
}

export default CardReadiness2
