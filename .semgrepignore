# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
.next/
out/
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output/
__mocks__/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Cache directories
.cache/
.eslintcache

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

# Docker
Dockerfile
docker-compose*.yml

# CI/CD
.github/
ci.yml

# Documentation (unless you want to scan for hardcoded secrets in docs)
*.md
docs/

# Static assets
public/images/
public/icons/
public/fonts/
*.png
*.jpg
*.jpeg
*.gif
*.webp
*.svg
*.ico

# Config files that are usually safe
next.config.js
ecosystem.config.js
prettier.config.js
eslint.config.js

# PDF files
*.pdf

# Generated/bundled files
src/iconify-bundle/

# Kubernetes
k8s/ 

# Pages
src/pages
