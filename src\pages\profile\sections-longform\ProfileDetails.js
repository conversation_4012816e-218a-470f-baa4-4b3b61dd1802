import {
  Box,
  FormControl,
  Grid, TextField,
  Typography
} from "@mui/material";
import { useEffect, useRef } from "react";
import { Controller, useForm } from "react-hook-form";
import MobileNumberValidation from "src/@core/components/custom-components/MobileNumberValidation";
import NameTextField from "src/@core/components/custom-components/NameTextField";

const ProfileDetails = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: formData?.profileDetails || {}, // Initialize form fields with existing data
  });

  // Ref to track if the initial reset has been called
  const isInitialized = useRef(false);

  // Reinitialize form when formData.societyDetails changes
  useEffect(() => {
    if (formData?.profileDetails && !isInitialized.current) {
      reset(formData.profileDetails); // Reset the form with initial values
      isInitialized.current = true; // Mark as initialized
    }
  }, [formData?.profileDetails, reset]);

  // Watch all fields for changes
  const watchedFields = watch();
  // Update formData on any change
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    const hasWatchedFieldsChanged = JSON.stringify(previousWatchedFields.current) !== JSON.stringify(watchedFields);

    if (hasWatchedFieldsChanged) {
      onUpdate({
        ...watchedFields
      });
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields,onUpdate]);

  return (
    <Box sx={{ pt: 3 }}>
      <Grid
        container
        spacing={3}
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          marginTop: "2rem",
        }}
      >
        <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
              <Controller
                name='name'
                control={control}
                rules={{ required: 'Name is required' }}
                render={({ field }) => (
                  <NameTextField
                    {...field}
                    size='small'
                    label='Name*'
                    placeholder='Enter Name'
                    error={Boolean(errors.name)}
                    helperText={errors.name?.message}
                  />
                )}
              />
            </FormControl>
        </Grid>

          {/* Mobile Number */}
          <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        <FormControl fullWidth>
              <Controller
                name='mobileNumber'
                control={control}
                rules={{ required: 'Mobile Number is required' }}
                render={({ field }) => (
                  <MobileNumberValidation
                    {...field}
                    type='tel'
                    label='Mobile Number*'
                    size='small'
                    error={Boolean(errors.contactNumber)}
                    helperText={errors.contactNumber?.message}
                    InputLabelProps={{ shrink: true }}
                    placeholder='+91 1234567890'
                    inputProps={{
                      maxLength: field?.value?.startsWith('+91') ? 13 : 10
                    }}
                  />
                )}
              />
            </FormControl>
        </Grid>

        {/* Email */}
        <Grid
          container
          item
          xs={12}
          sm={6}
          md={4}
          lg={4}
          xl={4}
          sx={{ width: "100%", mt: 1, mb: 2, ml: 3 }}
          spacing={2}
        >
          <Grid item>
            <Typography className="data-field"> Email:</Typography>
          </Grid>
          <Grid item>
            <Typography style={{ fontWeight: "bold" }}>{formData?.profileDetails?.email}</Typography>
          </Grid>
        </Grid>

      </Grid>
    </Box>
  );
};

export default ProfileDetails;
