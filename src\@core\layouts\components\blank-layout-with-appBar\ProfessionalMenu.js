// ** React Imports
import { useState } from 'react'

// ** MUI Imports
import Menu from '@mui/material/Menu'
import Button from '@mui/material/Button'
import MenuItem from '@mui/material/MenuItem'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

const ProfessionalMenu = () => {
  // ** State
  const [anchorEl, setAnchorEl] = useState(null)

  const handleClick = event => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  return (
    <div>
      <Button
        sx={{ textTransform: 'none', color: '#444', fontSize: '16px' }}
        aria-controls='simple-menu'
        aria-haspopup='true'
        onClick={handleClick}
      >
      
        For Players
        {/* <Icon icon='tabler:down' /> */}
      </Button>
      <Menu keepMounted id='simple-menu' anchorEl={anchorEl} onClose={handleClose} open={Boolean(anchorEl)}>
        <MenuItem onClick={handleClose}>Society</MenuItem>
        <MenuItem onClick={handleClose}>Architects</MenuItem>
        <MenuItem onClick={handleClose}>PMC</MenuItem>
        <MenuItem onClick={handleClose}>Structural Engineers</MenuItem>
        <MenuItem onClick={handleClose}>Broker</MenuItem>
        <MenuItem onClick={handleClose}>CA</MenuItem>
        <MenuItem onClick={handleClose}>Builders</MenuItem>
        <MenuItem onClick={handleClose}>Land Owners</MenuItem>
      </Menu>
    </div>
  )
}

export default ProfessionalMenu
