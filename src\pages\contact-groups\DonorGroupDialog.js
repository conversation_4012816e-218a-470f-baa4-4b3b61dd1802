import React, { useContext, useEffect, useRef, useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Step,
  <PERSON><PERSON>abel,
  Stepper,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useForm } from 'react-hook-form';
import { AuthContext } from 'src/context/AuthContext';
import { useAuth } from 'src/hooks/useAuth';
import toast, { Toaster } from 'react-hot-toast';
import CustomAvatar from 'src/@core/components/mui/avatar';
import CloseIcon from '@mui/icons-material/Close';
import HomeIcon from '@mui/icons-material/Home';
import PeopleIcon from '@mui/icons-material/People';
import PreviewIcon from '@mui/icons-material/Preview';

// Import step components
import GroupDetails from './GroupDetails';
import ReviewCreate from './ReviewCreate';
import DonorSelection from './DonorSelection';

const DonorGroupDialog = ({
  open,
  onClose,
  fetchDonorGroups,
  tenantsList,
  currentRow
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const auth = useAuth();
  const { user } = useContext(AuthContext);
  const isApiCalling = useRef(false);

  // Form state
  const {
    control,
    setValue,
    reset,
    formState: { errors }
  } = useForm();

  // Step management
  const [currentStep, setCurrentStep] = useState(0);
  const [formDataOne, setFormData] = useState({
    profileDetails: {
      contactGroupName: "",
      description: "",
    },
  });

  // Donors data
  const [selectedRows, setSelectedRows] = useState([]);
  const [tenantId, setTenantId] = useState("");
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [existingList, setExistingList] = useState([]);
  const [newList, setNewList] = useState([]);
  const [selectedFilters, setSelectedFilters] = useState([]);

  // Dialog state
  const [dialogMessage, setDialogMessage] = useState("");
  const [dialogSuccess, setDialogSuccess] = useState(false);

  // Define steps
  const sections = [
    {
      id: "groupDetails",
      title: "Group Details",
      component: GroupDetails,
      icon: <HomeIcon />,
    },
    {
      id: "donorSelection",
      title: "Donor Selection",
      component: DonorSelection,
      icon: <PeopleIcon />,
    },
    {
      id: "review",
      title: "Review & Create",
      component: ReviewCreate,
      icon: <PreviewIcon />,
    },
  ];

  // Load form data if editing
  useEffect(() => {
    if (currentRow && Object.keys(currentRow).length > 0) {
      setFormData(prev => ({
        ...prev,
        profileDetails: {
          contactGroupName: currentRow.name,
          description: currentRow.description
        }
      }));
      
      // If editing, load the donors
      if (currentRow.donorResponseList && currentRow.donorResponseList.length > 0) {
        setSelectedUsers(currentRow?.donorResponseList);
      }
     
      
      // Load filters if any
      if (currentRow.filters) {
        const filtersArray = [];
        Object.keys(currentRow.filters).forEach(key => {
          filtersArray.push({
            key,
            value: currentRow.filters[key]
          });
        });
        setSelectedFilters(filtersArray);
      }
      
      if (user?.organisationCategory === 'SUPER_ADMIN' && currentRow.orgId) {
        setTenantId(currentRow.orgId);
      }
    }
  }, [currentRow, user]);

  // Handle field updates for individual sections
  const handleFieldUpdate = (sectionId, updatedData) => {
    setFormData((prevData) => {
      return {
        ...prevData,
        [sectionId]: updatedData,
      };
    });
  };

  // Handle step navigation
  const handleNext = () => {
    if (currentStep === 0) {
      // Validate group details
      if (!formDataOne.profileDetails.contactGroupName) {
        toast.error('Group name is required');
        return;
      }
    }
    
    if (currentStep === 1 && selectedUsers.length === 0) {
      toast.error('Please add at least one donor to the final list');
      return;
    }
    
    if (currentStep < sections.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  // Handle form submission
  const handleFormSubmit = async () => {
    if (isApiCalling.current) {
      return;
    }

    if (selectedUsers.length < 1) {
      toast.error('At least one contact should be in final list to create a group');
      return;
    }
    
    isApiCalling.current = true;

    const filters = {};
    selectedFilters?.forEach((filter) => {
      const key = filter.key;
      filters[key] = filter.value;
    });
    
    const fields = {
      name: formDataOne.profileDetails.contactGroupName,
      description: formDataOne.profileDetails.description,
      filters: filters,
      orgId: user?.organisationCategory === 'SUPER_ADMIN' ? tenantId : user?.orgId,
      donorIds: selectedUsers?.map((user) => user.id)
    };

    try {
      if (currentRow && currentRow.id) {
        // Update existing group
        fields.id = currentRow.id;
        await auth.patchDonorGroup(
          fields,
          handleUpdateFailure,
          handleUpdateSuccess
        );
      } else {
        // Create new group
        await auth.postDonorGroup(
          fields,
          handleFailure,
          handleSuccess
        );
      }
      
      if (fetchDonorGroups) {
        fetchDonorGroups();
      }
    } catch (error) {
      console.error('Group operation failed:', error);
      handleFailure();
    } finally {
      isApiCalling.current = false;
      handleCancel();
    }
  };

  // Success/failure handlers
  const handleSuccess = () => {
    const message = `Donor Group Created Successfully.`;
    setDialogMessage(message);
    setDialogSuccess(true);
  };

  const handleFailure = () => {
    const message = `Failed to Create Group. Please try again later.`;
    setDialogMessage(message);
    setDialogSuccess(true);
  };

  const handleUpdateSuccess = () => {
    const message = `Donor Group Updated Successfully.`;
    setDialogMessage(message);
    setDialogSuccess(true);
  };

  const handleUpdateFailure = () => {
    const message = `Failed to Update Group. Please try again later.`;
    setDialogMessage(message);
    setDialogSuccess(true);
  };

  // Handle dialog close
  const handleCancel = () => {
    reset();
    setSelectedUsers([]);
    setSelectedRows([]);
    setFormData({
      profileDetails: {
        contactGroupName: "",
        description: "",
      },
    });
    setSelectedFilters([]);
    setCurrentStep(0);
    onClose();
  };

  const handleCloseDialog = () => {
    setDialogSuccess(false);
    onClose();
  };

  // Render section content
  const renderSection = () => {
    if (currentStep === 0) {
      return (
        <GroupDetails
          formData={formDataOne}
          onUpdate={(updatedData) => handleFieldUpdate('profileDetails', updatedData)}
          tenantsList={tenantsList}
          tenantId={tenantId}
          setTenantId={setTenantId}
          user={user}
        />
      );
    } else if (currentStep === 1) {
      return (
        <DonorSelection
          selectedUsers={selectedUsers}
          setSelectedUsers={setSelectedUsers}
          selectedFilters={selectedFilters}
          setSelectedFilters={setSelectedFilters}
          tenantId={tenantId}
          setExistingList={setExistingList}
          setNewList={setNewList}
          newList={newList}
        />
      );
    } else {
      return (
        <ReviewCreate
          formValues={formDataOne.profileDetails}
          selectedUsers={selectedUsers}
          setSelectedUsers={setSelectedUsers}
        />
      );
    }
  };

  return (
    <>
      <Dialog 
        fullScreen 
        open={open} 
        onClose={handleCancel}
      >
        <DialogTitle
          sx={{
            position: 'relative',
            borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => `${theme.spacing(1.75, 4)} !important`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: { xs: 'start' },
            fontSize: { xs: 19, md: 20 },
            height: '50px',
            marginLeft: { xl: 4, lg: 4, md: 4, sm: 4, xs: 4 }
          }}
        >
          {!currentRow || Object.keys(currentRow).length === 0
            ? "Create a Donor Group"
            : "Update Donor Group"}
          <Box
            sx={{
              position: 'absolute',
              top: '9px',
              right: '10px',
              marginRight: { xl: 10, lg: 6, md: 6, sm: 5.5, xs: 5.7 }
            }}
          >
            <IconButton onClick={handleCancel}>
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent
          sx={{
            p: (theme) => `${theme.spacing(5, 6)} !important`,
            position: 'relative',
            display: 'flex',
            flexDirection: 'column',
            height: '100%'
          }}
        >
          {/* Stepper */}
          <Stepper
            activeStep={currentStep}
            sx={{
              mb: 6,
              display: { xs: 'none', md: 'flex' }
            }}
          >
            {sections.map((section) => (
              <Step key={section.id}>
                <StepLabel
                  StepIconComponent={() => (
                    <CustomAvatar
                      skin={currentStep === sections.indexOf(section) ? 'filled' : 'light'}
                      variant='rounded'
                      color={currentStep >= sections.indexOf(section) ? 'primary' : 'secondary'}
                      sx={{ mr: 2, width: 30, height: 30 }}
                    >
                      {section.icon}
                    </CustomAvatar>
                  )}
                >
                  {section.title}
                </StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Mobile Stepper - Just show current step title */}
          <Box sx={{ mb: 4, display: { xs: 'block', md: 'none' } }}>
            <Typography variant='h6'>{sections[currentStep].title}</Typography>
            <Typography variant='body2'>
              Step {currentStep + 1} of {sections.length}
            </Typography>
          </Box>

          {/* Main Content Area */}
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
              overflowY: 'auto',
            }}
          >
            {/* Section Content */}
            <Box sx={{ flex: 1 }}>{renderSection()}</Box>
          </Box>
        </DialogContent>

        <DialogActions
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            p: (theme) => theme.spacing(3, 6),
            borderTop: (theme) => `1px solid ${theme.palette.divider}`
          }}
        >
          <Button
            variant='outlined'
            color='secondary'
            onClick={currentStep === 0 ? handleCancel : handlePrevious}
          >
            {currentStep === 0 ? 'Cancel' : 'Previous'}
          </Button>
          
          {currentStep === sections.length - 1 ? (
            <Button
              variant='contained'
              color='primary'
              onClick={handleFormSubmit}
            >
              {currentRow && currentRow.id ? 'Update Group' : 'Create Group'}
            </Button>
          ) : (
            <Button
              variant='contained'
              color='primary'
              onClick={handleNext}
            >
              Next
            </Button>
          )}
        </DialogActions>
        
        <Toaster position="top-right" />
      </Dialog>

      {/* Success/Failure Dialog */}
      <Dialog
        open={dialogSuccess}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: '100%',
            borderRadius: 1,
            textAlign: 'center',
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: 'primary.main',
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              {dialogMessage}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseDialog}
              sx={{ margin: 'auto', width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default DonorGroupDialog;
