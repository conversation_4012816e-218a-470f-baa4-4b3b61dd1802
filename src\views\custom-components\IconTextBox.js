// ** MUI Imports
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import { styled } from '@mui/material/styles'

// ** Icon Imports
import Icon from 'src/@core/components/icon'

// ** Custom Components Imports
import CustomAvatar from 'src/@core/components/mui/avatar'

const IconTextBox = props => {
  const { icon, title, subtitle } = props

  return (
    <Box sx={{ mb: 3.5, display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
      <CustomAvatar skin='light' color='primary' sx={{ mr: 5, width: 34, height: 34 }}>
        <Icon icon={icon} />
      </CustomAvatar>
      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
        <Typography sx={{ fontSize: '17px' }}>{title}</Typography>
        <Typography variant='body2'>{subtitle || null}</Typography>
      </Box>
    </Box>
  )
}

export default IconTextBox
