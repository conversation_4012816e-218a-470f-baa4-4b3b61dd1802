import React, { useState } from "react";
import axios from "axios";
import authConfig from "src/configs/auth";

import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import {
  Button,
  DialogContentText,
  CircularProgress,
  Box,
  Snackbar,
  Alert,
} from "@mui/material";

import { getAuthorizationHeaders } from "src/helpers/utils";

const DeleteDialog = ({ open, onClose, data }) => {
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  async function onDelete(data) {
    setLoading(true);

    const url = authConfig.baseURL + authConfig.donationReceiptEndpoint + "/" + data.id;
    const headers = getAuthorizationHeaders();
    
    try {
        if (data?.isActive) {
            // Call axios.delete if isActive is true
            await axios.delete(url, { headers });
            setSuccessMessage(`${data.receiptNo} Deactivated successfully.`);
          } else {
            // Call axios.patch if isActive is false
            await axios.patch(url, { headers });
            setSuccessMessage(`${data.receiptNo} Activated successfully.`);
          }
      
    } catch (error) {
      console.error("Delete operation failed", error);
    } finally {
      setLoading(false);
      onClose();
    }
  }

  const handleCloseSnackbar = () => {
    setSuccessMessage("");
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
                 {data?.isActive ? `Are you sure you want to deactivate ${data?.receiptNo} ?`:`Are you sure, you want to activate the ${data?.receiptNo} ?`}
             
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={() => onDelete(data)}
              sx={{ margin: "auto", width: 100 }}
              disabled={loading}
            >
              {data?.isActive ? loading ? <CircularProgress size={24} /> : "DeActivate": loading ? <CircularProgress size={24} /> : "Activate"}
            </Button>
            <Button
              variant="contained"
              onClick={onClose}
              sx={{ margin: "auto", width: 100 }}
              disabled={loading}
            >
              Cancel
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
      <Dialog
        open={!!successMessage}
        onClose={handleCloseSnackbar}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              {successMessage}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleCloseSnackbar}
              sx={{ margin: "auto", width: 100 }}
              autoFocus
            >
              OK
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default DeleteDialog;
