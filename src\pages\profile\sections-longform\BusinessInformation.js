// ** React Imports
import { useEffect, useRef } from "react";

// ** MUI Imports
import { InputLabel, MenuItem, Select } from "@mui/material";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import TextField from "@mui/material/TextField";

// ** Third Party Imports
import { Controller, useForm, useWatch } from "react-hook-form";

const BusinessInformation = ({ formData, onUpdate }) => {
  const {
    control,
    watch,
    formState: { errors },
  } = useForm({
    defaultValues: {
      ...formData?.businessInformation,
      doYouHaveGstNo: formData?.businessInformation?.doYouHaveGstNo || "",
    },
  });

  const watchedFields = watch();
  // Track previous watched fields using useRef
  const previousWatchedFields = useRef();

  useEffect(() => {
    // Compare previous watched fields with current watched fields
    if (
      JSON.stringify(previousWatchedFields.current) !==
      JSON.stringify(watchedFields)
    ) {
      onUpdate(watchedFields); // Send only the updated fields to the parent
      previousWatchedFields.current = watchedFields; // Update the ref with the latest value
    }
  }, [watchedFields, onUpdate]);

  const gstOption = useWatch({ control, name: "doYouHaveGstNo" });

  const renderTextField = ({
    name,
    label,
    placeholder,
    rules = {},
    errors,
    type = "text",
    multiline = false,
  }) => (
    <FormControl fullWidth>
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <TextField
            {...field}
            value={field.value || ""}
            label={label}
            placeholder={placeholder}
            InputLabelProps={{ shrink: true }}
            size="small"
            type={type}
            multiline={multiline}
            rows={multiline ? 2 : 1}
            error={Boolean(errors[name])}
            helperText={errors[name]?.message || ""}
          />
        )}
      />
    </FormControl>
  );

  return (
    <Grid
      container
      spacing={3}
      sx={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        marginTop: "2rem",
      }}
    >
      {/* PAN Number */}
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        {renderTextField({
          name: "pan",
          label: "PAN Number",
          placeholder: "Enter PAN Number",
          errors,
        })}
      </Grid>

      {/* Aadhar */}
      <Grid item xs={12} sm={6} md={4} lg={4} xl={4} sx={{ width: "100%" }}>
        {renderTextField({
          name: "aadhar",
          label: "Aadhar Number",
          placeholder: "Enter Aadhar",
          errors,
        })}
      </Grid>

    </Grid>
  );
};

export default BusinessInformation;
