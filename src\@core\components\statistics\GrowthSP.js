import dynamic from 'next/dynamic';

const ApexChart = dynamic(() => import('react-apexcharts'), { ssr: false });

const GrowthSP = () => {
  const state = {
    series: [
      {
        name: 'Enrolled SP',
        data: [30, 50, 70, 90, 110, 130, 150] // Data for Enrolled SP over the years
      },
      {
        name: 'New SP',
        data: [20, 40, 60, 80, 100, 120, 140] // Data for New SP over the years
      },
      {
        name: 'Avoid SP',
        data: [10, 30, 50, 70, 90, 110, 130] // Data for Avoid SP over the years
      }
    ],
    options: {
      chart: {
        type: 'bar',
        height: 350,
        stacked: true, // Stacked bar chart
      },
      plotOptions: {
        bar: {
          horizontal: true, // Horizontal bars
          dataLabels: {
            total: {
              enabled: true, // Enable total display
              offsetX: 0,
              style: {
                fontSize: '13px',
                fontWeight: 900
              }
            }
          }
        },
      },
      stroke: {
        width: 1,
        colors: ['#fff']
      },
      title: {
        text: 'Service Providers (SP) Data Over Years',
        align: 'left' // Align title to the left
      },
      xaxis: {
        categories: [2018, 2019, 2020, 2021, 2022, 2023, 2024], // Yearly categories
        labels: {
          formatter: function (val) {
            return val + "K"; // Display data in thousands
          }
        }
      },
      yaxis: {
        title: {
          text: 'Number of Service Providers (K)' // Y-axis label
        },
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + "K"; // Format tooltip to show data in thousands
          }
        },
        // Custom tooltip to show additional info
        custom: function({ seriesIndex, dataPointIndex, w }) {
          let info = '';
          if (seriesIndex === 0) {
            info = `Enrolled Service Providers: ${w.globals.series[seriesIndex][dataPointIndex]}K`;
          } else if (seriesIndex === 1) {
            info = `New Service Providers: ${w.globals.series[seriesIndex][dataPointIndex]}K`;
          } else if (seriesIndex === 2) {
            info = `Avoid Service Providers: ${w.globals.series[seriesIndex][dataPointIndex]}K`;
          }
          return `<div style="padding:10px;">${info}</div>`;
        }
      },
      fill: {
        opacity: 1
      },
      legend: {
        position: 'top',
        horizontalAlign: 'left',
        offsetX: 40, // Offset legend for better positioning
        itemMargin: { vertical: 10 }, // Space between legend items
      },
      annotations: {
        points: [
          {
            x: 2022,
            y: 150,
            seriesIndex: 0,
            label: {
              borderColor: '#fff',
              offsetY: 0,
              style: {
                color: '#fff',
                background: '#FF4560',
              },
              text: 'Peak in Enrolled SP',
            },
          },
        ],
      },
    },
  };

  return (
    <div>
      <div id="chart">
        <ApexChart options={state.options} series={state.series} type="bar" height={350} />
      </div>
    </div>
  );
};

export default GrowthSP;
