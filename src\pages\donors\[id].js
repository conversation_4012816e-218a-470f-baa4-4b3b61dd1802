import { useRouter } from "next/router";
import { useEffect, useState, useContext } from "react";
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  Breadcrumbs,
  Link,
  IconButton,
  Chip,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Skeleton,
  Alert,
  Avatar,
  Divider,
  Switch,
  FormControlLabel,
  Button,
  CircularProgress,
} from "@mui/material";

import {
  ArrowBack,
  Person,
  Email,
  Phone,
  CreditCard,
  TrendingUp,
  CalendarToday,
  MonetizationOn,
  ExpandMore,
  Star,
  Timeline as TimelineIcon,
  Assessment,
  ContactMail,
  Notes,
} from "@mui/icons-material";
import dynamic from "next/dynamic";
import axios from "axios";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRBAC } from "../permission/RBACContext";
import { MENUS, PAGES, PERMISSIONS } from "src/constants";

// Dynamically import ApexChart to avoid SSR issues
const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const DonorDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const { user } = useContext(AuthContext);
  const { canMenuPage, rbacRoles } = useRBAC();

  // State management
  const [donorData, setDonorData] = useState(null);
  const [donationHistory, setDonationHistory] = useState([]);
  const [donorStats, setDonorStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [chartData, setChartData] = useState({ series: [], options: {} });

  // Permission check
  const canAccessDonors = (requiredPermission) =>
    canMenuPage(MENUS.LEFT, PAGES.DONORS, requiredPermission);

  // Transform API response to component data structure
  const transformApiData = (apiResponse) => {
    if (!apiResponse || apiResponse.length === 0) {
      return null;
    }

    // Get donor info from first record (all records have same donor info)
    const firstRecord = apiResponse[0];

    // Calculate stats from all donations
    const totalDonations = apiResponse.reduce((sum, donation) => sum + parseFloat(donation.donationAmount), 0);
    const donationCount = apiResponse.length;
    const averageDonation = totalDonations / donationCount;

    // Sort donations by date to get first and last
    const sortedDonations = [...apiResponse].sort((a, b) => new Date(a.dateOfDonation) - new Date(b.dateOfDonation));
    const firstDonationDate = sortedDonations[0].dateOfDonation;
    const lastDonationDate = sortedDonations[sortedDonations.length - 1].dateOfDonation;

    // Sort donations by date in ascending order (oldest first)
    const sortedDonationsAsc = [...apiResponse].sort((a, b) =>
      new Date(a.dateOfDonation) - new Date(b.dateOfDonation)
    );

    // Transform donations for timeline (in chronological order)
    const transformedDonations = sortedDonationsAsc?.map((donation, index) => ({
      id: `donation-${index + 1}`,
      amount: parseFloat(donation.donationAmount),
      date: donation.dateOfDonation,
      donationHead: donation.donationHead,
      paymentMode: donation.paymentMode,
      paymentType: donation.paymentType,
      receiptNumber: `RCP-${new Date(donation.dateOfDonation).getFullYear()}-${String(index + 1).padStart(3, '0')}`,
      notes: "", // API doesn't provide notes, can be added later
    }));

    // Create chart data - group by month and sort chronologically
    const chartData = {};
    sortedDonationsAsc.forEach(donation => {
      const date = new Date(donation.dateOfDonation);
      const monthKey = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
      const sortKey = date.getFullYear() * 100 + date.getMonth(); // Create sortable key (YYYYMM format)

      if (!chartData[monthKey]) {
        chartData[monthKey] = {
          amount: 0,
          sortKey: sortKey
        };
      }
      chartData[monthKey].amount += parseFloat(donation.donationAmount);
    });

    // Convert to chart series format and sort chronologically
    const chartSeries = Object.entries(chartData)
      ?.map(([month, data]) => ({
        month,
        amount: data.amount,
        sortKey: data.sortKey
      }))
      .sort((a, b) => a.sortKey - b.sortKey) // Sort by chronological order
      ?.map(({ month, amount }) => ({ month, amount })); // Remove sortKey from final result

    return {
      donor: {
        id: id,
        name: firstRecord.name,
        email: firstRecord.email,
        contactNumber: firstRecord.mobileNumber,
        panNo: "", // Not provided in API
        address: "", // Not provided in API
        state: "", // Not provided in API
        pinCode: "", // Not provided in API
        isActive: true, // Assume active
        createdOn: firstDonationDate,
        orgName: firstRecord.orgName,
        tags: [], // Not provided in API
        communicationPreferences: {
          email: true,
          sms: true,
          whatsapp: false,
        },
        notes: `Donor associated with ${firstRecord.orgName}. Total contributions: ₹${totalDonations.toLocaleString()}`
      },
      stats: {
        totalDonations: totalDonations,
        donationCount: donationCount,
        averageDonation: averageDonation,
        firstDonationDate: firstDonationDate,
        lastDonationDate: lastDonationDate,
        donorPercentile: Math.min(85, Math.max(50, Math.floor((averageDonation / 15000) * 100))), // Calculated based on average
        organizationAverage: averageDonation * 0.8, // Assume donor is above average
      },
      donations: transformedDonations, // Already sorted in ascending chronological order
      chartSeries: chartSeries
    };
  };

  // Fetch donor data
  useEffect(() => {
    if (id && rbacRoles?.length > 0) {
      if (!canAccessDonors(PERMISSIONS.READ)) {
        router.push("/401");
        return;
      }
      fetchDonorData();
    }
  }, [id, rbacRoles]);

  const fetchDonorData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Call the actual API endpoint for donor donations
      const url = getUrl(authConfig.donorsEndpoint) + `/${id}/donations`;
      const headers = getAuthorizationHeaders();

      const response = await axios({
        method: "get",
        url: url,
        headers: headers,
      });

      // Transform API response to component data structure
      const transformedData = transformApiData(response.data);

      if (!transformedData) {
        setError("No donation data found for this donor.");
        return;
      }

      setDonorData(transformedData.donor);
      setDonorStats(transformedData.stats);
      setDonationHistory(transformedData.donations);

      // Prepare chart data
      const chartSeries = transformedData.chartSeries;
      setChartData({
        series: [{
          name: 'Donation Amount',
          data: chartSeries?.map(item => item.amount)
        }],
        options: {
          chart: {
            type: 'line',
            height: 350,
            toolbar: {
              show: false
            }
          },
          stroke: {
            curve: 'smooth',
            width: 3
          },
          colors: ['#1f1f7a'],
          xaxis: {
            categories: chartSeries?.map(item => item.month),
            title: {
              text: 'Time Period'
            }
          },
          yaxis: {
            title: {
              text: 'Amount (₹)'
            },
            labels: {
              formatter: function (val) {
                return '₹' + val.toLocaleString();
              }
            }
          },
          tooltip: {
            y: {
              formatter: function (val) {
                return '₹' + val.toLocaleString();
              }
            }
          },
          grid: {
            borderColor: '#f1f1f1'
          },
          markers: {
            size: 6,
            colors: ['#1f1f7a'],
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: {
              size: 8
            }
          }
        }
      });

    } catch (err) {
      console.error("Error fetching donor data:", err);
      setError("Failed to load donor data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!canAccessDonors(PERMISSIONS.READ)) {
    return null;
  }

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '60vh',
          flexDirection: 'column',
          gap: 2
        }}
      >
        <CircularProgress size={60} thickness={4} />
        <Typography variant="h6" color="text.secondary">
          Loading donor details...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => router.back()}>
          Go Back
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Breadcrumb Navigation */}
      <Box sx={{ mb: 3 }}>
        <Breadcrumbs aria-label="breadcrumb">
          <Link
            color="inherit"
            href="/donors"
            onClick={(e) => {
              e.preventDefault();
              router.push('/donors');
            }}
            sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
          >
            <Person sx={{ mr: 0.5 }} fontSize="inherit" />
            Donors
          </Link>
          <Typography color="text.primary" sx={{ display: 'flex', alignItems: 'center' }}>
            <Assessment sx={{ mr: 0.5 }} fontSize="inherit" />
            {donorData?.name}
          </Typography>
        </Breadcrumbs>
      </Box>

      {/* Header Section */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={() => router.back()} sx={{ mr: 2 }}>
          <ArrowBack />
        </IconButton>
        <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
          <Person />
        </Avatar>
        <Box sx={{ flexGrow: 1 }}>
          <Typography variant="h5" component="h1" gutterBottom>
            {donorData?.name}
          </Typography>
        </Box>
      </Box>

      {/* Stats Cards Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <MonetizationOn sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5" component="div" color="primary.main" fontWeight="bold">
                {formatCurrency(donorStats?.totalDonations || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                Total Donations
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <Assessment sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5" component="div" color='primary.main' fontWeight="bold">
                {donorStats?.donationCount || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                Number of Donations
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <TrendingUp sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="h5" component="div" color='primary.main' fontWeight="bold">
                {formatCurrency(donorStats?.averageDonation || 0)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem' }}>
                Average Donation
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ textAlign: 'center', py: 2 }}>
              <CalendarToday sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
              <Typography variant="body1" component="div" color='primary.main' fontWeight="bold" sx={{ fontSize: '0.9rem' }}>
                {formatDate(donorStats?.firstDonationDate || '')}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: '0.8rem' }}>
                First Donation
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                Last: {formatDate(donorStats?.lastDonationDate || '')}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

        {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Left Column - Horizontal Donation Timeline */}
        <Grid item xs={12} md={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <TimelineIcon sx={{ mr: 1 }} />
              Donation Timeline
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{
                  ml: 'auto',
                  fontSize: '0.875rem',
                  display: { xs: 'none', sm: 'block' } // Hide on mobile (xs), show on small screens and up
                }}
              >
                ← Scroll horizontally to view all donations →
              </Typography>
            </Typography>

            {/* Horizontal Timeline Container */}
            <Box
              sx={{
                position: 'relative',
                overflow: 'hidden',
                '&:hover .scroll-indicator': {
                  opacity: 0.7
                }
              }}
            >
              {/* Scroll Indicators */}
              <Box
                className="scroll-indicator"
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '20px',
                  height: '100%',
                  background: 'linear-gradient(to right, rgba(255,255,255,0.8), transparent)',
                  zIndex: 2,
                  opacity: 0,
                  transition: 'opacity 0.3s ease',
                  pointerEvents: 'none'
                }}
              />
              <Box
                className="scroll-indicator"
                sx={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  width: '20px',
                  height: '100%',
                  background: 'linear-gradient(to left, rgba(255,255,255,0.8), transparent)',
                  zIndex: 2,
                  opacity: 0,
                  transition: 'opacity 0.3s ease',
                  pointerEvents: 'none'
                }}
              />

              {/* Horizontal Scrollable Timeline */}
              <Box
                sx={{
                  display: 'flex',
                  overflowX: 'auto',
                  overflowY: 'hidden',
                  pb: 2,
                  gap: 3,
                  minHeight: '200px',
                  scrollBehavior: 'smooth',
                  '&::-webkit-scrollbar': {
                    height: '8px',
                  },
                  '&::-webkit-scrollbar-track': {
                    backgroundColor: '#f1f1f1',
                    borderRadius: '4px',
                  },
                  '&::-webkit-scrollbar-thumb': {
                    backgroundColor: '#c1c1c1',
                    borderRadius: '4px',
                    '&:hover': {
                      backgroundColor: '#a1a1a1',
                    },
                  },
                }}
              >
                {donationHistory?.map((donation, index) => (
                  <Box
                    key={donation.id}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      minWidth: { xs: '200px', sm: '230px' },
                      position: 'relative',
                    }}
                  >
                    {/* Timeline Connector Line - Left half */}
                    {donationHistory.length > 1 && index > 0 && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '20px',
                          left: '0%',
                          width: '50%',
                          height: '2px',
                          backgroundColor: 'primary.main',
                          zIndex: 0,
                        }}
                      />
                    )}

                    {/* Timeline Connector Line - Right half */}
                    {donationHistory.length > 1 && index < donationHistory.length - 1 && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '20px',
                          left: '50%',
                          width: '50%',
                          height: '2px',
                          backgroundColor: 'primary.main',
                          zIndex: 0,
                        }}
                      />
                    )}

                    {/* Timeline Dot */}
                    <Box
                      sx={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        backgroundColor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 1,
                        mb: 2,
                        boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                      }}
                    >
                      <MonetizationOn sx={{ color: 'white', fontSize: '20px' }} />
                    </Box>

                    {/* Donation Card */}
                    <Card
                      sx={{
                        width: '100%',
                        minHeight: '120px',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        }
                      }}
                    >
                      <CardContent sx={{ p: 2 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Typography variant="h6" color="primary.main" sx={{ fontSize: '1.1rem', fontWeight: 'bold' }}>
                            {formatCurrency(donation.amount)}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontSize: '0.75rem' }}>
                          {formatDate(donation.date)}
                        </Typography>
                        <Typography variant="body2" gutterBottom sx={{ fontWeight: 500, mb: 1 }}>
                          {donation.donationHead}
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mb: 1 }}>
                          <Chip
                            label={donation.paymentMode}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: '20px' }}
                          />
                          <Chip
                            label={donation.receiptNumber}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: '20px' }}
                          />
                        </Box>
                        {donation.notes && (
                          <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.7rem', fontStyle: 'italic' }}>
                            {donation.notes}
                          </Typography>
                        )}
                      </CardContent>
                    </Card>
                  </Box>
                ))}
              </Box>
            </Box>

            {/* Navigation Hint for Mobile */}
            <Box sx={{
              display: { xs: 'block', sm: 'none' },
              textAlign: 'center',
              mt: 2,
              color: 'text.secondary',
              fontSize: '0.75rem'
            }}>
              👆 Swipe left/right to navigate timeline
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Donation Frequency Chart */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <TimelineIcon sx={{ mr: 1 }} />
              Donation Pattern Over Time
            </Typography>
            <Box sx={{ height: 350 }}>
              {chartData.series.length > 0 && (
                <ApexChart
                  options={chartData.options}
                  series={chartData.series}
                  type="line"
                  height={350}
                />
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>

    
    </Box>
  );
};

export default DonorDetailPage;
