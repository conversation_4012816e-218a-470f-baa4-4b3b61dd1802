import React, { useContext, useEffect, useState } from "react";
import dynamic from "next/dynamic";
import { Box, Typography, Card } from "@mui/material";
import { FaUserTie } from "react-icons/fa";
import axios from "axios";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import authConfig from "src/configs/auth";
import { useRouter } from "next/router";
import { AuthContext } from "src/context/AuthContext";

const ApexChart = dynamic(() => import("react-apexcharts"), { ssr: false });

const ServiceProviderGraph = () => {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [sampleData, setSampleData] = useState({
    spCount: { daily: 0, monthly: 0, quarterly: 0, yearly: 0 },
    chsCount: { daily: 0, monthly: 0, quarterly: 0, yearly: 0 },
  });
  useEffect(() => {
    let url;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs +
          "/admin/individuals-group-by-role-and-duration"
      );
    } else {
      url = getUrl(
        authConfig?.statisticsEndpointGraphs +
          "/individuals-group-by-role-and-duration"
      );
    }

    let headers;
    if (user?.roleId === authConfig?.superAdminRoleId) {
      headers = getAuthorizationHeaders({
        accept:
          authConfig?.STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_AND_DURATION_V1,
      });
    } else {
      headers = getAuthorizationHeaders({
        accept:
          authConfig?.STATISTICS_INDIVIDUAL_COUNT_GROUP_BY_ROLE_AND_DURATION_EMPLOYEE_V1,
      });
    }
    axios({
      method: "get",
      url: url,
      headers: headers,
    })
      .then((res) => {
        setSampleData(res?.data);
      })
      .catch((err) => console.log("Employees error", err));
  }, []);

  const handleBarClick = (event, chartContext, config) => {
    const clickedIndex = config.dataPointIndex;

    const clickedSeriesName = config.seriesIndex === 0 ? "SP" : "CHS";

    const queryParams = {};

    if (clickedIndex === 0) {
      queryParams.frequency = "daily";
    } else if (clickedIndex === 1) {
      queryParams.frequency = "monthly";
    } else if (clickedIndex === 2) {
      queryParams.frequency = "quarterly";
    } else if (clickedIndex === 3) {
      queryParams.frequency = "yearly";
    }

    if (user?.roleId !== authConfig?.superAdminRoleId) {
      queryParams.created = true;
    }

    router.push({
      pathname: `/${clickedSeriesName}`,
      query: queryParams,
    });
  };

  const chartOptions = {
    chart: {
      id: "onboarded-SPs-CHS-bar-chart",
      type: "bar",
      toolbar: {
        show: false,
      },
      events: {
        dataPointSelection: handleBarClick, // Add click event handler
      },
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: "50%",
        borderRadius: 3,
      },
    },
    xaxis: {
      categories: ["Daily", "Monthly", "Quarterly", "Yearly"], // Updated categories
    },
    yaxis: {
      title: {
        text: "No. of onboarded leads",
      },
    },
    colors: ["rgba(141, 223, 141, 1)", "rgba(16, 138, 0, 1)"], // Colors in RGBA
    dataLabels: {
      enabled: true,
      style: {
        colors: ["#000"],
        fontSize: "12px",
        fontWeight: "bold",
      },
      formatter: function (val) {
        return val; // Show the value inside the bars
      },
    },
    legend: {
      position: "top",
      horizontalAlign: "center",
    },
  };

  const combinedSPData = [
    sampleData?.spCount?.daily,
    sampleData?.spCount?.monthly,
    sampleData?.spCount?.quarterly,
    sampleData?.spCount?.yearly,
  ];

  const combinedCHSData = [
    sampleData?.chsCount?.daily,
    sampleData?.chsCount?.monthly,
    sampleData?.chsCount?.quarterly,
    sampleData?.chsCount?.yearly,
  ];

  return (
    <Card sx={{ p: 3 }}>
      <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
        <FaUserTie
          size={20}
          color="rgba(96, 194, 96, 1)"
          style={{ marginRight: "8px" }}
        />
        <Typography variant="h6" sx={{ fontWeight: "bold" }}>
          On boarded SP's and CHS's
        </Typography>
      </Box>

      <ApexChart
        options={chartOptions}
        series={[
          { name: "SP", data: combinedSPData },
          { name: "CHS", data: combinedCHSData },
        ]}
        type="bar"
        height={300}
      />

      <Box sx={{ mt: 2 }}>
        <Typography variant="body2" sx={{ mb: 1 }}>
          Yearly SP Data: {sampleData?.spCount?.yearly}
        </Typography>
        <Typography variant="body2">
          Yearly CHS Data: {sampleData?.chsCount?.yearly}
        </Typography>
      </Box>
    </Card>
  );
};

export default ServiceProviderGraph;
