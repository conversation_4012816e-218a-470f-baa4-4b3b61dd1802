// RBACProvider.js
import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import { PERMISSIONS } from 'src/constants';

const defaultProvider = {
  rbacRoles: null,
};

const RBACContext = createContext(defaultProvider);

export const useRBAC = () => {
  return useContext(RBACContext);
};

// Helper function to check permissions
const hasRequiredPermissions = (rolePermissions, requiredPermission) => 
  rolePermissions === PERMISSIONS.FULL_ACCESS || (rolePermissions & requiredPermission) === requiredPermission;

export const RBACProvider = ({ children }) => {
  const { user } = useContext(AuthContext);
  const [rbacRoles, setRbacRoles] = useState(user?.permissionsDTOList || []);

  // Update rbacRoles whenever user.permissionsDTOList changes
  useEffect(() => {
    if (user?.permissionsDTOList) {
      setRbacRoles(user.permissionsDTOList);
    }
  }, [user?.permissionsDTOList]);

  // Unified permission-checking function for both page and field levels
  const canAccess = useCallback((menuName, pageName, requiredPermission, fieldName = null) => {
    if (requiredPermission === 0) {
      console.log(`Access denied to ${pageName}${fieldName ? ` -> ${fieldName}` : ''} due to required permission being 0.`);
      return false;
    }

    // Check roles and permissions
    const hasPermission = rbacRoles.some(role => {
      const menuMatch = role.name === menuName;
      const pageMatch = role.children?.some(child => 
        child.name === pageName &&
        (!fieldName 
          ? hasRequiredPermissions(child.permissions, requiredPermission) 
          : child.children?.some(field => 
              field.name === fieldName &&
              hasRequiredPermissions(field.permissions, requiredPermission)
            )
        )
      );

      return menuMatch && pageMatch;
    });

    // Log permission check results
    // if (!hasPermission) {
    //   console.log(`Access denied: required ${requiredPermission} for ${menuName} -> ${pageName}${fieldName ? ` -> ${fieldName}` : ''}`);
    // } else {
    //   console.log(`Access granted: required ${requiredPermission} for ${menuName} -> ${pageName}${fieldName ? ` -> ${fieldName}` : ''}`);
    // }

    return hasPermission;
  }, [rbacRoles]);

  const canMenuPageSectionField = (menuName, pageName, sectionName, fieldName, requiredPermission) => {
    if (requiredPermission === 0) {
      console.log(`Access denied to ${menuName} -> ${pageName} -> ${sectionName} -> ${fieldName} due to required permission being 0.`);
      return false;
    }
  
    return rbacRoles.some(role => {
      if (role.name !== menuName) return false;
  
      const page = role.children?.find(p => p.name === pageName);
      if (!page) return false;
  
      const section = page.children?.find(s => s.name === sectionName);
      if (!section) return false;
  
      const field = section.children?.find(f => f.name === fieldName);
      if (!field) return false;
  
      return hasRequiredPermissions(field.permissions, requiredPermission);
    });
  };
  

  // Wrappers for different access levels
  const canMenuPage = (menuName, pageName, requiredPermission) => canAccess(menuName, pageName, requiredPermission);
  const canMenuPageSection = (menuName, pageName, fieldName, requiredPermission) => canAccess(menuName, pageName, requiredPermission, fieldName);

  // Expose values
  const values = useMemo(() => ({
    rbacRoles,
    canMenuPage,
    canMenuPageSection,
    canMenuPageSectionField,
  }), [rbacRoles, canAccess]);

  return (
    <RBACContext.Provider value={values}>
      {children}
    </RBACContext.Provider>
  );
};

export default RBACContext;
