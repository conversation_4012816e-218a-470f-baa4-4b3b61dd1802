import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import {
  Box,
  Typography,
  Paper,
  CircularProgress,
  Tabs,
  Tab,
  useTheme,
} from "@mui/material";

import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import axios from "axios";
import authConfig from "src/configs/auth";

// Dynamically import ApexCharts
const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

const TotalDonationsGrowthLineChart = () => {
  // Use theme from Material-UI context
  const theme = useTheme();

  // Define initial state for dynamic data
  const [title] = useState("Total Donations Growth");
  const [subtitle] = useState("Donation trends over different periods");
  const [selectedTab, setSelectedTab] = useState(0);
  const [series, setSeries] = useState([]);
  const [categories, setCategories] = useState([]); // Last 5 years
  const [currencySymbol] = useState("₹");
  const [loading, setLoading] = useState(false);

  const handleChange = (_, newValue) => {
    setSelectedTab(newValue);
  };

  // Placeholder for future GET endpoint integration
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      let freq =
        selectedTab === 0
          ? "YEARLY"
          : selectedTab === 1
          ? "MONTHLY"
          : selectedTab === 2
          ? "WEEKLY"
          : selectedTab === 3
          ? "DAILY"
          : "";
      try {
        const url = `${getUrl(
          authConfig.dashboardStatisticsEndpoint +
            "/donation-amount?graphType=" +
            freq
        )}`;

        const response = await axios({
          method: "get",
          url: url,
          headers: getAuthorizationHeaders(),
        });
        const data = response.data;
        const extractedSeries = data.map((item) => parseFloat(item.amount));
        const extractedLabels = data.map((item) => item.label);

        setSeries([{ name: "Donations", data: extractedSeries }]);
        setCategories(extractedLabels);
      } catch (error) {
        console.error("Error fetching donation growth data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedTab]); // Re-run when tab changes to fetch relevant data

  // Chart options
  const chartOptions = {
    chart: {
      type: "line",
      toolbar: { show: false },
      zoom: { enabled: false },
      foreColor: theme.palette.text.secondary,
    },
    colors: ["#1F1F7A"],
    dataLabels: {
      enabled: false,
    },
    stroke: {
      curve: "smooth",
      width: 4,
      lineCap: "round",
    },
    markers: {
      size: 6,
      colors: ["#1F1F7A"],
      strokeColors: "#fff",
      strokeWidth: 3,
      hover: {
        size: 8,
        sizeOffset: 3,
      },
    },
    xaxis: {
      categories: categories,
      axisBorder: { show: true, color: theme.palette.divider },
      axisTicks: { show: false },
      labels: {
        style: {
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
        },
        formatter: (value) => `${currencySymbol}${Math.floor(value)}`,
      },
    },
    grid: {
      borderColor: theme.palette.divider,
      strokeDashArray: 3,
      opacity: 0.5,
    },
    tooltip: {
      theme: theme.palette.mode,
      y: {
        formatter: (value) => `${currencySymbol}${Number(value).toFixed(2)}`,
        title: {
          formatter: (seriesName) => `${seriesName}:`,
        },
      },
    },
  };

  return (
    <Paper elevation={3} sx={{ p: 3, height: "100%" }}>
      <Typography variant="h5" gutterBottom>
        {title}
      </Typography>
      <Typography variant="body2" color="text.secondary" gutterBottom>
        {subtitle}
      </Typography>

      <Tabs value={selectedTab} onChange={handleChange} sx={{ mb: 2 }}>
        <Tab label="Yearly" />
        <Tab label="Monthly" />
        <Tab label="Weekly" />
        <Tab label="Daily" />
      </Tabs>

      <Box
        sx={{
          minHeight: 300,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {loading ? (
          <CircularProgress />
        ) : series.length > 0 && categories.length > 0 ? (
          <Chart
            options={chartOptions}
            series={series}
            type="line"
            height={300}
            width="200%"
          />
        ) : (
          <Typography color="error">No data available</Typography>
        )}
      </Box>
    </Paper>
  );
};

export default TotalDonationsGrowthLineChart;
