import {
  Box,
  Card,
  CardContent,
  DialogContentText,
  Divider,
  InputAdornment,
  Menu,
  MenuItem,
  Tooltip,
} from "@mui/material";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogActions from "@mui/material/DialogActions";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import IconButton from "@mui/material/IconButton";
import TextField from "@mui/material/TextField";
import Typography from "@mui/material/Typography";
import { DataGrid } from "@mui/x-data-grid";
import axios from "axios";
import SearchIcon from "@mui/icons-material/Search";
import { useContext, useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";
import authConfig from "src/configs/auth";
import { AuthContext } from "src/context/AuthContext";
import { getAuthorizationHeaders, getUrl } from "src/helpers/utils";
import { useAuth } from "src/hooks/useAuth";
import CustomChip from "src/@core/components/mui/chip";
import NameTextField from "src/@core/components/custom-components/NameTextField";
import { format } from "date-fns";
import FallbackSpinner from "src/@core/components/spinner";
import DeleteDialog from "./DeleteDialog";

const userStatusObj = {
  true: "Active",
  false: "InActive",
};

const States = () => {
  const [namesList, setNamesList] = useState([]);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDialogContent, setOpenDialogContent] = useState(false);

  const [openDialog, setOpenDialog] = useState(false);
  const auth = useAuth();

  const [loading, setLoading] = useState(true);
  const [keyword, setKeyword] = useState("");
  const [searchKeyword, setSearchKeyword] = useState("");
  const [expanded, setExpanded] = useState(true);
  const rowsPerPageOptions = [10, 15, 20, 25, 50, 100];
  const [pageSize, setPageSize] = useState(rowsPerPageOptions[0]);
  const [page, setPage] = useState(1);
  const [currentRow, setCurrentRow] = useState();
  const [rowCount, setRowCount] = useState(0);
  const [dialogMessage, setDialogMessage] = useState("");
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);

  const {
    handleSubmit,
    setValue,
    control,
    reset,
    formState: { errors },
  } = useForm({
    defaultValues: {
      name: "",
    },
  });
  async function submit(data) {
    const fields = {
      name: data?.name.trim(),
      listNamesId: authConfig.stateListNameId,
    };
    try {
      const response = await auth.postService(
        fields,
        handleFailure,
        handleSuccess
      );
      reset();
    } catch (error) {
      console.error("State name Creation failed:", error);
      handleFailure();
    }

    setOpenDialog(false);
    reset();
    fetchNames(page, pageSize, searchKeyword);
  }

  async function update(data) {
    const fields = {
      id: currentRow.id,
      name: data.state.trim(),
      listNameId: authConfig.stateListNameId,
      isActive: currentRow?.isActive,
    };

    const response = await auth.patchServicesData(fields, () => {
      console.error(" Master Data Details failed");
    });


    fetchNames(page, pageSize,searchKeyword);

    handleCloseDialog();
  }

  const handleSuccess = () => {
    const message = `
      <div> 
        <h3> State Name added Successfully.</h3>
      </div>
    `;
    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleFailure = (err) => {
    const data = err.response?.data;
    let message;
    if (data.message?.includes("existing entry")) {
      message = `
      <div> 
        <h3>State already exists with given name. Please try another name </h3>
      </div>
    `;
    } else {
      message = `
      <div> 
        <h3> Failed to Add State name. Please try again later.</h3>
      </div>
    `;
    }

    setDialogMessage(message);
    setOpenDialogContent(true);
  };

  const handleButtonClick = () => {
    setOpenDialogContent(false);
  };

  const handleCloseDialog = () => {
    reset();
    setOpenDialog(false);
    fetchNames(page, pageSize, searchKeyword);
  };

  const fetchNames = async (currentPage, currentPageSize, searchKeyword) => {
    const url =
      getUrl(authConfig.listValuesAll) + "/" + authConfig.stateListNameId;

    const headers = getAuthorizationHeaders();

    const data = {
      page: currentPage,
      pageSize: currentPageSize,
      searchKeyword: searchKeyword,
    };

    try {
      const response = await axios({
        method: "post",
        url: url,
        headers: headers,
        data: data,
      });

      if (response.data) {
        setNamesList(response.data?.listValuesResponse);
        setRowCount(response.data?.rowCount || 0);
      } else {
        console.error("Unexpected API response format:", response);
      }
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setOpenDeleteDialog(false);
    fetchNames(page, pageSize, searchKeyword);
  };

  useEffect(() => {
    fetchNames(page, pageSize, searchKeyword);
  }, [page, pageSize, searchKeyword]);

  const handlePageChange = (direction) => {
    if (direction === page) {
      setPage(page + 1);
    } else {
      setPage(page - 1);
    }
  };

  const handlePageSizeChange = (params) => {
    if (params) {
      setPageSize(params);
      setPage(1);
    }
  };

  const handleCloseMenuItems = () => {
    setMenuAnchorEl(null);
  };

  const handleMenuOpen = (event, row) => {
    setMenuAnchorEl(event.currentTarget);
    setCurrentRow(row);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const mapIsActiveToLabel = (isActive) => {
    return userStatusObj[isActive] || "Unknown";
  };

  const columns = [
    { field: "name", headerName: "Name", flex: 0.15, minWidth: 120 },
    {
      field: "isActive",
      headerName: "Status",
      flex: 0.05,
      minWidth: 100,
      renderCell: ({ row }) => {
        return (
          <CustomChip
            rounded={true}
            skin="light"
            size="small"
            label={mapIsActiveToLabel(row.isActive)}
            color={row.isActive === true ? "success" : "error"}
            sx={{ textTransform: "capitalize" }}
          />
        );
      },
    },
    {
      field: "createdOn",
      headerName: "Created on",
      flex: 0.07,
      minWidth: 100,
      renderCell: ({ row }) => {
        return format(new Date(row.createdOn), "dd-MM-yyyy");
      },
    },
    {
      field: "updatedOn",
      headerName: "Updated on",
      flex: 0.07,
      minWidth: 100,
      renderCell: ({ row }) => {
        return format(new Date(row.updatedOn), "dd-MM-yyyy");
      },
    },
    {
      field: "createdBy",
      headerName: "Created By",
      flex: 0.11,
      minWidth: 100
    },
    {
      field: "updatedBy",
      headerName: "Updated By",
      flex: 0.11,
      minWidth: 100
    },
    {
      field: "actions",
      headerName: "Actions",
      flex: 0.04,
      minWidth: 100,
      renderCell: (params) => {
        const onClickViewProfile = () => {
          setOpenDialog(true);
          setValue("state", currentRow?.name);
          handleCloseMenuItems();
        };
        const onClickToggleStatus = () => {
          setOpenDeleteDialog(true);
          handleCloseMenuItems(); // Close menu after action
        };
        return (
          <>
            <IconButton onClick={(event) => handleMenuOpen(event, params.row)}>
              <Icon icon="bi:three-dots-vertical" />
            </IconButton>
            <Menu
              anchorEl={menuAnchorEl}
              open={Boolean(menuAnchorEl)}
              onClose={handleMenuClose}
            >
              <MenuItem onClick={onClickViewProfile}>Edit</MenuItem>
              <MenuItem onClick={onClickToggleStatus}>
                {currentRow?.isActive ? "Deactivate" : "Activate"}
              </MenuItem>
            </Menu>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Grid>
        <Box
          sx={{
            py: 3,
            px: 6,
            rowGap: 2,
            columnGap: 4,
            display: "flex",
            flexWrap: "wrap",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={4} sx={{ textAlign: "flex-start" }}>
              <Typography variant="h6">{"List of States"}</Typography>
            </Grid>

            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                justifyContent="flex-end"
              >
                  <Grid item xs={12} sm={7} md={4} lg={4}>
                    <FormControl fullWidth>
                      <Controller
                        name="mainSearch"
                        control={control}
                        render={({ field: { onChange } }) => (
                          <TextField
                            id="mainSearch"
                            size="small"
                            placeholder="Search by state name"
                            value={keyword}
                            onChange={(e) => {
                              onChange(e.target.value);
                              setKeyword(e.target.value);
                              setSearchKeyword(e.target.value);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === "Enter") {
                                setSearchKeyword(keyword);
                                fetchNames(page, pageSize, searchKeyword);
                              }
                            }}
                            InputProps={{
                              endAdornment: (
                                <InputAdornment position="start">
                                  <SearchIcon
                                    sx={{
                                      cursor: "pointer",
                                      marginRight: "-15px",
                                    }}
                                    onClick={() => {
                                      setSearchKeyword(keyword);
                                      fetchNames(page, pageSize, searchKeyword);
                                    }}
                                  />{" "}
                                </InputAdornment>
                              ),
                            }}
                          />
                        )}
                      />
                    </FormControl>
                  </Grid>
                <Grid item xs="auto" sm={5}>
                  <FormControl fullWidth>
                    <Controller
                      name="name"
                      control={control}
                      rules={{ required: openDialog ? false : "state is required" }}
                      render={({ field }) => (
                        <NameTextField
                          {...field}
                          size="small"
                          InputLabelProps={{ shrink: true }}
                          placeholder={"Enter state to add"}
                          error={Boolean(errors.name)}
                          helperText={errors.name?.message}
                          aria-describedby="Section1-name"
                        />
                      )}
                    />
                  </FormControl>
                </Grid>
                <Grid item xs="auto" sm="auto" onClick={handleSubmit(submit)}>
                  <Button variant="contained">Add State</Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Box>
        <Divider />
        <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth={"md"}>
          <DialogTitle
            sx={{
              position: "relative",
              borderBottom: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(1.75, 4)} !important`,
              display: "flex",
              alignItems: "center",
              justifyContent: "start",
              fontSize: { xs: 15, md: 21 },
            }}
            textAlign={"center"}
          >
            Edit State
            <Box sx={{ position: "absolute", top: "4px", right: "12px" }}>
              <IconButton
                size="small"
                onClick={handleCloseDialog}
                sx={{
                  // p: "0.438rem",
                  borderRadius: 1,
                  color: "common.white",
                  backgroundColor: "primary.main",
                  "&:hover": {
                    backgroundColor: "#66BB6A",
                    transition: "background 0.5s ease, transform 0.5s ease",
                  },
                }}
              >
                <Icon icon="tabler:x" fontSize="1rem" />
              </IconButton>
            </Box>
          </DialogTitle>
          <DialogContent
            sx={{
              position: "relative",
              pt: (theme) => `${theme.spacing(8)} !important`,
              pb: (theme) => `${theme.spacing(5)} !important`,
              px: (theme) => [`${theme.spacing(8)} !important`],
            }}
          >
            <Grid item xs="auto" sm={5}>
              <FormControl fullWidth>
                <Controller
                  name="state"
                  control={control}
                  rules={{ required: false }}
                  render={({ field }) => (
                    <NameTextField
                      {...field}
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      placeholder={"Enter state to add"}
                      error={Boolean(errors.state)}
                      helperText={errors.state?.message}
                      aria-describedby="Section1-state"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </DialogContent>
          <DialogActions
            sx={{
              justifyContent: "center",
              borderTop: (theme) => `1px solid ${theme.palette.divider}`,
              p: (theme) => `${theme.spacing(2.5)} !important`,
            }}
          >
            <Button
              display="flex"
              justifyContent="center"
              variant="outlined"
              color="primary"
              onClick={handleCloseDialog}
            >
              Close
            </Button>
            <Button
              display="flex"
              justifyContent="center"
              variant="contained"
              color="primary"
              onClick={handleSubmit(update)}
            >
              Update
            </Button>
          </DialogActions>
        </Dialog>

        <CardContent>
          <div style={{ height: 380, width: "100%", overflow: "auto" }}>
            {loading ? (
              <Box
                display="flex"
                justifyContent="center"
                alignItems="center"
                height="60vh"
              >
                <FallbackSpinner />
              </Box>
            ) : (
              <DataGrid
                rows={namesList}
                columns={columns}
                pagination
                pageSize={pageSize}
                page={page - 1}
                rowsPerPageOptions={rowsPerPageOptions}
                rowCount={rowCount}
                paginationMode="server"
                onPageChange={handlePageChange}
                onPageSizeChange={handlePageSizeChange}
                rowHeight={38}
                headerHeight={38}
              />
            )}
          </div>
        </CardContent>

        <DeleteDialog
          open={openDeleteDialog}
          onClose={handleCloseDeleteDialog}
          data={currentRow}
        />
      </Grid>

      <Dialog
        open={openDialogContent}
        onClose={handleButtonClick}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            p: (theme) => `${theme.spacing(2.5)} !important`,
            backgroundColor: (theme) => theme.palette.primary.background,
          },
        }}
      >
        <Box
          sx={{
            width: "100%",
            borderRadius: 1,
            textAlign: "center",
            border: (theme) => `1px solid ${theme.palette.divider}`,
            borderColor: "primary.main",
          }}
        >
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              color="primary.main"
            >
              <div dangerouslySetInnerHTML={{ __html: dialogMessage }} />
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              onClick={handleButtonClick}
              sx={{ margin: "auto", width: 100 }}
            >
              Okay
            </Button>
          </DialogActions>
        </Box>
      </Dialog>
    </>
  );
};

export default States;
