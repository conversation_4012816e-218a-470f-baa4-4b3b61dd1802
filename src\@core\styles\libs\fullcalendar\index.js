// ** MUI imports
import { styled } from '@mui/material/styles'
import Box from '@mui/material/Box'
import useMediaQuery from '@mui/material/useMediaQuery'

// ** Hooks Imports
import useBgColor from 'src/@core/hooks/useBgColor'

// ** utilities
import { hexToRGBA } from 'src/@core/utils/hex-to-rgba'

const CalendarWrapper = styled(Box)(({ theme }) => {
  // ** Hook
  const bgColors = useBgColor();
  const isXSDown = useMediaQuery(theme.breakpoints.down('xs'));
  const isMDDown = useMediaQuery(theme.breakpoints.down('md'));

  return {
    display: 'flex',
    position: 'relative',
    borderRadius: theme.shape.borderRadius,
    paddingBottom:'0 !important',
    '& .fc': {
      zIndex: 1,
  // ** Override the min-height style
  '& .fc-daygrid-body-unbalanced .fc-daygrid-day-events': {
    [theme.breakpoints.down('sm')]: {
      minHeight: 'unset !important', // Remove min-height for XS view
    },
  },
      // ** Toolbar
      '& .fc-toolbar': {
        flexWrap: 'wrap',
        flexDirection: 'row !important',
        '&.fc-header-toolbar': {
          marginBottom: theme.spacing(3), // Reduced margin for smaller overall size
        },
        '.fc-prev-button, & .fc-next-button': {
          display: 'inline-block',
          borderColor: 'transparent',
          backgroundColor: 'transparent',
          '& .fc-icon': {
            color: theme.palette.text.secondary,
            fontSize: theme.typography.h5.fontSize, // Reduced icon size
          },
          '&:hover, &:active, &:focus': {
            boxShadow: 'none !important',
            borderColor: 'transparent !important',
            backgroundColor: 'transparent !important',
          },
        },
        '& .fc-prev-button': {
          paddingLeft: '0 !important',
          marginRight: theme.spacing(1),
        },
        '& .fc-toolbar-chunk:first-of-type': {
          display: 'flex',
          flexWrap: 'wrap',
          alignItems: 'center',
          ...(isMDDown && {
            '& div:first-of-type': {
              display: 'flex',
              alignItems: 'center',
            },
          }),
        },
        '& .fc-button': {
          padding: theme.spacing(0.5), // Reduced padding for buttons
          '&:active, .&:focus': {
            boxShadow: 'none',
          },
        },
        '& .fc-button-group': {
          '& .fc-button': {
            textTransform: 'capitalize',
            '&:focus': {
              boxShadow: 'none',
            },
          },
          '& .fc-button-primary': {
            '&:not(.fc-prev-button):not(.fc-next-button):not(.fc-sidebarToggle-button)': {
              padding: theme.spacing(1, 4), // Reduced padding for primary buttons
              color: theme.palette.primary.main,
              borderColor: hexToRGBA(theme.palette.primary.main, 0.24),
              backgroundColor: hexToRGBA(theme.palette.primary.main, 0.16),
              '&.fc-button-active, &:hover': {
                backgroundColor: hexToRGBA(theme.palette.primary.main, 0.24),
              },
            },
          },
          '& .fc-sidebarToggle-button': {
            border: 0,
            lineHeight: 0.8,
            borderColor: 'transparent',
            paddingBottom: '0 !important',
            backgroundColor: 'transparent',
            color: theme.palette.text.secondary,
            marginLeft: `${theme.spacing(-2)} !important`,
            padding: `${theme.spacing(1, 2)} !important`, // Reduced padding
            '&:focus': {
              outline: 0,
              boxShadow: 'none',
            },
            '&:not(.fc-prev-button):not(.fc-next-button):hover': {
              backgroundColor: 'transparent !important',
            },
            '& + div': {
              marginLeft: 0,
            },
          },
          '.fc-dayGridMonth-button, .fc-timeGridWeek-button, .fc-timeGridDay-button, & .fc-listMonth-button': {
            padding: theme.spacing(1.5, 4),
            fontSize: '0.8rem !important', // Reduced padding
            [theme.breakpoints.down('sm')]: {
              fontSize: '0.67em !important',
              padding: theme.spacing(2.5, 5),

            },
            '&:last-of-type, &:first-of-type': {
              borderRadius: theme.shape.borderRadius,
            },
            '&:first-of-type': {
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
            },
            '&:last-of-type': {
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
            },
          },
        },
        '& > * > :not(:first-of-type)': {
          marginLeft: 0,
        },
        '& .fc-toolbar-title': {
          fontWeight: 500,
          marginRight: theme.spacing(2),
          marginLeft: theme.spacing(2),
          fontSize: theme.typography.h6.fontSize,
        },
        '.fc-button:empty:not(.fc-sidebarToggle-button), & .fc-toolbar-chunk:empty': {
          display: 'none',
        },
      },

      // ** Calendar head & body common
      '& tbody td, & thead th': {
        borderColor: theme.palette.divider,
        fontSize: '0.75rem !important',
        [theme.breakpoints.down('sm')]: {
          fontSize: '0.6rem !important',
        },
        '&.fc-col-header-cell': {
          borderLeft: 0,
          fontSize: '0.9rem', // Reduced font size
          padding: theme.spacing(0.5), // Reduced padding
          ...(isXSDown && {
            fontSize: '0.7rem !important', // Font size for xs screens
            padding: theme.spacing(0.25), // Reduced padding for xs screens
          }),
        },
        '&[role="presentation"]': {
          borderRightWidth: 0,
        },
      },

      // ** Event Colors
      '& .fc-event': {
        marginTop: 0,
        fontWeight: 500,
        marginBottom: 0,
        borderRadius: 4,
        fontSize: '0.6rem', // Reduced font size
        padding: theme.spacing(0.2, 1), // Reduced padding
        whiteSpace: 'nowrap', // Prevent text from wrapping inside the event
        overflow: 'hidden', // Ensure event content is contained
        textOverflow: 'ellipsis', // Handle overflow with ellipsis if needed
        display: 'flex', // Use flexbox to maintain layout
        flexDirection: 'column',        // gap:'2rem',
        '& .fc-event-time': {
          display: 'none', // Hide the event time
        },
      },
      '& .fc-daygrid-event-harness': {
        display: 'block', // Ensure the event harness is displayed as block
        width: '100%', // Ensure it takes the full width of the cell
        maxWidth: '100%', // Ensure it doesn't overflow the cell
        '&:not(:last-of-type)': {
          marginBottom: theme.spacing(1),
        },
      },
      '& .fc-daygrid-day-bottom': {
        marginTop: theme.spacing(0.5),
      },
      '& .fc-daygrid-day': {
        padding: '2px', // Reduced padding
        overflow: 'hidden', // Ensure content is contained within the cell
        '& .fc-daygrid-day-top': {
          flexDirection: 'row',
        },
        ...(isXSDown && {
          width: '14.28%', // Adjust the width to fit within the screen for xs
          padding: '2px', // Reduced padding for xs screens
        }),
      },

      // ** All Views Event
      '& .fc-daygrid-day-number': {
        marginBottom: theme.spacing(1),
        padding: theme.spacing(0, 1),
        ...(isXSDown && {
          fontSize: '0.7rem !important', // Adjust font size for day numbers in xs
        }),
      },
      '& .fc-daygrid-day-number, & .fc-timegrid-slot-label-cushion, & .fc-list-event-time': {
        textDecoration: 'none !important',
        color: `${theme.palette.text.primary} !important`,
      },
      '& .fc-day-today:not(.fc-popover):not(.fc-col-header-cell)': {
        backgroundColor: theme.palette.action.selected,
      },

      // ** WeekView
      '& .fc-timegrid': {
        '& .fc-scrollgrid-section': {
          height: 0,
          '& .fc-col-header-cell, & .fc-timegrid-axis': {
            borderLeft: 0,
            borderColor: theme.palette.divider,
          },
          '& .fc-timegrid-axis': {
            borderColor: theme.palette.divider,
          },
        },
        '& .fc-timegrid-axis': {
          '&.fc-scrollgrid-shrink': {
            '& .fc-timegrid-axis-cushion': {
              fontSize: '.6rem', // Reduced font size
              textTransform: 'lowercase',
              color: theme.palette.text.disabled,
            },
          },
        },
        '& .fc-timegrid-slots': {
          '& .fc-timegrid-slot': {
            height: '1rem', // Reduced height
            borderColor: theme.palette.divider,
            '&.fc-timegrid-slot-label': {
              borderRight: 0,
            },
            '&.fc-timegrid-slot-lane': {
              borderLeft: 0,
            },
            '& .fc-timegrid-slot-label-frame': {
              textAlign: 'center',
              '& .fc-timegrid-slot-label-cushion': {
                fontSize: '0.6rem', // Reduced font size
                textTransform: 'lowercase',
                color: `${theme.palette.text.secondary} !important`,
              },
            },
          },
        },
        '& .fc-timegrid-divider': {
          display: 'none',
        },
        '& .fc-timegrid-event': {
          boxShadow: 'none',
        },
      },

      // ** List View
      '& .fc-list': {
        border: 'none',
        '& th[colspan="3"]': {
          position: 'relative',
        },
        '& .fc-list-day-cushion': {
          background: theme.palette.action.hover,
        },
        '.fc-list-event': {
          cursor: 'pointer',
          '&:hover': {
            '& td': {
              backgroundColor: theme.palette.action.hover,
            },
          },
          '& td': {
            borderColor: theme.palette.divider,
          },
        },
        '& .fc-list-day': {
          backgroundColor: theme.palette.action.hover,
          '& .fc-list-day-text, & .fc-list-day-side-text': {
            fontWeight: 500,
            fontSize: '0.9rem', // Reduced font size
            textDecoration: 'none',
          },
          '&  >  *': {
            background: 'none',
            borderColor: theme.palette.divider,
          },
        },
        '& .fc-list-event-title': {
          fontSize: '0.9rem', // Reduced font size
          paddingLeft: theme.spacing(1.5),
          color: theme.palette.text.primary,
        },
        '& .fc-list-event-time': {
          fontSize: '0.9rem', // Reduced font size
          color: theme.palette.text.disabled,
        },
      },

      // ** Popover
      '& .fc-popover': {
        zIndex: 20,
        boxShadow: 1,
        borderColor: theme.palette.divider,
        background: theme.palette.background.paper,
        '& .fc-popover-header': {
          padding: theme.spacing(1.5), // Reduced padding
          background: theme.palette.action.hover,
          '& .fc-popover-title, & .fc-popover-close': {
            color: theme.palette.text.primary,
          },
        },
        '& .fc-popover-body': {
          '& *:not(.fc-event-main):not(:last-of-type)': {
            marginBottom: theme.spacing(0.5), // Reduced margin
          },
        },
      },

      // ** Media Queries
      [theme.breakpoints.up('md')]: {
        '& .fc-sidebarToggle-button': {
          display: 'none',
        },
        '& .fc-toolbar-title': {
          marginLeft: 0,
        },
      },
      [theme.breakpoints.down('xs')]: {
        '& .fc-col-header-cell': {
          fontSize: '0.7rem !important', // Font size for xs screens
          padding: theme.spacing(0.25), // Reduced padding for xs screens
        },
        '& .fc-toolbar-title': {
          fontSize: '0.9rem', // Adjust font size of the toolbar title for xs
        },
      },
      '@media (max-width:610px)': {
        '& .fc-header-toolbar .fc-toolbar-chunk:last-of-type': {
          marginTop: theme.spacing(2),
        },
      },
    },
  };
});

export default CalendarWrapper;
