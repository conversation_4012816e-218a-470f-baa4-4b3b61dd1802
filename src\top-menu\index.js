import { useContext, useMemo } from 'react';
import { AuthContext } from 'src/context/AuthContext';
import StaticTopMenu from 'src/utils/StaticTopMenu';

const ALLOWED_PERMISSIONS_SET = new Set([1, 3, 5, 7, 15]);
const MODULE_TYPE = "MODULE";
const PAGE_TYPE = "PAGE";
const TOP_MENU_NAME = "Top_Menu";

// Preprocess Static Top Menu for faster lookup
const topMenuMap = new Map(StaticTopMenu.map(item => [item?.name, item]));

const TopNavigation = () => {
  const { user } = useContext(AuthContext);

  const permissionsList = useMemo(() => user?.permissionsDTOList || [], [user?.permissionsDTOList]);

  const filterPages = (children, parentName) => {
    const parentStaticItem = topMenuMap.get(parentName);
    if (!parentStaticItem?.children) return null;

    const filteredPages = children
      .filter(child => child?.type === PAGE_TYPE && ALLOWED_PERMISSIONS_SET.has(child?.permissions))
      .map(child => {
        const staticPageItem = parentStaticItem.children?.find(item => item?.name === child?.name);
        if (!staticPageItem) return null;

        const pageData = {
          title: staticPageItem?.title,
          path: staticPageItem?.path,
          icon: staticPageItem?.icon,
          displayNumber: child?.displayNumber || 0,
          permissions: child?.permissions,
          children: child?.children ? filterPages(child.children, child.name) : null,
        };
        
        // Log each page data
        console.log("Filtered Page Data:", pageData);
        
        return pageData;
      })
      .filter(Boolean)
      .sort((a, b) => (a?.displayNumber || 0) - (b?.displayNumber || 0));

    console.log("Filtered Pages Array:", filteredPages); // Log filtered pages array

    return filteredPages.length > 0 ? filteredPages : null;
  };

  const topNavArray = useMemo(() => {
    const topMenu = permissionsList?.find(item => item?.name === TOP_MENU_NAME);
    if (!topMenu?.children) return [];

    const moduleArray = topMenu.children
      .filter(child => child?.type === MODULE_TYPE && ALLOWED_PERMISSIONS_SET.has(child?.permissions))
      .map(child => {
        const staticModuleItem = topMenuMap.get(child?.name);
        if (!staticModuleItem) return null;

        const moduleData = {
          title: staticModuleItem?.title,
          path: staticModuleItem?.path,
          icon: staticModuleItem?.icon,
          displayNumber: child?.displayNumber || 0,
          permissions: child?.permissions,
          children: child?.children ? filterPages(child.children, child.name) : null,
        };
        
        // Log each module data
        console.log("Filtered Module Data:", moduleData);
        
        return moduleData;
      })
      .filter(Boolean)
      .sort((a, b) => (a?.displayNumber || 0) - (b?.displayNumber || 0));

    console.log("Top Navigation Array:", moduleArray); // Log the final top navigation array

    return moduleArray;
  }, [permissionsList]);

  return topNavArray;
};

export default TopNavigation;
