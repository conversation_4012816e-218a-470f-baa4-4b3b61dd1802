import { FormControl, Grid, TextField } from "@mui/material";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import { styled } from "@mui/material/styles";
import Tooltip from "@mui/material/Tooltip";
import Typography from "@mui/material/Typography";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import PerfectScrollbar from "react-perfect-scrollbar";
import SelectClearAutoComplete from "src/@core/components/custom-components/SelectClearAutoComplete";
import Icon from "src/@core/components/icon";
import CustomAvatar from "src/@core/components/mui/avatar";

const Header = styled(Box)(({ theme }) => ({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  padding: theme.spacing(3),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const AdvancedSearch = (props) => {
  const {
    open,
    toggle,
    selectedFilters,
    setSearchingState,
    clearAllFilters,
    onApplyFilters,
   
  } = props;

  const {
    setValue,
    control,
    reset,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const [selectRoleId, setSelectRoleId] = useState("");

  useEffect(() => {
    // Create a map of filter keys for easy lookup
    const filterMap = new Map(selectedFilters?.map((filter) => [filter.key, filter.value]));

    // Set values based on filters or clear them if the filter is not present
    setValue("name", filterMap.get("nameFilter") || "");
    setValue("description", filterMap.get("descriptionFilter") || "");
    setSelectRoleId(filterMap.get("parentRoleFilter") || null);
  }, [selectedFilters, setValue]);

  const handleCancel = () => {
    reset();
    setSelectRoleId("");
    setSearchingState(false);
    clearAllFilters();
    
  };

  const handleClose = () => {
    toggle();
  };

  const handleApply = (data) => {
    // Prepare selected filters for chips
    const filters = [];
    if (data?.name) {
      filters.push({ key: "nameFilter", label: "Name", value: data?.name });
    }
    if (data?.description) {
      filters.push({ key: "descriptionFilter", label: "Description", value: data?.description });
    }
    if (selectRoleId) {
      filters.push({ key: "parentRoleFilter", label: "Role", value: selectRoleId });
    }

    // Call the parent callback with selected filters
    onApplyFilters(filters);
    setSearchingState(true); // Update searching state in parent if needed
    toggle(); // Close the drawer
  };

  return (
    <>
      <Tooltip title="Advanced Search">
        <CustomAvatar
          variant="rounded"
          sx={{ width: 36, height: 36, cursor: "pointer" }}
          onClick={toggle}
        >
          <Icon icon="tabler:filter" fontSize={27} />
        </CustomAvatar>
      </Tooltip>

      <Drawer
        open={open}
        anchor="right"
        variant="temporary"
        onClose={handleClose}
        ModalProps={{ keepMounted: true }}
        sx={{ "& .MuiDrawer-paper": { width: { xs: "85%", sm: 500 } } }}
      >
        <Header 
          sx={{position: "relative", 
            display: "flex",
            alignItems: "center", 
            justifyContent: "space-between"
           }}
           >
          <Typography variant="h5" sx={{
            ml:{
              xs :3,
              sm:3,

              xl :3
            }}}
            
            
            > Advanced Search&nbsp;   </Typography>
          <Box sx={{ position: "absolute", top: "8px", right: "14px", 
              mt:{
                xs:1,
              xl:1.5,
              lg:1.5,
              md:1.5,
              sm:1
            },
              mr:{
                xs:2.5,
                xl:2.5,
                lg:2.5,
                md:2.5,
                sm:2.5
              },
            

           }}>
            <IconButton
              size="small"
              onClick={handleClose}
              sx={{
                borderRadius: 1,
                color: "common.white",
                backgroundColor: "primary.main",
                "&:hover": {
                  backgroundColor: "#66BB6A",
                  transition: "background 0.5s ease, transform 0.5s ease",
                },
              }}
            >
              <Icon icon="tabler:x" fontSize="1rem" />
            </IconButton>
          </Box>
        </Header>

        <PerfectScrollbar options={{ wheelPropagation: false }}>
          <Box sx={{ p: (theme) => theme.spacing(4, 6) }}>
            <Grid container spacing={3} alignItems={"center"}>
              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="name"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Name"
                        placeholder="Search By Name"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="userRole"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="description"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <TextField
                        {...field}
                        size="small"
                        label=" Search by Description"
                        placeholder="Search By Description"
                        InputLabelProps={{ shrink: true }}
                        aria-describedby="name"
                        value={field.value}
                      />
                    )}
                  />
                </FormControl>
              </Grid>

              {/* <Grid item xs={12} md={12}>
                <FormControl fullWidth>
                  <Controller
                    name="roleId"
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <SelectClearAutoComplete
                        size="small"
                        id="roleId"
                        label="Select Parent Role"
                        nameArray={roles}
                        value={selectRoleId}
                        onChange={(event) => {
                          field.onChange(event.target.value);
                          setSelectRoleId(event.target.value);
                        }}
                      />
                    )}
                  />
                </FormControl>
              </Grid> */}
              {/* TO DO ADD Org dropdown for filter */}
            </Grid>
          </Box>
        </PerfectScrollbar>

        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme.palette.divider}`,
            p: (theme) => theme.spacing(2),
            justifyContent: "flex-end",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Button variant="tonal" sx={{ mr: 3 }} onClick={handleCancel}>
            Clear All
          </Button>
          <Button variant="contained" onClick={handleSubmit(handleApply)} sx={{ mr: {
                xs: 4, 
                sm: 4, 
                md: 4, 
                lg: 4, 
                xl: 4,
              },} }>
            Apply
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default AdvancedSearch;
