// ** Config
import authConfig from "src/configs/auth";

export function returnEntity(response, entityCategory) {
  switch (entityCategory) {
    case "ARCHITECT":
      return response;
    case "PLUMBER":
      return response;
    case "SOCIETY":
      return response;
    case "STRUCTURAL_ENGINEER":
      return response;
    case "PMC":
      return response;
    case "BROKER":
      return response;
    case "CHARTERED_ACCOUNTANT":
      return response;
    case "LEGAL":
      return response;
    default:
      console.error("UnKnown Category at returnEntity", entityCategory);
      return null;
  }
}

export function getUrl(endpoint) {
  return authConfig.baseURL + endpoint;
}

export function getAuthorizationHeaders({
  contentType = "application/json",
  accept = null
} = {}) {

  const headers = {
    "Content-Type": contentType,
    "Authorization": "Bearer " + window.localStorage.getItem(authConfig.storageTokenKeyName),
  };

  // Only add the "Accept" header if the accept parameter is provided
  if (accept) {
    headers["Accept"] = accept;
  }

  return headers;
}

export function getFileUploadHeaders({
  contentType = "multipart/form-data",
  accept = null
} = {}) {

  const headers = {
    "Content-Type": contentType,
    "Authorization": "Bearer " + window.localStorage.getItem(authConfig.storageTokenKeyName),
  };

  if (accept) {
    headers["Accept"] = accept;
  }
   return headers;
}


export function getFileUploadPDFHeaders({
  contentType = null,
  accept = "multipart/form-data"
} = {}) {

  const headers = {
    "Accept":accept,
    "Authorization": "Bearer " + window.localStorage.getItem(authConfig.storageTokenKeyName),
  };

  if (contentType) {
    headers["Content-Type"] = contentType;
  }
   return headers;
}
