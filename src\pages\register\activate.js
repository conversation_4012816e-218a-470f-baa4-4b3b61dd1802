// ** MUI Imports
import { useTheme } from '@mui/material/styles'
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'

import BlankLayout from "src/@core/layouts/BlankLayout";
import { Typography } from '@mui/material';

const ActivationPage = ({ sx }) => {
  // ** Hook
  const theme = useTheme()

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        alignItems: 'center',
        flexDirection: 'column',
        justifyContent: 'top',
        marginTop: '32px',
        textAlign: 'center',
        ...sx
      }}
    >
      <Typography sx={{ fontSize: '1.25rem', color: '#1f1f7a', mb: 4 }}>
        Email has been sent to your email address.<br/> Please check your inbox and click the link to activate your account.
      </Typography>
      <img width={72} height={58.375} alt='' src='/images/donation-reciept/logo-1.webp' className=''/>
    </Box>
  )
}

ActivationPage.getLayout = (page) => <BlankLayout>{page}</BlankLayout>;
ActivationPage.guestGuard = true;
export default ActivationPage
